<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>spring-cloud-shuidi-parent</artifactId>
        <groupId>com.shuidihuzhu.infra</groupId>
        <version>3.1.42</version>
    </parent>

    <groupId>com.shuidihuzhu.cf</groupId>
    <artifactId>cf-finance-toc-api</artifactId>
    <version>0.0.6-SNAPSHOT</version>

    <properties>
        <boot-image.tag>2</boot-image.tag>
        <java.version>11</java.version>
        <cf-enhancer-starter.verison>1.0.96</cf-enhancer-starter.verison>
        <pay-rpc-client.version>3.0.2</pay-rpc-client.version>
        <charity-rpc-client.version>0.0.113</charity-rpc-client.version>
        <cf.version>3.6.640</cf.version>
        <cf-client.version>1.2.426</cf-client.version>
        <cf-client-base.version>9.0.104</cf-client-base.version>
        <cf-finance-client.version>3.0.54</cf-finance-client.version>
        <cf-admin-api-pure-client.version>9.0.96</cf-admin-api-pure-client.version>
        <cos-starter.version>0.0.5</cos-starter.version>
        <sharding-jdbc-core.version>2.0.3</sharding-jdbc-core.version>
    </properties>

    <scm>
        <connection>scm:git:http://git.shuiditech.com/cf/cf-finance-toc-api</connection>
        <developerConnection>scm:git:**********************:cf/cf-finance-toc-api.git</developerConnection>
        <url>http://git.shuiditech.com/cf/cf-finance-toc-api</url>
        <tag>HEAD</tag>
    </scm>

    <dependencies>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-enhancer-starter</artifactId>
            <version>${cf-enhancer-starter.verison}</version>
        </dependency>
        <dependency>
            <groupId>com.thetransactioncompany</groupId>
            <artifactId>cors-filter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.pay</groupId>
            <artifactId>pay-rpc-client</artifactId>
            <version>${pay-rpc-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.frame</groupId>
            <artifactId>charity-rpc-client</artifactId>
            <version>${charity-rpc-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-model</artifactId>
            <version>${cf.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>groovy</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-api-client</artifactId>
            <version>${cf.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-client</artifactId>
            <version>${cf-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-client-base</artifactId>
            <version>${cf-client-base.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.data</groupId>
            <artifactId>servicelog-meta-cf</artifactId>
            <version>1.0.62</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-finance-client</artifactId>
            <version>${cf-finance-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-admin-api-pure-client</artifactId>
            <version>${cf-admin-api-pure-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.infra</groupId>
            <artifactId>cos-spring-boot-starter</artifactId>
            <version>${cos-starter.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-store</artifactId>
            <version>1.0.13</version>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.op</groupId>
            <artifactId>shuidi-auth-saas-client</artifactId>
            <version>0.0.15</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-event-center-client</artifactId>
            <version>9.0.104</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.account</groupId>
            <artifactId>account-relation-client</artifactId>
            <version>0.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.dataservice</groupId>
            <artifactId>dataservice-client</artifactId>
            <version>2.1.112</version>
        </dependency>
        <dependency>
            <groupId>io.shardingjdbc</groupId>
            <artifactId>sharding-jdbc-core</artifactId>
            <version>${sharding-jdbc-core.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-finance-feign-client</artifactId>
            <version>3.0.26</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
            <version>${apollo-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>aopalliance</groupId>
                    <artifactId>aopalliance</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>1.4.1</version>
                <executions>
                    <execution>
                        <id>enforce</id>
                        <configuration>
                            <rules>
                                <!--强制项目没有重复声明的依赖项-->
                                <banDuplicatePomDependencyVersions />
                                <!--确保所有jar依赖项是同一版本-->
                                <combinationRules implementation="com.shuidihuzhu.customrule.combination.CombinationRules">
                                    <excludePath>/cf-api/src/main/java</excludePath>
                                </combinationRules>

                                <!--<dependencyConvergence />-->
                                <executorServiceRule implementation="com.shuidihuzhu.customrule.ExecutorServiceRule">
                                    <exclude>

                                    </exclude>
                                    <excludePath>/cf-api/src/main/java</excludePath>
                                </executorServiceRule>
                                <valueRule implementation="com.shuidihuzhu.customrule.ValueRule">
                                    <exclude>

                                    </exclude>
                                    <excludePath>/cf-api/src/main/java</excludePath>
                                </valueRule>
                                <loggerRule implementation="com.shuidihuzhu.customrule.LoggerRule">
                                    <excludePath>/cf-api/src/main/java</excludePath>
                                </loggerRule>
                                <cipherRule implementation="com.shuidihuzhu.rule.CipherRule">
                                    <exclude>

                                    </exclude>
                                </cipherRule>
                            </rules>
                        </configuration>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>enforce-ban-duplicate-classes</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <phase>process-classes</phase>
                        <configuration>
                            <rules>
                                <!--针对jar检测不出来的class-->
                                <banDuplicateClasses>
                                    <ignoreClasses>
                                        <ignoreClass>javax.*</ignoreClass>
                                        <ignoreClass>org.junit.*</ignoreClass>
                                        <ingoreClass>org.aspectj.*</ingoreClass>
                                        <ingoreClass>org.apache.juli.*</ingoreClass>
                                        <ingoreClass>org.apache.commons.logging.*</ingoreClass>
                                        <ingoreClass>org.apache.log4j.*</ingoreClass>
                                        <ingoreClass>org.apache.tomcat.*</ingoreClass>
                                        <ingoreClass>io.netty.*</ingoreClass>
                                        <ingoreClass>net.jcip.annotations.*</ingoreClass>
                                        <ingoreClass>aj.org.objectweb.asm.*</ingoreClass>
                                        <ingoreClass>org.xmlpull.v1.*</ingoreClass>
                                        <ingoreClass>org.hibernate.validator.*</ingoreClass>
                                        <ingoreClass>org.apache.commons.collections.*</ingoreClass>
                                        <ingoreClass>com.shuidihuzhu.cf.service.notice.workwx.*</ingoreClass>
                                        <ingoreClass>org.apache.xmlbeans.*</ingoreClass>
                                        <ingoreClass>org.json.*</ingoreClass>
                                        <ingoreClass>com.shuidihuzhu.dataservice.*</ingoreClass>
                                        <ingoreClass>com.shuidihuzhu.client.*</ingoreClass>
                                        <ingoreClass>com.sun.*</ingoreClass>
                                        <ingoreClass>com.dangdang.ddframe.job.event.rdb.*</ingoreClass>
                                        <ingoreClass>com.zaxxer.*</ingoreClass>
                                    </ignoreClasses>
                                    <findAllDuplicates>true</findAllDuplicates>
                                </banDuplicateClasses>
                            </rules>
                            <fail>true</fail>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>extra-enforcer-rules</artifactId>
                        <version>1.0</version>
                    </dependency>
                    <dependency>
                        <groupId>com.shuidihuzhu.cf</groupId>
                        <artifactId>custom-rule</artifactId>
                        <version>RELEASE</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <dependencies>
                    <dependency>
                        <groupId>javax.activation</groupId>
                        <artifactId>activation</artifactId>
                        <version>1.1.1</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>
</project>
