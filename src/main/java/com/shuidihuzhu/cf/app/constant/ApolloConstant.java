package com.shuidihuzhu.cf.app.constant;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * apollo配置
 *
 * <AUTHOR>
 * @since 2025/1/6
 */
@Component
@Setter
@Getter
@RefreshScope
public class ApolloConstant {

    @Value("${apollo.sensitive.donate.money:}")
    private String sensitiveDonateMoney;

    @Value("${oss-to-cos-defaultCdnDomain:https://image.shuidichou.com}")
    private String ossToCosDefaultCdnDomain;

    @Value("${cos-to-oss-defaultCdnDomain:https://oss.shuidichou.com}")
    private String cosToOssDefaultCdnDomain;

    @Value("${origin-image-cdn:images.shuidichou.com}")
    private String originImageCdn;

    @Value("${oss-image-cdn:oss.shuidichou.com}")
    private String ossImageCdn;

    @Value("${cos-image-cdn:image.shuidichou.com}")
    private String cosImageCdn;
}
