package com.shuidihuzhu.cf.app.constant;


import com.shuidihuzhu.cf.financetoc.biz.CfRedisKvBiz;
import com.shuidihuzhu.client.cf.api.chaifenbeta.commontool.CfUrlConfigFeignClient;
import com.shuidihuzhu.common.web.util.ApplicationContextUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

/**
 * Created by wangsf on 17/2/27.
 */
public class CfUrlConfig {

	private static final String HUZHU_OSS_DOMAIN = CfUrlConfigFeignClient.HUZHU_OSS_DOMAIN;;
	public static final String SDC_OSS_DOMAIN = CfUrlConfigFeignClient.SDC_OSS_DOMAIN;
	private static final String OSS_SHUIDICHOU_DOMAIN = CfUrlConfigFeignClient.OSS_SHUIDICHOU_DOMAIN;

	private static final String KEY_PAGE_DOMAIN = "PAGE_DOMAIN";
	private static final String KEY_API_DOMAIN = "API_DOMAIN";
	private static final String KEY_STATIC_DOMAIN = "STATIC_DOMAIN";
	private static final String KEY_OSS_DOMAIN = "OSS_DOMAIN";

	private static final Logger LOGGER = LoggerFactory.getLogger(CfUrlConfig.class);
	private static final String CF_PAGE_DOMAIN = CfUrlConfigFeignClient.CF_PAGE_DOMAIN;
	private static final String CF_API_DOMAIN = CfUrlConfigFeignClient.CF_API_DOMAIN;
	private static final String CF_STATIC_DOMAIN = CfUrlConfigFeignClient.CF_STATIC_DOMAIN;

	private static final String OSS_SHUIDI_CF_IMAGE_URL = CfUrlConfigFeignClient.OSS_SHUIDI_CF_IMAGE_URL;


	public static String pageDomain() {
		return getDomain(KEY_PAGE_DOMAIN, CF_PAGE_DOMAIN);
	}

	public static String apiDomain() {
		return getDomain(KEY_API_DOMAIN, CF_API_DOMAIN);
	}

	public static String ossDomain() {
		return getDomain(KEY_OSS_DOMAIN, OSS_SHUIDI_CF_IMAGE_URL);
	}


	public static String staticDomain() {
		return getDomain(KEY_STATIC_DOMAIN, CF_STATIC_DOMAIN);
	}

	private static String getDomain(String key, String defaultValue) {
		CfRedisKvBiz cfRedisKvBiz = ApplicationContextUtil.getBean(CfRedisKvBiz.class);

		String domain = (cfRedisKvBiz == null ? "" : cfRedisKvBiz.queryByKey(key, true));
		LOGGER.debug("{} in redis:{}", key, domain);

		domain = (StringUtils.isEmpty(domain) ? defaultValue : domain);
		LOGGER.debug("getDomain {}:{}", key, domain);

		return domain;
	}

	public static String compactOssUrl(String url)  {
		String ossDomain = SDC_OSS_DOMAIN;
		if(!StringUtils.isEmpty(url) && !StringUtils.isEmpty(ossDomain)) {
			url = url.replaceAll(HUZHU_OSS_DOMAIN, ossDomain);
			url = url.replaceAll(OSS_SHUIDICHOU_DOMAIN, ossDomain);
		}
		return url;
	}
}
