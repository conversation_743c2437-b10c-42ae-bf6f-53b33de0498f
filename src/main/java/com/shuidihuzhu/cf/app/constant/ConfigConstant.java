package com.shuidihuzhu.cf.app.constant;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 配置文件常量
 * <AUTHOR>
 * @since 2025/1/6
 */
@Getter
@Setter
@Component
@RefreshScope
public class ConfigConstant {

    @Value("${donations.comment:}")
    private String donationsComment;
}
