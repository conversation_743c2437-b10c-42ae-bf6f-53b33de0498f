package com.shuidihuzhu.cf.app.configuration;

import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.util.redisson.RedissonHandlerWrapper;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * redisson配置
 *
 * <AUTHOR>
 * @since 2025/1/6
 */
@Configuration
public class RedissonConfiguration {

    @Bean("cfUGCRedissonHandler")
    @ConfigurationProperties("redisson-handler.cf-api-cf-ugc-redisson-handler.cf-finance-toc-api")
    public RedissonHandler cfUGCRedissonHandler() {
        return new RedissonHandlerWrapper();
    }

    @Bean("cfRedissonHandler")
    @ConfigurationProperties("redisson-handler.cf-api-cf-redisson-handler.cf-finance-toc-api")
    public RedissonHandler cfRedissonHandler() {
        return new RedissonHandlerWrapper();
    }

    @Bean("cfShareViewRedissonHandler")
    @ConfigurationProperties("redisson-handler.cf-api-cf-share-view-redisson-handler.cf-finance-toc-api")
    public RedissonHandler cfShareViewRedissonHandler() {
        return new RedissonHandlerWrapper();
    }

    @Bean("cfOlapCaseStat")
    @ConfigurationProperties("redisson-handler.cf-api-cf-olap-case-stat.cf-finance-toc-api")
    public RedissonHandler cfOlapCaseStat() {
        return new RedissonHandlerWrapper();
    }

    @Bean("cfInfoDetailRedissonHandler")
    @ConfigurationProperties("redisson-handler.cf-api-cf-info-detail-redisson-handler.cf-finance-toc-api")
    public RedissonHandler cfInfoDetailRedissonHandler() {
        return new RedissonHandlerWrapper();
    }

    @Bean("cfCoreRedissonHandler")
    @ConfigurationProperties("redisson-handler.cf-api-cf-core-redisson-handler.cf-finance-toc-api")
    public RedissonHandler cfCoreRedissonHandler() {
        return new RedissonHandlerWrapper();
    }


    @Bean("cfApiRedisClusterHandler")
    @ConfigurationProperties("redisson-handler.cf-api-cf-api-redis-cluster-handler.cf-finance-toc-api")
    public RedissonHandler cfApiRedisCluster() {
        return new RedissonHandlerWrapper();
    }
}
