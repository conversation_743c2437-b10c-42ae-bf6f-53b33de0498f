package com.shuidihuzhu.cf.app.configuration;
import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import io.shardingjdbc.core.api.ShardingDataSourceFactory;
import io.shardingjdbc.core.api.config.ShardingRuleConfiguration;
import io.shardingjdbc.core.api.config.TableRuleConfiguration;
import io.shardingjdbc.core.api.config.strategy.InlineShardingStrategyConfiguration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.Collections;
import java.util.Map;
import java.util.Properties;

/**
 * 张智
 */
@Configuration
@RefreshScope
public class CfOrderNewDataSourceConfiguration {

    @Value("${shardingjdbc.sql.show:true}")
    private String sqlShow;

    @Value("${shardingjdbc.executor.size1:40}")
    private String executorSize;

    @Resource(name = CfDataSource.CF_ORDER_NEW_MASTER_DATASOURCE)
    private DataSource cfOrderNewMasterDataSource;

    @Resource(name = CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
    private DataSource cfOrderNewSlaveDataSource;

    @Bean(CfDataSource.SHARDING_DONATION_BIG_TABLE_NEW_MASTER)
    public DataSource newShardingDonationBigTableMaster() throws SQLException {
        return newBuildDataSource( Collections.singletonMap("newOrder", cfOrderNewMasterDataSource));
    }
    @Bean(CfDataSource.SHARDING_DONATION_BIG_TABLE_NEW_SLAVE)
    public DataSource newShardingDonationBigTableSlave() throws SQLException {
        return newBuildDataSource( Collections.singletonMap("newOrder", cfOrderNewSlaveDataSource));
    }

    public DataSource newBuildDataSource(Map<String, DataSource> dataSource) throws SQLException {

        TableRuleConfiguration cfOrderCrowdfundingIdCapital = new TableRuleConfiguration();
        cfOrderCrowdfundingIdCapital.setLogicTable("crowdfunding_order_crowdfundingid_sharding");
        cfOrderCrowdfundingIdCapital.setActualDataNodes("newOrder.crowdfunding_order_crowdfundingid_sharding_0${0..9}${0..9}");
        cfOrderCrowdfundingIdCapital.setTableShardingStrategyConfig(new InlineShardingStrategyConfiguration("crowdfunding_id", "crowdfunding_order_crowdfundingid_sharding_${String.format(\"%03d\", Math.abs(crowdfunding_id) % 100)}"));

        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();
        shardingRuleConfig.setDefaultDataSourceName(CfDataSource.CF_ORDER_NEW_MASTER_DATASOURCE);
        shardingRuleConfig.getTableRuleConfigs().add(cfOrderCrowdfundingIdCapital);
        Properties prop = new Properties();
        prop.setProperty("sql.show", sqlShow);
        prop.setProperty("executor.size", executorSize);
        return ShardingDataSourceFactory.createDataSource(dataSource, shardingRuleConfig, Collections.emptyMap(), prop);
    }
}
