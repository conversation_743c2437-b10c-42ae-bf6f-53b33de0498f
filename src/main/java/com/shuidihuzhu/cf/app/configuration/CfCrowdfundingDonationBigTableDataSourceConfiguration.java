package com.shuidihuzhu.cf.app.configuration;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import io.shardingjdbc.core.api.ShardingDataSourceFactory;
import io.shardingjdbc.core.api.config.ShardingRuleConfiguration;
import io.shardingjdbc.core.api.config.TableRuleConfiguration;
import io.shardingjdbc.core.api.config.strategy.InlineShardingStrategyConfiguration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.Collections;
import java.util.Map;
import java.util.Properties;

/**
 * @author: wanghui
 * @time: 2019/3/10 5:15 PM
 * @description:
 */
@Configuration
@RefreshScope
public class CfCrowdfundingDonationBigTableDataSourceConfiguration {

    @Resource(name = CfDataSource.DONATION_BIGTABLE_MASTER)
    private DataSource crowfundingDonationBigTableMaster;
    @Resource(name = CfDataSource.DONATION_BIGTABLE_SLAVE_1)
    private DataSource crowfundingDonationBigTableSlave;

    @Value("${shardingjdbc.sql.show:true}")
    private String sqlShow;

    @Value("${shardingjdbc.executor.size1:40}")
    private String executorSize;


    @Bean(CfDataSource.SHARDING_DONATION_BIG_TABLE_MASTER)
    public DataSource crowdfundingOrderIdSharding() throws SQLException {
        return buildDataSource( Collections.singletonMap("crowfundingDonationBigTableSharding", crowfundingDonationBigTableMaster));
    }
    @Bean(CfDataSource.SHARDING_DONATION_BIG_TABLE_SLAVE)
    public DataSource crowdfundingOrderCrowdfundingidSharding() throws SQLException {
        return buildDataSource( Collections.singletonMap("crowfundingDonationBigTableSharding", crowfundingDonationBigTableSlave));
    }



    public DataSource buildDataSource(Map<String, DataSource> dataSource) throws SQLException {
        TableRuleConfiguration cfOrderIdCapital = new TableRuleConfiguration();
        cfOrderIdCapital.setLogicTable("crowdfunding_order_id_sharding");
        cfOrderIdCapital.setActualDataNodes("crowfundingDonationBigTableSharding.crowdfunding_order_id_sharding_0${0..9}${0..9}");
        cfOrderIdCapital.setTableShardingStrategyConfig(new InlineShardingStrategyConfiguration("id", "crowdfunding_order_id_sharding_${String.format(\"%03d\", Math.abs(id) % 100)}"));

        TableRuleConfiguration cfOrderCrowdfundingIdCapital = new TableRuleConfiguration();
        cfOrderCrowdfundingIdCapital.setLogicTable("crowdfunding_order_crowdfundingid_sharding");
        cfOrderCrowdfundingIdCapital.setActualDataNodes("crowfundingDonationBigTableSharding.crowdfunding_order_crowdfundingid_sharding_0${0..9}${0..9}");
        cfOrderCrowdfundingIdCapital.setTableShardingStrategyConfig(new InlineShardingStrategyConfiguration("crowdfunding_id", "crowdfunding_order_crowdfundingid_sharding_${String.format(\"%03d\", Math.abs(crowdfunding_id) % 100)}"));

        TableRuleConfiguration cfOrderUserIdCapital = new TableRuleConfiguration();
        cfOrderUserIdCapital.setLogicTable("crowdfunding_order_userid_sharding");
        cfOrderUserIdCapital.setActualDataNodes("crowfundingDonationBigTableSharding.crowdfunding_order_userid_sharding_0${0..9}${0..9}");
        cfOrderUserIdCapital.setTableShardingStrategyConfig(new InlineShardingStrategyConfiguration("user_id", "crowdfunding_order_userid_sharding_${String.format(\"%03d\", Math.abs(user_id) % 100)}"));

        TableRuleConfiguration cfPayRecordOrderIdCapital = new TableRuleConfiguration();
        cfPayRecordOrderIdCapital.setLogicTable("crowdfunding_pay_record_orderid_sharding");
        cfPayRecordOrderIdCapital.setActualDataNodes("crowfundingDonationBigTableSharding.crowdfunding_pay_record_orderid_sharding_0${0..9}${0..9}");
        cfPayRecordOrderIdCapital.setTableShardingStrategyConfig(new InlineShardingStrategyConfiguration("crowdfunding_order_id", "crowdfunding_pay_record_orderid_sharding_${String.format(\"%03d\", Math.abs(crowdfunding_order_id) % 100)}"));

        TableRuleConfiguration cfPayRecordPayUidCapital = new TableRuleConfiguration();
        cfPayRecordPayUidCapital.setLogicTable("crowdfunding_pay_record_payuid_sharding");
        cfPayRecordPayUidCapital.setActualDataNodes("crowfundingDonationBigTableSharding.crowdfunding_pay_record_payuid_sharding_0${0..9}${0..9}");
        cfPayRecordPayUidCapital.setTableShardingStrategyConfig(new InlineShardingStrategyConfiguration("pay_uid", "crowdfunding_pay_record_payuid_sharding_${String.format(\"%03d\", Math.abs(pay_uid.hashCode() & 0x7FFFFFFF) % 100)}"));


        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();
        shardingRuleConfig.setDefaultDataSourceName("crowfundingDonationBigTableSharding");
        shardingRuleConfig.getTableRuleConfigs().add(cfOrderIdCapital);
        shardingRuleConfig.getTableRuleConfigs().add(cfOrderCrowdfundingIdCapital);
        shardingRuleConfig.getTableRuleConfigs().add(cfOrderUserIdCapital);
        shardingRuleConfig.getTableRuleConfigs().add(cfPayRecordOrderIdCapital);
        shardingRuleConfig.getTableRuleConfigs().add(cfPayRecordPayUidCapital);

        Properties prop = new Properties();
        prop.setProperty("sql.show", sqlShow);
        prop.setProperty("executor.size", executorSize);
        return ShardingDataSourceFactory.createDataSource(dataSource, shardingRuleConfig, Collections.emptyMap(), prop);
    }
}
