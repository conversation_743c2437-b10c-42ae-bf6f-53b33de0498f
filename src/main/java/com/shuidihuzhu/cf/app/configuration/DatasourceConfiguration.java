package com.shuidihuzhu.cf.app.configuration;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.shuidihuzhu.common.datasource.DS;

import javax.sql.DataSource;

/**
 * 数据源配置
 *
 * <AUTHOR>
 * @since 2025/1/6
 */
@Configuration
public class DatasourceConfiguration {

    public static final String CF_FINANCE_MASTER = "cfFinanceMasterDataSource";

    @Bean(DS.CF)
    @ConfigurationProperties("spring.datasource.druid.cf-api-cf.cf-finance-toc-api")
    public DataSource cfMaster() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(DS.CF_SLAVE)
    @ConfigurationProperties("spring.datasource.druid.cf-api-cf-slave.cf-finance-toc-api")
    public DataSource cfSlave() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(CfDataSource.CF_ORDER_NEW_MASTER_DATASOURCE)
    @ConfigurationProperties("spring.datasource.druid.cf-api-cf-order-new.cf-finance-toc-api")
    public DataSource cfOrderNewMaster() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
    @ConfigurationProperties("spring.datasource.druid.cf-api-cf-order-new-slave.cf-finance-toc-api")
    public DataSource cfOrderNewSlave() {
        return DruidDataSourceBuilder.create().build();
    }

    // cf-order-master
    @Bean(CfDataSource.DONATION_BIGTABLE_MASTER)
    @ConfigurationProperties("spring.datasource.druid.cf-api-cf-donation-big-table.cf-finance-toc-api")
    public DataSource cfOrderMaster() {
        return DruidDataSourceBuilder.create().build();
    }
    // cf-order-slave
    @Bean(CfDataSource.DONATION_BIGTABLE_SLAVE_1)
    @ConfigurationProperties("spring.datasource.druid.cf-api-cf-donation-big-table-slave.cf-finance-toc-api")
    public DataSource cfOrderSlave() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(CF_FINANCE_MASTER)
    @ConfigurationProperties("spring.datasource.druid.cf-api-cf-finance.cf-finance-toc-api")
    public DataSource cfFinanceMaster() {
        return DruidDataSourceBuilder.create().build();
    }
}
