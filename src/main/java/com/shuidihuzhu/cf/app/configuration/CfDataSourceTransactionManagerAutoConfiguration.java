package com.shuidihuzhu.cf.app.configuration;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.fulllink.measurement.datasource.FulllinkDataSourceTransactionManager;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.AutoConfigureOrder;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

@Configuration
@ConditionalOnClass({JdbcTemplate.class, PlatformTransactionManager.class})
@AutoConfigureOrder(Ordered.LOWEST_PRECEDENCE)
@EnableConfigurationProperties(DataSourceProperties.class)
public class CfDataSourceTransactionManagerAutoConfiguration {


    @Configuration
    static class CfDataSourceTransactionManagerConfiguration {

        private final DataSource dataSource;

        private final TransactionManagerCustomizers transactionManagerCustomizers;

        CfDataSourceTransactionManagerConfiguration(DataSource dataSource,
                                                    ObjectProvider<TransactionManagerCustomizers> transactionManagerCustomizers) {
            this.dataSource = dataSource;
            this.transactionManagerCustomizers = transactionManagerCustomizers
                    .getIfAvailable();
        }

        @Bean(CfDataSource.CROWDFUNDIN_DATASOURCE_TRANSACTION_MANAGER)
        public DataSourceTransactionManager crowdfundingDataSourceTransactionManage(
                DataSourceProperties properties) {
            DataSourceTransactionManager transactionManager = new FulllinkDataSourceTransactionManager(dataSource, "crowdfundingDataSource");
            if (this.transactionManagerCustomizers != null) {
                this.transactionManagerCustomizers.customize(transactionManager);
            }
            return transactionManager;
        }

        @Bean(CfDataSource.CF_ORDER_NEW_MASTER_DATASOURCE_TRANSACTION_MANAGER)
        public DataSourceTransactionManager crowfundingDonationBigTableMasterTransactionManage(
                DataSourceProperties properties) {
            DataSourceTransactionManager transactionManager = new FulllinkDataSourceTransactionManager(dataSource, CfDataSource.CF_ORDER_NEW_MASTER_DATASOURCE);
            if (this.transactionManagerCustomizers != null) {
                this.transactionManagerCustomizers.customize(transactionManager);
            }
            return transactionManager;
        }

    }
}