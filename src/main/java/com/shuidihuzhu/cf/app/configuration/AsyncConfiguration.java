package com.shuidihuzhu.cf.app.configuration;

import com.shuidihuzhu.cf.constants.AsyncPoolConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置
 *
 * <AUTHOR>
 * @since 2025/1/6
 */
@Slf4j
@Configuration
public class AsyncConfiguration {

    public static final String addOrderMsgPool = "addOrderMsgPool";

    @Bean(AsyncPoolConstants.SEND_PAY_SUCCESS_OLAP)
    public Executor casePayOlapExecutor() {
        return getExecutor(AsyncPoolConstants.SEND_PAY_SUCCESS_OLAP, 10, 20);
    }

    private Executor getExecutor(String poolName, int coreSize, int maxSize) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreSize);
        executor.setMaxPoolSize(maxSize);
        executor.setQueueCapacity(1000);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix(poolName + "-");
        executor.setRejectedExecutionHandler(new RejectedExecutionHandler() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                log.info("The {} is discarded", poolName);
            }
        });
        return executor;
    }

    private Executor getExecutor(String poolName, int coreSize, int maxSize, RejectedExecutionHandler rejectedExecutionHandler) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreSize);
        executor.setMaxPoolSize(maxSize);
        executor.setQueueCapacity(1000);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix(poolName + "-");
        executor.setRejectedExecutionHandler(rejectedExecutionHandler);
        return executor;
    }

    @Bean(AsyncPoolConstants.SEND_CRYPTO_RELATION_MQ_POOL)
    public Executor sendRelationMQExecutor() {
        return getExecutor(AsyncPoolConstants.SEND_CRYPTO_RELATION_MQ_POOL, 5, 10);
    }

    @Bean(AsyncPoolConstants.ORDER_ADD)
    public Executor orderAdd() {
        return getExecutor(AsyncPoolConstants.ORDER_ADD, 10, 20);
    }

    @Bean(addOrderMsgPool)
    public Executor addOrderMsgPool() {
        return getExecutor(addOrderMsgPool, 10, 20, new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(AsyncPoolConstants.WRITE_SIMPLE_INFO_POOL)
    public Executor refreshUserInfoExecutor() {
        return getExecutor(AsyncPoolConstants.WRITE_SIMPLE_INFO_POOL, 5, 10);
    }
}
