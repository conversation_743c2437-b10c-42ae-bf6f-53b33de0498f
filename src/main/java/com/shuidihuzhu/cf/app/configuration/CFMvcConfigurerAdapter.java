package com.shuidihuzhu.cf.app.configuration;

import com.shuidihuzhu.common.web.filter.LogRequestFilter;
import com.shuidihuzhu.common.web.filter.UserIdentityInjectFilter;
import com.shuidihuzhu.common.web.interceptor.OptionalSessionKeyValidateInterceptor;
import com.shuidihuzhu.common.web.interceptor.SessionKeyValidateInterceptor;
import com.shuidihuzhu.common.web.interceptor.SigValidateInterceptor;
import com.thetransactioncompany.cors.CORSConfiguration;
import com.thetransactioncompany.cors.CORSConfigurationException;
import com.thetransactioncompany.cors.CORSFilter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.servlet.FileCleanerCleanup;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.ServletListenerRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Collections;
import java.util.Properties;

/**
 * Created by zhouyou on 2017/12/8.
 */
@Slf4j
@Configuration
@SuppressWarnings({"all"})
public class CFMvcConfigurerAdapter implements WebMvcConfigurer {

    @Bean
    public SigValidateInterceptor sigValidateInterceptor() {
        return new SigValidateInterceptor();
    }

    @Bean
    public SessionKeyValidateInterceptor sessionKeyValidateInterceptor() {
        return new SessionKeyValidateInterceptor();
    }

    @Bean
    public OptionalSessionKeyValidateInterceptor optionalSessionKeyValidateInterceptor() {
        return new OptionalSessionKeyValidateInterceptor();
    }


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(sigValidateInterceptor())
                .addPathPatterns("/api/**");
        registry.addInterceptor(sessionKeyValidateInterceptor())
                .addPathPatterns("/api/**");
        registry.addInterceptor(optionalSessionKeyValidateInterceptor())
                .addPathPatterns("/api/**");
    }

    @Bean
    public ServletListenerRegistrationBean<FileCleanerCleanup> fileCleanerCleanup() {
        return new ServletListenerRegistrationBean<>(new FileCleanerCleanup());
    }

    @Bean
    public FilterRegistrationBean logRequestFilter() {
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean(new LogRequestFilter());
        filterRegistrationBean.setUrlPatterns(Collections.singleton("/*"));
        filterRegistrationBean.setOrder(Ordered.HIGHEST_PRECEDENCE + 2);
        return filterRegistrationBean;
    }

    @Bean
    public FilterRegistrationBean corsFilter() throws CORSConfigurationException {
        Properties prop = new Properties();
        prop.setProperty("cors.maxAge", "3600");
        CORSConfiguration corsConfig = new CORSConfiguration(prop);
        CORSFilter filter = new CORSFilter(corsConfig);
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean(filter);
        filterRegistrationBean.setUrlPatterns(Collections.singleton("/*"));
        filterRegistrationBean.setOrder(Ordered.HIGHEST_PRECEDENCE + 3);
        return filterRegistrationBean;
    }

    @Bean
    public UserIdentityInjectFilter sessionKeyParseFilter(){
        return new UserIdentityInjectFilter();
    }

    @Bean
    public FilterRegistrationBean sessionKeyParseFilterBean(UserIdentityInjectFilter sessionKeyParseFilter) throws CORSConfigurationException {
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean(sessionKeyParseFilter);
        filterRegistrationBean.setUrlPatterns(Collections.singleton("/api/**"));
        filterRegistrationBean.setOrder(Ordered.HIGHEST_PRECEDENCE + 4);
        return filterRegistrationBean;
    }
}
