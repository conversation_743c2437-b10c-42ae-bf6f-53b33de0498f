package com.shuidihuzhu.cf.app;

import com.shuidihuzhu.cf.enhancer.subject.druid.EnableDruidMonitor;
import com.shuidihuzhu.cf.enhancer.subject.threadpool.annotation.EnableDynamicThreadPool;
import com.shuidihuzhu.eb.grafana.configuration.plugin.health.*;
import com.shuidihuzhu.eb.grafana.configuration.plugin.hystrix.executionhook.EnableCfMetricsHystrixCommandExecutionHook;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.circuitbreaker.EnableCircuitBreaker;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.netflix.hystrix.dashboard.EnableHystrixDashboard;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 * @since 2024/12/30
 */
@SpringBootApplication(scanBasePackages = {
        "com.shuidihuzhu",
},
        exclude = {
                DataSourceAutoConfiguration.class,
                ElasticsearchRestClientAutoConfiguration.class
        })
@EnableDiscoveryClient
@EnableFakeRedissonHealth
@EnableFakeDataSourceHealth
@EnableFeignClients(basePackages = {
        "com.shuidihuzhu.client",
        "com.shuidihuzhu.cf.risk",
        "com.shuidihuzhu.cf.feign",
        "com.shuidihuzhu.hz.client",
        "com.shuidihuzhu.data",
        "com.shuidihuzhu.delay",
        "com.shuidihuzhu.frame.client",
        "com.shuidihuzhu.cf.finance.client",
        "com.shuidihuzhu.auth",
        "com.shuidihuzhu.riskcontrol.client.innerapi",
        "com.shuidihuzhu.sdb.order",
        "com.shuidihuzhu.cf.client",
        "com.shuidihuzhu.msgserver",
        "com.shuidihuzhu.cf.activity",
        "com.shuidihuzhu.cf",
        "com.shuidihuzhu.charity.client",
        "com.shuidihuzhu.account",
        "com.shuidihuzhu.frame.platform.client",
        "com.shuidihuzhu.ai.algo.client",
        "com.shuidihuzhu.cf.service.calltree",
        "com.shuidihuzhu.account.verifycode.client",
        "com.shuidihuzhu.alps",
        "com.shuidihuzhu.ai.alps",
        "com.shuidihuzhu.wx",
        "com.shuidihuzhu.tax"
})
@EnableAspectJAutoProxy(proxyTargetClass = true)
@EnableCircuitBreaker
@EnableAsync
@EnableHystrixDashboard
@EnableTransactionManagement
@EnableFakeCommonsClient
@EnableFakeConsulHealth
@EnableFakeDiskSpaceHealth
@EnableScheduling
@EnableCfMetricsHystrixCommandExecutionHook
@EnableDruidMonitor
@EnableDynamicThreadPool(globalModel = true)
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
