package com.shuidihuzhu.cf.financetoc.mq.producer;

import com.shuidihuzhu.cf.app.configuration.AsyncConfiguration;
import com.shuidihuzhu.cf.event.OrderAddEvent;
import com.shuidihuzhu.cf.financetoc.service.CommonMessageHelperService;
import com.shuidihuzhu.cf.model.CaseEndModel;
import com.shuidihuzhu.cf.mq.payload.OrderAddPayload;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * MQ统一发送中心
 *
 * <AUTHOR>
 * @since 2025/1/6
 */
@Slf4j
@Component
public class CommonProducer {

    @Resource
    private CommonMessageHelperService commonMessageHelperService;

    @Async(AsyncConfiguration.addOrderMsgPool)
    public void orderAddMsg(OrderAddPayload orderAddPayload) {
        commonMessageHelperService.send(commonMessageHelperService.getOrderAddMsg(orderAddPayload));
    }

    public void crowdfundingEndMsg(CaseEndModel caseEndModel) {
        commonMessageHelperService.send(commonMessageHelperService.getCrowdfundingEndMsg(caseEndModel));
    }
}
