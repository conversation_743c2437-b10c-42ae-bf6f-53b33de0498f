package com.shuidihuzhu.cf.financetoc.mq.consumer;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.financetoc.biz.refund.CfDonorApplyRefundBiz;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.wx.grpc.model.WxTextMessageModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 捐款人输入 我要退款 事件消费MQ
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_DONOR_APPLY_REFUND_EVENT,
        tags = MQTagCons.CF_DONOR_APPLY_REFUND_EVENT,
        topic = MQTopicCons.CF,
        group = MQTagCons.CF_DONOR_APPLY_REFUND_EVENT)
public class CfDonorApplyRefundConsumer implements MessageListener<WxTextMessageModel> {


    @Resource
    private CfDonorApplyRefundBiz cfDonorApplyRefundBiz;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<WxTextMessageModel> mqMessage) {
        if (Objects.isNull(mqMessage)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        try {
            cfDonorApplyRefundBiz.doEvent(mqMessage.getPayload());
        } catch (Exception e) {
            log.error("openId:{}", mqMessage.getPayload().getOpenId(), e);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
