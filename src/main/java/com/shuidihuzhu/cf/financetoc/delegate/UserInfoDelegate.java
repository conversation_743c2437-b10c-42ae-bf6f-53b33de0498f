package com.shuidihuzhu.cf.financetoc.delegate;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.client.grpc.account.v1.feign.UserInfoServiceClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserInfoDelegate {

    @Autowired
    private UserInfoServiceClient userInfoServiceClient;

    private final static int USER_BATCH_PARTITION_SIZE = 1000;

    private UserInfoServiceClient getClient() {
        return this.userInfoServiceClient;
    }

    public Map<Long, UserInfoModel> getUserMap(List<Long> userIds){

        List<UserInfoModel> users = null;

        try {
            users = this.getUserInfoByUserIdBatch(userIds);
        } catch (Exception e) {
            log.warn("getUserInfoByUserIdBatch userIds:{} 异常", userIds, e);
        }

        if (users == null) {
            return Maps.newHashMap();
        }
        return users.stream().collect(Collectors.toMap(UserInfoModel::getUserId, v -> v, (x, y) -> x));
    }

    public UserInfoModel getUserInfoByUserId(long userId) {
        return getClient().getUserInfoByUserId(userId);
    }

    public List<UserInfoModel> getUserInfoByUserIdBatch(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        List<UserInfoModel> result  = new ArrayList<>(userIds.size());
        for (List<Long> item : Lists.partition(userIds, USER_BATCH_PARTITION_SIZE)) {
            result.addAll(getClient().getUserInfoByUserIdBatchV2(item));
        }
        return result;
    }

    public UserInfoModel getUserInfoByOpenId(String openId) {
        return getClient().getUserInfoByOpenId(openId);
    }

    public List<UserInfoModel> getUserInfoByCryptoIdcards(List<String> idCards) {
        return getClient().getUserInfoByCryptoIdcards(idCards);
    }

    public UserInfoModel getUserInfoByMobile(String mobile) {
        return getClient().getUserInfoByMobile(mobile);
    }

    public void updateRealNameAndCryptoIdCard(long userId, String realName, String cryptoIdCard) {
        getClient().updateRealNameAndCryptoIdCard(userId, realName, cryptoIdCard);
    }
}
