package com.shuidihuzhu.cf.financetoc.delegate;

import com.shuidihuzhu.client.baseservice.pay.model.*;
import com.shuidihuzhu.client.baseservice.pay.model.v3.PayInfoParamV3;
import com.shuidihuzhu.client.baseservice.pay.model.v3.combine.callback.CombineInnerCallBack;
import com.shuidihuzhu.client.baseservice.pay.model.v3.combine.callback.CombineInnerCallBackInfo;

public interface IPayClientRpcDelegate {

    /**
     * 支付回调验证接口(第二步)
     * @param payInnerCallBackInfo（调用PayInnerCallBackInfo(request)构造请求参数）
     * @return 不为空则是表示验证成功
     */
    PayInnerCallBack verifyCallback(PayInnerCallBackInfo payInnerCallBackInfo);

    CombineInnerCallBack verifyCombineCallback(CombineInnerCallBackInfo combineInnerCallBackInfo);

    /**
     * 预支付接口(第一步)
     * @param payInfoParamV3
     * @return
     */
    PayRpcResponse<PayResultV2> unifiedOrder(PayInfoParamV3 payInfoParamV3);
}
