package com.shuidihuzhu.cf.financetoc.delegate;

import com.shuidihuzhu.cf.finance.model.deposit.CfPayeeDepositAccount;

import java.util.List;

/**
 * @Author: l<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019/12/15 18:17
 */
public interface ICfDepositAccountDelegate {

    /**
     * 获取收款账户--在资金
     *
     * @param encryptCardNo
     * @return
     */
    List<CfPayeeDepositAccount> getPayeeAccount(String encryptCardNo);

}
