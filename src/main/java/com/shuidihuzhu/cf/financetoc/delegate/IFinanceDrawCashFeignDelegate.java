package com.shuidihuzhu.cf.financetoc.delegate;

import com.shuidihuzhu.cf.finance.model.CfReminderWord;
import com.shuidihuzhu.cf.finance.model.drawcash.CfDrawExpectCapitationModel;
import com.shuidihuzhu.cf.finance.model.vo.CfDrawCashApplyVo;
import com.shuidihuzhu.cf.finance.model.vo.DrawApplyCheckResult;
import com.shuidihuzhu.cf.finance.model.vo.drawcash.CfApplyDrawWithAuditVo;
import com.shuidihuzhu.cf.finance.model.vo.drawcash.CrowdfundingInfoApplyDrawVo;
import com.shuidihuzhu.client.model.AnchorPageVO;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;
import java.util.Map;

/**
 * @author: wanghui
 * @create: 2019/6/12 5:02 PM
 */
public interface IFinanceDrawCashFeignDelegate {

	/**
	 * 边筹边取 提现申请
	 *
	 * @param crowdfundingInfoApplyDrawVo
	 * @return
	 */
	Response<DrawApplyCheckResult> drawSplitApplyDrawCash(CrowdfundingInfoApplyDrawVo crowdfundingInfoApplyDrawVo);
}
