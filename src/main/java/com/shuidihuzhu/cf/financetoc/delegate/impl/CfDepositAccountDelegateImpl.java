package com.shuidihuzhu.cf.financetoc.delegate.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.finance.client.feign.deposit.CfDepositAccountFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.deposit.CfPayeeDepositAccount;
import com.shuidihuzhu.cf.financetoc.delegate.ICfDepositAccountDelegate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * @Author: lianghongchao
 * @Date: 2019/12/15 18:17
 */
@Service
@Slf4j
public class CfDepositAccountDelegateImpl implements ICfDepositAccountDelegate {
	@Autowired
	private CfDepositAccountFeignClient cfDepositAccountFeignClient;

	@Override
	public List<CfPayeeDepositAccount> getPayeeAccount(String encryptIdCardNo) {
		if (StringUtils.isEmpty(encryptIdCardNo)) {
			return Lists.newArrayList();
		}
		FeignResponse<List<CfPayeeDepositAccount>> feignResponse = cfDepositAccountFeignClient.getPayeeAccount(encryptIdCardNo);
		List<CfPayeeDepositAccount> payeeDepositAccountList = feignResponse.getData();
		return CollectionUtils.isEmpty(payeeDepositAccountList) ? Lists.newArrayList() : payeeDepositAccountList;
	}
}
