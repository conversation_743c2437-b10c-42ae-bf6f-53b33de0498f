package com.shuidihuzhu.cf.financetoc.delegate.impl;

import com.shuidihuzhu.cf.financetoc.delegate.ICfGrowthtoolDelegate;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolVolunteerFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: wanghui
 * @create: 2019/7/23 4:24 PM
 */
@Slf4j
@Service
public class CfGrowthtoolDelegate implements ICfGrowthtoolDelegate {
    @Autowired
    private CfGrowthtoolVolunteerFeignClient cfGrowthtoolVolunteerFeignClient;
    @Override
    public void transferCfVolunteer( long fromUserId,  long toUserId){
        cfGrowthtoolVolunteerFeignClient.transferCfVolunteer(fromUserId,toUserId);
    }
}
