package com.shuidihuzhu.cf.financetoc.delegate.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceFeignClientV2;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceRefundFeignClient;
import com.shuidihuzhu.cf.finance.client.feign.CfOrderAccountFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.CfDonorRefundApply;
import com.shuidihuzhu.cf.finance.model.CfFinanceBcpModel;
import com.shuidihuzhu.cf.finance.model.CfOrderTransferModel;
import com.shuidihuzhu.cf.finance.model.CfRefundRecord;
import com.shuidihuzhu.cf.finance.model.req.CfDrawInfoReq;
import com.shuidihuzhu.cf.finance.model.vo.CfFinanceCapitalStatusVo;
import com.shuidihuzhu.cf.finance.model.vo.CfFinanceDrawApplyVo;
import com.shuidihuzhu.cf.finance.model.vo.DonationAmountInFenVo;
import com.shuidihuzhu.cf.finance.model.vo.draw.CfAmountInfoVo;
import com.shuidihuzhu.cf.finance.model.vo.drawcash.DrawCashInfoVo;
import com.shuidihuzhu.cf.finance.model.vo.refund.CfRefundApplyV2Vo;
import com.shuidihuzhu.cf.financetoc.delegate.ICFFinanceFeignDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefund;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfRefundApplyVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingOrderVo;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class ICFFinanceFeignDelegateImpl implements ICFFinanceFeignDelegate {

    @Resource
    private CfFinanceRefundFeignClient cfFinanceRefundFeignClient;
    @Autowired
    private CfFinanceFeignClientV2 cfFinanceFeignClientV2;
    @Autowired
    private CfOrderAccountFeignClient cfOrderAccountFeignClient;

    /**
     * 自助下发退款
     *
     * @param userId
     * @param infoUuid
     * @param orderId
     * @return
     */
    @Override
    public Response cfRefundLaunchHelper(long userId, String infoUuid, long orderId) {
        if (userId <= 0 || StringUtils.isBlank(infoUuid) || orderId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.ORDER_NOT_EXISTS);
        }
        FeignResponse response = cfFinanceRefundFeignClient.launchHelper(userId, infoUuid, orderId);
        log.info("自助下发退款失败:orderId:{},response:{}", orderId, JSON.toJSONString(response));
        if (response.notOk()) {
            return NewResponseUtil.makeFail("退款失败,请稍后联系水滴筹平台");
        }
        return NewResponseUtil.makeSuccess(null);
    }

    /**
     * 筹款人看到的金额详情
     *
     * @param infoUuid
     * @return
     */
    @Override
    public CfAmountInfoVo getFundraiserAmountInfo(String infoUuid) {
        if (StringUtils.isBlank(infoUuid)) {
            return null;
        }
        com.shuidihuzhu.cf.finance.client.response.model.FeignResponse<CfAmountInfoVo> response
                = cfFinanceFeignClientV2.getFundraiserAmountInfo(infoUuid);
        CfAmountInfoVo amountInfoVo = Optional.ofNullable(response)
                .filter(com.shuidihuzhu.cf.finance.client.response.model.FeignResponse::ok)
                .map(com.shuidihuzhu.cf.finance.client.response.model.FeignResponse::getData)
                .orElse(null);

        if (amountInfoVo != null && amountInfoVo.getCashLimit() != null &&
                amountInfoVo.getCashLimit().getCurLimitAmount() != null) {
            amountInfoVo.getCashLimit().setOperateMsg("");
            amountInfoVo.getCashLimit().setCaseId(0);
            amountInfoVo.getCashLimit().setOperateId(0);
            amountInfoVo.getCashLimit().setCurLimitAmount(
                    Math.max(0, amountInfoVo.getCashLimit().getCurLimitAmount()));
        }

        return amountInfoVo;
    }

    /**
     * 获取单笔退款订单状态
     *
     * @param orderIds
     * @return
     */
    @Override
    public Map<Long, CfDonorRefundApply.StatusEnum> getOrderRefundStatus(List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Maps.newHashMap();
        }
        FeignResponse<Map<Long, CfDonorRefundApply.StatusEnum>> response = cfFinanceRefundFeignClient.getOrderRefundStatus(orderIds);
        return Optional.ofNullable(response)
                .filter(FeignResponse::ok)
                .map(FeignResponse::getData)
                .orElse(Maps.newHashMap());
    }

    /**
     * 资金系统下单
     *
     * @param cfFinanceBcpModel
     * @return
     */
    @Override
    public boolean addOrderToFinance(CfFinanceBcpModel cfFinanceBcpModel) {
        if (cfFinanceBcpModel == null) {
            return false;
        }
        com.shuidihuzhu.cf.finance.client.response.model.FeignResponse<Integer> feignResponse
                = cfFinanceFeignClientV2.updateByPayV3(cfFinanceBcpModel);
        return Optional.ofNullable(feignResponse)
                .filter(com.shuidihuzhu.cf.finance.client.response.model.FeignResponse::ok)
                .map(com.shuidihuzhu.cf.finance.client.response.model.FeignResponse::getData)
                .orElse(0) == 1;
    }



    @Override
    public int transferOrder(List<CrowdfundingOrder> orderList, long fromUserId) {
        if (CollectionUtils.isEmpty(orderList) || fromUserId < 0) {
            return 0;
        }
        int[] count = {0, 0};
        Lists.partition(orderList, 200).forEach(orders -> {
            List<CfOrderTransferModel> cfOrderTransferModelList = Lists.newArrayList();
            orders.forEach(order -> {
                cfOrderTransferModelList.add(CfOrderTransferModel.builder()
                        .caseId(order.getCrowdfundingId())
                        .orderId(order.getId())
                        .newUserId(order.getUserId())
                        .oldUserId(fromUserId)
                        .build());
            });
            int res = this.transferOrderByUserId(cfOrderTransferModelList);
            log.info("userId:{},转移了{}个订单", fromUserId, res);
            if (res < 0) {
                //失败再补一次
                res = this.transferOrderByUserId(cfOrderTransferModelList);
            }
            if (res > 0) {
                count[0] += res;
            }
        });
        return count[0];
    }

    private int transferOrderByUserId(List<CfOrderTransferModel> cfOrderTransferModelList) {
        if (CollectionUtils.isEmpty(cfOrderTransferModelList)) {
            return 0;
        }
        try {
            com.shuidihuzhu.cf.finance.client.response.model.FeignResponse<Integer> response =
                    cfOrderAccountFeignClient.transferOrderByUserId(cfOrderTransferModelList);
            log.info("资产转移结果:{},{}", JSON.toJSONString(response), JSON.toJSONString(cfOrderTransferModelList));
            if (response.ok() && response.getData() != null) {
                return response.getData();
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return -1;
    }

    @Override
    public Response<Void> cancelRefundApply(int caseId, long userId) {

        FeignResponse<CfRefundApplyV2Vo> response = cfFinanceRefundFeignClient.cancelRefundApply(caseId, userId);
        if (response.notOk()) {
            return NewResponseUtil.makeFail("取消申请失败");
        }
        return NewResponseUtil.makeSuccess();
    }
}
