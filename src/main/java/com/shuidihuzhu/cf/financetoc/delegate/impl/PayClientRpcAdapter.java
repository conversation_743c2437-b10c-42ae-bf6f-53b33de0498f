package com.shuidihuzhu.cf.financetoc.delegate.impl;

import com.shuidihuzhu.cf.financetoc.delegate.IPayClientRpcAdapter;
import com.shuidihuzhu.cf.financetoc.delegate.IPayClientRpcDelegate;
import com.shuidihuzhu.client.baseservice.pay.model.*;
import com.shuidihuzhu.client.baseservice.pay.model.v3.PayInfoParamV3;
import com.shuidihuzhu.client.baseservice.pay.model.v3.combine.callback.CombineInnerCallBack;
import com.shuidihuzhu.client.baseservice.pay.model.v3.combine.callback.CombineInnerCallBackInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service(value = "payClientRpcAdapter")
@Slf4j
public class PayClientRpcAdapter implements IPayClientRpcAdapter {
    @Resource(name = "payClientRpcDelegateImpl")
    private IPayClientRpcDelegate realPayClientRpcDelegate;

    private IPayClientRpcDelegate getCurrentDelegate(){
        return this.realPayClientRpcDelegate;
    }

    /**
     * 支持mock
     * @param payInnerCallBackInfo（调用PayInnerCallBackInfo(request)构造请求参数）
     * @return
     */
    @Override
    public PayInnerCallBack verifyCallback(PayInnerCallBackInfo payInnerCallBackInfo) {
        return getCurrentDelegate().verifyCallback(payInnerCallBackInfo);
    }

    @Override
    public CombineInnerCallBack verifyCombineCallback(CombineInnerCallBackInfo combineInnerCallBackInfo) {
        return getCurrentDelegate().verifyCombineCallback(combineInnerCallBackInfo);
    }

    /**
     * 支持mock
     * @param payInfoParamV3
     * @return
     */
    @Override
    public PayRpcResponse<PayResultV2> unifiedOrder(PayInfoParamV3 payInfoParamV3) {
        return getCurrentDelegate().unifiedOrder(payInfoParamV3);
    }
}
