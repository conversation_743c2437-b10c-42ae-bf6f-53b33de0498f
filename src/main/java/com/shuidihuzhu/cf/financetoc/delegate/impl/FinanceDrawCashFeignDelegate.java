package com.shuidihuzhu.cf.financetoc.delegate.impl;

import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceDrawCashFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.vo.DrawApplyCheckResult;
import com.shuidihuzhu.cf.finance.model.vo.drawcash.CrowdfundingInfoApplyDrawVo;
import com.shuidihuzhu.cf.financetoc.delegate.IFinanceDrawCashFeignDelegate;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * @author: wanghui
 * @create: 2019/6/12 5:03 PM
 */
@Slf4j
@Service
@RefreshScope
public class FinanceDrawCashFeignDelegate implements IFinanceDrawCashFeignDelegate {
	@Autowired
	private CfFinanceDrawCashFeignClient cfFinanceDrawCashFeignClient;

	@Override
	public Response<DrawApplyCheckResult> drawSplitApplyDrawCash(CrowdfundingInfoApplyDrawVo crowdfundingInfoApplyDrawVo) {
		try {
			FeignResponse<DrawApplyCheckResult> response = cfFinanceDrawCashFeignClient.drawSplitApplyDrawCash(crowdfundingInfoApplyDrawVo);
			if (response == null) {
				log.error("finance服务异常");
				return NewResponseUtil.makeError(CfErrorCode.USER_IN_BLACKLIST);
			}
			return NewResponseUtil.makeResponse(response.getCode(), response.getMsg(), response.getData());
		} catch (Exception e) {
			log.error("finance服务异常,", e);
			return NewResponseUtil.makeError(CfErrorCode.USER_IN_BLACKLIST);
		}
	}
}
