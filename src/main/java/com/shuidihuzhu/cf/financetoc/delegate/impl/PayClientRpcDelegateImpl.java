package com.shuidihuzhu.cf.financetoc.delegate.impl;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.financetoc.delegate.IPayClientRpcDelegate;
import com.shuidihuzhu.client.baseservice.pay.model.PayInnerCallBack;
import com.shuidihuzhu.client.baseservice.pay.model.PayInnerCallBackInfo;
import com.shuidihuzhu.client.baseservice.pay.model.PayResultV2;
import com.shuidihuzhu.client.baseservice.pay.model.PayRpcResponse;
import com.shuidihuzhu.client.baseservice.pay.model.v3.PayInfoParamV3;
import com.shuidihuzhu.client.baseservice.pay.model.v3.combine.callback.CombineInnerCallBack;
import com.shuidihuzhu.client.baseservice.pay.model.v3.combine.callback.CombineInnerCallBackInfo;
import com.shuidihuzhu.client.baseservice.pay.v1.PayClientV1;
import com.shuidihuzhu.client.baseservice.pay.v3.CombinePayClientV3;
import com.shuidihuzhu.client.baseservice.pay.v3.PayClientV3;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

@Service(value = "payClientRpcDelegateImpl")
@Slf4j
@RefreshScope
public class PayClientRpcDelegateImpl implements IPayClientRpcDelegate {

    @Autowired
    private PayClientV1 payClientV1;

    @Autowired
    private PayClientV3 payClientV3;

    @Autowired
    private CombinePayClientV3 combinePayClientV3;

    @Override
    public PayInnerCallBack verifyCallback(PayInnerCallBackInfo payInnerCallBackInfo) {
        PayInnerCallBack response = payClientV1.verifyCallback(payInnerCallBackInfo);
        log.info(this.getClass().getSimpleName()+" verifyCallback param:{},response:{}", JSON.toJSONString(payInnerCallBackInfo),JSON.toJSONString(response));
        return response;
    }

    @Override
    public CombineInnerCallBack verifyCombineCallback(CombineInnerCallBackInfo combineInnerCallBackInfo) {
        CombineInnerCallBack callBack = combinePayClientV3.verifyCallback(combineInnerCallBackInfo);
        log.info(this.getClass().getSimpleName()+" verifyCombineCallback param:{},response:{}", JSON.toJSONString(combineInnerCallBackInfo),JSON.toJSONString(callBack));

        return callBack;
    }

    @Override
    public PayRpcResponse<PayResultV2> unifiedOrder(PayInfoParamV3 payInfoParamV3) {
        PayRpcResponse<PayResultV2> response = payClientV3.unifiedOrder(payInfoParamV3);
        log.info(this.getClass().getSimpleName()+" unifiedOrder param:{},response:{}",JSON.toJSONString(payInfoParamV3),JSON.toJSONString(response));
        return response;
    }
}
