package com.shuidihuzhu.cf.financetoc.delegate;

import com.shuidihuzhu.cf.finance.model.CfDonorRefundApply;
import com.shuidihuzhu.cf.finance.model.CfFinanceBcpModel;
import com.shuidihuzhu.cf.finance.model.vo.draw.CfAmountInfoVo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;
import java.util.Map;

public interface ICFFinanceFeignDelegate {

    /**
     * 自助下发退款
     *
     * @param userId
     * @param infoUuid
     * @param orderId
     * @return
     */
    Response cfRefundLaunchHelper(long userId, String infoUuid, long orderId);

    /**
     * 筹款人看到的金额详情
     *
     * @param infoUuid
     * @return
     */
    CfAmountInfoVo getFundraiserAmountInfo(String infoUuid);

    /**
     * 获取单笔退款订单状态
     *
     * @param orderIds
     * @return
     */
    Map<Long, CfDonorRefundApply.StatusEnum> getOrderRefundStatus(List<Long> orderIds);

    /**
     * 资金系统下单
     *
     * @param cfFinanceBcpModel
     * @return
     */
    boolean addOrderToFinance(CfFinanceBcpModel cfFinanceBcpModel);

    /**
     * 账号转移
     *
     * @param orderList
     * @param fromUserId
     * @return
     */
    int transferOrder(List<CrowdfundingOrder> orderList, long fromUserId);

    /**
     * 取消退款申请
     *
     * @param caseId    -
     * @param userId    -
     * @return -
     */
    Response<Void> cancelRefundApply(int caseId, long userId);
}
