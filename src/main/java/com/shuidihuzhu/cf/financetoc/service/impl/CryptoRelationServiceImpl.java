package com.shuidihuzhu.cf.financetoc.service.impl;

import com.shuidihuzhu.account.client.UserRelationV2FeignClient;
import com.shuidihuzhu.account.dto.UserRelationTypeMQModel;
import com.shuidihuzhu.account.enums.RelationContextEnum;
import com.shuidihuzhu.account.enums.RelationTypeEnum;
import com.shuidihuzhu.account.enums.SubRelationTypeEnum;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.constants.AsyncPoolConstants;
import com.shuidihuzhu.cf.constants.crowdfunding.status.RelationShip;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.financetoc.service.CryptoRelationService;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Created by zhoutian on 19/04/06.
 */

@Slf4j
@Service
public class CryptoRelationServiceImpl implements CryptoRelationService {

    @Resource
    UserRelationV2FeignClient userRelationV2FeignClient;


    @Override
    public void sendPayeeInfoRelation(CrowdfundingInfo crowdfundingInfo) {
        if (StringUtils.isEmpty(crowdfundingInfo.getPayeeIdCard()) ||
                StringUtils.isEmpty(crowdfundingInfo.getPayeeName()) ||
                crowdfundingInfo.getUserId() <= 0 ||
                null == crowdfundingInfo.getRelationType()) {
            log.warn("sendPayeeInfoRelation()方法调用失败传入的参数有误，参数为crowdfundingInfo:{}", crowdfundingInfo);
            return;
        }
        //将id转换为string类型
        String businessId = String.valueOf(crowdfundingInfo.getId());
        addRelationForRelationShip(crowdfundingInfo.getUserId(),
                crowdfundingInfo.getPayeeName(),
                crowdfundingInfo.getPayeeIdCard(),
                crowdfundingInfo.getRelationType(),
                RelationContextEnum.SDCF_PAYEE_INFO,
                businessId);
    }

    @Async(AsyncPoolConstants.SEND_CRYPTO_RELATION_MQ_POOL)
    private <T> void addRelationForRelationShip(long userId, String name, String cryptoIdCard, T relationEnum, RelationContextEnum relationContextEnum, String businessId) {
        if (null == relationContextEnum && StringUtils.isEmpty(businessId)) {

            return;
        }
        RelationTypeEnum relationTypeEnum = null;
        SubRelationTypeEnum subRelationTypeEnum = null;
        if (relationEnum != null) {
            relationTypeEnum = RelationTypeEnum.OTHER;
            subRelationTypeEnum = SubRelationTypeEnum.NONE;
            try {
                // 现对传入的枚举类型进行判断，也可使用insatnceof关键字
                if (relationEnum.getClass().equals(RelationShip.class)) {
                    relationTypeEnum = switchedRelationPram((RelationShip) relationEnum);
                    subRelationTypeEnum = switchedSubRelationPram((RelationShip) relationEnum);
                } else if (relationEnum.getClass().equals(CrowdfundingRelationType.class)) {
                    relationTypeEnum = switchedCFRelationPram((CrowdfundingRelationType) relationEnum);
                    subRelationTypeEnum = SubRelationTypeEnum.NONE;
                } else if (relationEnum.getClass().equals(UserRelTypeEnum.class)) {
                    relationTypeEnum = switchedUserRelationPram((UserRelTypeEnum) relationEnum);
                    subRelationTypeEnum = switchedUserSubRelationPram((UserRelTypeEnum) relationEnum);
                } else if (relationEnum.getClass().equals(RelationTypeEnum.class)) {
                    relationTypeEnum = (RelationTypeEnum) relationEnum;
                    subRelationTypeEnum = SubRelationTypeEnum.NONE;
                } else {
                    return;
                }
            } catch (ClassCastException e) {
                log.error("泛型类型转换异常，参数为T:{}", relationEnum, e);

            }
        }

        // 调用client端接口发送MQ
        try {
            UserRelationTypeMQModel userRelationTypeMqModel = new UserRelationTypeMQModel();
            userRelationTypeMqModel.setUserId(userId);
            userRelationTypeMqModel.setName(name);
            userRelationTypeMqModel.setCryptoIdCard(cryptoIdCard);
            userRelationTypeMqModel.setRelationType(relationTypeEnum);
            userRelationTypeMqModel.setSubRelationTypeEnum(subRelationTypeEnum);
            userRelationTypeMqModel.setContext(relationContextEnum);
            userRelationTypeMqModel.setBusinessId(businessId);
            userRelationV2FeignClient.sendUserRelationMq(userRelationTypeMqModel);
            log.info("addRelationForRelationShip方法调用成功,参数为，" +
                            "参数为userId:{},name:{},cryptoIdCard:{},relationEnum:{},subRelationTypeEnum:{},relationContextEnum:{},businessId:{}",
                    userId, name, cryptoIdCard, relationEnum, subRelationTypeEnum, relationContextEnum, businessId);
        } catch (Exception e) {
            log.error("添加身份关系失败,参数为，" +
                            "参数为userId:{},name:{},cryptoIdCard:{},relationEnum:{},subRelationTypeEnum:{},relationContextEnum:{},businessId:{}",
                    userId, name, cryptoIdCard, relationEnum, subRelationTypeEnum, relationContextEnum, businessId, e);
        }
    }

    private RelationTypeEnum switchedRelationPram(RelationShip relationShip) {
        if (null == relationShip) {
            return null;
        }
        switch (relationShip) {
            case FAMILY:
                return RelationTypeEnum.FAMILY;
            case FRIEND:
                return RelationTypeEnum.FRIENDS;
            case CO_WORKER:
                return RelationTypeEnum.WORK_MATE;
            case CLASS_MATE:
                return RelationTypeEnum.CLASS_MATE;
            case NEIGHBOURS:
                return RelationTypeEnum.NEIGHBOURS;
            case TEACHER:
                return RelationTypeEnum.TEACHER_STUDENT;
            case STUDENT:
                return RelationTypeEnum.TEACHER_STUDENT;
            case SICK_FRIEND:
                return RelationTypeEnum.SICK_FRIENDS;
            case HEALTH_CARE:
                return RelationTypeEnum.HEALTH_CARE;
            case OTHER:
                return RelationTypeEnum.OTHER;
            case TEACHER_STUDENT:
                return RelationTypeEnum.TEACHER_STUDENT;
            default:
                return RelationTypeEnum.OTHER;
        }
    }

    private SubRelationTypeEnum switchedSubRelationPram(RelationShip relationShip) {
        if (null == relationShip) {
            return null;
        }
        switch (relationShip) {
            case TEACHER:
                return SubRelationTypeEnum.TEACHER;
            case STUDENT:
                return SubRelationTypeEnum.STUDENT;
            default:
                return SubRelationTypeEnum.NONE;
        }
    }

    private RelationTypeEnum switchedCFRelationPram(CrowdfundingRelationType crowdfundingRelationType) {
        if (null == crowdfundingRelationType) {
            return null;
        }
        switch (crowdfundingRelationType) {
            case other:
                return RelationTypeEnum.OTHER;
            case self:
                return RelationTypeEnum.SELF;
            case parents:
                return RelationTypeEnum.PARENTS;
            case children:
                return RelationTypeEnum.CHILDREN;
            case brothers_or_sisters:
                return RelationTypeEnum.SIBLINGS;
            case spouse:
                return RelationTypeEnum.SPOUSE;
            case workmate:
                return RelationTypeEnum.WORK_MATE;
            case classmate:
                return RelationTypeEnum.CLASS_MATE;
            case FAMILY:
                return RelationTypeEnum.FAMILY;
            case FRIENDS:
                return RelationTypeEnum.FRIENDS;
            default:
                return RelationTypeEnum.OTHER;
        }
    }

    private RelationTypeEnum switchedUserRelationPram(UserRelTypeEnum userRelTypeEnum) {
        if (null == userRelTypeEnum) {
            return null;
        }
        switch (userRelTypeEnum) {
            case SELF:
                return RelationTypeEnum.SELF;
            case COUPON:
                return RelationTypeEnum.SPOUSE;
            case PARENT:
                return RelationTypeEnum.PARENTS;
            case CHILDREN:
                return RelationTypeEnum.CHILDREN;
            case GRANDCHILD:
                return RelationTypeEnum.GRANDCHILD;
            case GRANDPARENT:
                return RelationTypeEnum.GRANDPARENT;
            case SIBLING:
                return RelationTypeEnum.SIBLINGS;
            case OTHER:
                return RelationTypeEnum.OTHER;
            case NOTHING:
                return RelationTypeEnum.OTHER;
            case SELFFATHER:
                return RelationTypeEnum.PARENTS;
            case SELFMOTHER:
                return RelationTypeEnum.PARENTS;
            case COUPONFATHER:
                return RelationTypeEnum.COUPONFATHER;
            case COUPONMOTHER:
                return RelationTypeEnum.COUPONMOTHER;
            default:
                return RelationTypeEnum.OTHER;
        }
    }

    private SubRelationTypeEnum switchedUserSubRelationPram(UserRelTypeEnum userRelTypeEnum) {
        if (null == userRelTypeEnum) {
            return null;
        }
        switch (userRelTypeEnum) {
            case SELFFATHER:
                return SubRelationTypeEnum.FATHER;
            case SELFMOTHER:
                return SubRelationTypeEnum.MOTHER;
            default:
                return SubRelationTypeEnum.NONE;
        }
    }
}
