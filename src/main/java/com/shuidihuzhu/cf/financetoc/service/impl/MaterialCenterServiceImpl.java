package com.shuidihuzhu.cf.financetoc.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfFirstApproveClient;
import com.shuidihuzhu.cf.client.material.model.CfMaterialTypeEnum;
import com.shuidihuzhu.cf.client.material.model.materialField.MaterialExtKeyConst;
import com.shuidihuzhu.cf.client.material.model.mq.DoubleWriteMaterialModel;
import com.shuidihuzhu.cf.client.material.model.mq.MqTagsConstant;
import com.shuidihuzhu.cf.financetoc.service.IMaterialCenterService;
import com.shuidihuzhu.cf.financetoc.util.OldObjects;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
@RefreshScope
public class MaterialCenterServiceImpl implements IMaterialCenterService {
    @Autowired
    private CfFirstApproveClient firstApproveClient;
    @Autowired
    private Producer producer;

    @Override
    public void doubleWriteFirstApprove(CfFirsApproveMaterial material,
                                        List<String> userRequiredFields) {

        Map<String, String> extInfo = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(userRequiredFields)) {
            extInfo.put(MaterialExtKeyConst.REQUIRED_FIELD, JSON.toJSONString(userRequiredFields));
        }

        RpcResult<String> result = firstApproveClient.addOrUpdateFirstApprove(material, extInfo);
        sendDoubleWriteMsg(material.getInfoId(), CfMaterialTypeEnum.FIRST_APPROVE_MATERIAL, material, extInfo);
        log.info("前置信息保存 param:{} info:{} result:{}", material, extInfo, result);
    }

    private <T> void sendDoubleWriteMsg(int caseId, CfMaterialTypeEnum materialType, T entity, Map<String, String> extInfo) {

        if (entity == null) {
            log.info("实体为空不发消息 caseId:{} materialType:{} entity:{}", caseId, materialType, entity);
            return;
        }

        Message msg = new Message(MqTagsConstant.MQ_CF_TOPIC, MqTagsConstant.MQ_DOUBLE_WRITE_MATERIAL_CHECK,
                System.currentTimeMillis() + "_" + caseId,
                new DoubleWriteMaterialModel(caseId, materialType, entity,
                        OldObjects.firstNonNull(extInfo, Maps.newHashMap())), DelayLevel.S5);

        producer.send(msg);
    }
}
