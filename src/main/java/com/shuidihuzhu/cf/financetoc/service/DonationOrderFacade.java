package com.shuidihuzhu.cf.financetoc.service;

import com.shuidihuzhu.cf.model.contribute.ContributeAddOrderDTO;
import com.shuidihuzhu.cf.model.crowdfunding.vo.OrderResultVo;
import com.shuidihuzhu.cf.param.order.LoveHelpCombineOrderParam;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DonationOrderFacade {


    Response<OrderResultVo> createOrder(long userId, String infoUuid, double amt, String comment, String platform, String source, String selfTag, String showUrl,
                                        String returnUrl, Long userAddressId, Long gearId, Integer goodsCount, Boolean anonymous, Integer userThirdType,
                                        String splitFlowUuid, String shareSourceId, Integer payType, int activityId, boolean useSubsidy, int shareDv, String subActivityId,
                                        String clientIp, Integer osType, String channel, int blessingCardId, Integer contributeAmount, String bonfireUuid, List<LoveHelpCombineOrderParam> loveHelpCombineOrderParams);



    Response<OrderResultVo> createContributeOrder(ContributeAddOrderDTO contributeAddOrderDTO, long userId, String clientIp, Integer osType);

}
