package com.shuidihuzhu.cf.financetoc.service;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.activity.model.DonateCooperateFeeVO;
import com.shuidihuzhu.cf.constants.crowdfunding.RedisKeyCons;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.financetoc.util.SubsidyUtils;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ActivitySubsidyOrderService {

    @Resource(name = "cfShareViewRedissonHandler")
    private RedissonHandler redissonHandler;

    private static final long EXPIRE_TIME = RedissonHandler.ONE_HOUR;

    /**
     * 根据 订单id 记录的shareDv channel feeId
     *
     * @param activityId
     * @param orderId
     * @param donateCooperateFeeVO
     * @param shareDv
     * @param fromQQ
     * @return
     */
    public Response<Void> saveOrderSubsidy(int activityId, Long orderId, @Nullable DonateCooperateFeeVO donateCooperateFeeVO, int shareDv, boolean fromQQ) {
        log.info("saveOrderSubsidy orderId:{},shareDv:{}", orderId,shareDv);
        String feeId = Optional.ofNullable(donateCooperateFeeVO).map(DonateCooperateFeeVO::getFeeId).orElse("");
        PaySuccessSubsidyModelV2 e = new PaySuccessSubsidyModelV2();
        e.setChannelType(SubsidyUtils.parseChannel(shareDv).getCode());
        e.setShareDv(shareDv);
        e.setFeeId(feeId);
        e.setActivityId(activityId);
        e.setFromQQ(fromQQ);
        boolean success = false;
        try {
            success = redissonHandler.setNX(getSubsidyKeyV2(orderId), JSON.toJSONString(e), EXPIRE_TIME);
        } catch (Exception ex) {
            log.error("saveOrderSubsidy 设置redis超时, orderId:{}", orderId);
        }
        Response<Void> saveResp = success ? NewResponseUtil.makeSuccess(null) : NewResponseUtil.makeError(CfErrorCode.SYSTEM_ERROR);
        log.info("saveOrderSubsidy success={}", success);
        return saveResp;
    }

    /**
     * 上线一小时后可删旧代码
     * @param orderId
     * @return
     */
    public PaySuccessSubsidyModelV2 getOrderSubsidyModel(Long orderId) {
        String json = redissonHandler.get(getSubsidyKeyV2(orderId), String.class);
        if (StringUtils.isNotBlank(json)) {
            return JSON.parseObject(json, PaySuccessSubsidyModelV2.class);
        }
        PaySuccessSubsidyModel paySuccessSubsidyModel = redissonHandler.get(getSubsidyKey(orderId), PaySuccessSubsidyModel.class);
        if (paySuccessSubsidyModel == null) {
            return null;
        }
        PaySuccessSubsidyModelV2 view = new PaySuccessSubsidyModelV2();
        BeanUtils.copyProperties(paySuccessSubsidyModel, view);
        return view;
    }

    private String getSubsidyKey(Long orderId) {
        return RedisKeyCons.ShareView.ORDER + orderId;
    }

    private String getSubsidyKeyV2(Long orderId) {
        return RedisKeyCons.ShareView.ORDER_V2 + orderId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PaySuccessSubsidyModel {

        private CrowdfundingOrder order;

        private int shareDv;

        private int channelType;

        private String feeId;

        @ApiModelProperty("小善日活动id")
        private int activityId;

    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PaySuccessSubsidyModelV2 extends PaySuccessSubsidyModel{

        @ApiModelProperty("是否来自qq")
        private boolean fromQQ;

    }

}
