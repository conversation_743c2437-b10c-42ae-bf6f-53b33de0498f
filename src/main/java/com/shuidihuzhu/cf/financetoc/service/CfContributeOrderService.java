package com.shuidihuzhu.cf.financetoc.service;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.financetoc.dao.order.CfContributeOrderDao;
import com.shuidihuzhu.cf.model.contribute.CfContributeMsgBody;
import com.shuidihuzhu.cf.model.contribute.CfContributeOrder;
import com.shuidihuzhu.cf.model.contribute.CfPayInnerCallBack;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageQueueSelector;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageQueue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class CfContributeOrderService {

    @Autowired
    private CfContributeOrderDao contributeOrderDao;
    @Autowired
    private Producer producer;

    public int updateUserByIds(List<Long> ids, long toUserId) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }

        return contributeOrderDao.updateUserByIds(ids, toUserId);
    }

    public List<CfContributeOrder> selectByUserIds(List<Long> userIds, List<Integer> statusList) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }

        List<CfContributeOrder> resultList = Lists.newArrayList();
        Lists.partition(userIds, 50).forEach(ids -> {
                    resultList.addAll(contributeOrderDao.selectByUserIds(ids, statusList));
                }
        );


        return resultList;
    }


    public int addOrder(CfContributeOrder contributeOrder) {
        if (contributeOrder == null) {
            return 0;
        }
        return contributeOrderDao.addContributeOrder(contributeOrder);
    }

    public int updatePaySuccess(CfPayInnerCallBack callBack) {
        int fee = MoneyUtil.multiply("" + callBack.getAmountInFen(), "0.006", 0, RoundingMode.HALF_UP).intValue();
        return contributeOrderDao.updatePaySuccess(
                callBack.getOrderId(),
                callBack.getPayUid(),
                CfContributeOrder.OrderStatus.PAY_SUCCESS.getCode(),
                new Date(callBack.getBusinessTime().getTime()),
                callBack.getAmountInFen(),
                fee
        );
    }

    public void sendContributeActionMsg(String payUid, CfContributeMsgBody.OrderAction action) {
        if (StringUtils.isBlank(payUid) || action == null) {
            return;
        }

        // 发消息
        Message msg = new Message(MQTopicCons.CF, MQTagCons.CF_CONTRIBUTE_ORDER_TAG, payUid,
                CfContributeMsgBody.builder().payUid(payUid).action(action.getCode()).build());

        MessageResult result = producer.send(msg, new MessageQueueSelector() {
            @Override
            public MessageQueue select(List<MessageQueue> messageQueues, Message message) {
                int hashCode = ((CfContributeMsgBody) message.getPayload()).getPayUid().hashCode();
                return messageQueues.get(Math.abs(hashCode) % messageQueues.size());
            }
        });

        log.info("捐赠消息发送msg:{} result:{}", msg, result);
    }

    public CfContributeOrder selectByPayUid(String payUid) {
        if (StringUtils.isBlank(payUid)) {
            return null;
        }
        List<CfContributeOrder> list = selectByPayUids(Lists.newArrayList(payUid));

        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    public List<CfContributeOrder> selectByPayUids(List<String> payUids) {
        if (CollectionUtils.isEmpty(payUids)) {
            return Lists.newArrayList();
        }

        return contributeOrderDao.selectByPayUids(payUids);
    }
}
