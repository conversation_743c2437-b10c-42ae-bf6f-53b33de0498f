package com.shuidihuzhu.cf.financetoc.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfPayeeMaterialClient;
import com.shuidihuzhu.cf.client.material.model.materialField.MaterialExtKeyConst;
import com.shuidihuzhu.cf.financetoc.service.IPayeeMaterialCenterService;
import com.shuidihuzhu.cf.model.crowdfunding.CfCharityPayee;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoHospitalPayee;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;

@Slf4j
@Service
public class PayeeMaterialCenterServicelmpl implements IPayeeMaterialCenterService {

    @Autowired
    private CfPayeeMaterialClient payeeMaterialClient;

    @Override
    public void addOrUpdateCharity(int caseId, CfCharityPayee charity, Set<String> requireNames) {
        Map<String, String> extInfo = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(requireNames)) {
            extInfo.put(MaterialExtKeyConst.REQUIRED_FIELD, JSON.toJSONString(requireNames));
        }

        RpcResult<String> result = payeeMaterialClient.addOrUpdateCharity(caseId, charity, extInfo);
        log.info("保存慈善组织收款人材料.caseId:{} param:{} result:{}", caseId, charity, result);
    }

    @Override
    public void addOrUpdateHospitalAccount(int caseId, CrowdfundingInfoHospitalPayee hospitalPayee, Set<String> requireNames) {
        Map<String, String> extInfo = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(requireNames)) {
            extInfo.put(MaterialExtKeyConst.REQUIRED_FIELD, JSON.toJSONString(requireNames));
        }

        RpcResult<String> result = payeeMaterialClient.addOrUpdateHospitalAccount(caseId, hospitalPayee, extInfo);
        log.info("保存医院账号收款人材料.caseId:{} param:{} result:{}", caseId, hospitalPayee, result);
    }

    @Override
    public void addOrUpdatePersonAccount(int caseId, CrowdfundingInfoPayee infoPayee, Set<String> requireNames) {
        Map<String, String> extInfo = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(requireNames)) {
            extInfo.put(MaterialExtKeyConst.REQUIRED_FIELD, JSON.toJSONString(requireNames));
        }

        RpcResult<String> result = payeeMaterialClient.addOrUpdatePersonAccount(caseId, infoPayee, extInfo);
        log.info("保存收款人材料.caseId:{} param:{} result:{}", caseId, infoPayee, result);
    }
}
