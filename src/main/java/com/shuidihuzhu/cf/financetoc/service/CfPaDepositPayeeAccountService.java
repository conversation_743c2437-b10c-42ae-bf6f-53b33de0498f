package com.shuidihuzhu.cf.financetoc.service;

import com.shuidihuzhu.cf.finance.model.deposit.CfPayeeDepositAccount;
import com.shuidihuzhu.common.web.model.Response;

/**
 * @Author: l<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019/12/16 14:14
 */
public interface CfPaDepositPayeeAccountService {

	/**
	 * 开户成功的资金账户
	 *
	 * @param payeeName
	 * @param bankCard
	 * @param idCard
	 * @return
	 */
	Response<CfPayeeDepositAccount> getOpenSuccessAccount(String payeeName, String bankCard, String idCard);
}
