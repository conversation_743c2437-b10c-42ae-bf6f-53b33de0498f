package com.shuidihuzhu.cf.financetoc.service;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.CfFinishStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.UserTagGroup;
import com.shuidihuzhu.cf.vo.UserInfoVo;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Ahrievil
 * @date : 2018/5/16 01:00
 * 为了解决某些业务需要绑定结束案例这个事件而创建的类
 */
@Slf4j
@Service
public class CrowdfundingFinishService {

    @Autowired(required = false)
    private Producer producer;

    public void addTag(long userId,String infoId, CfFinishStatus cfFinishStatus) {
        try {
            UserInfoVo userInfoVo = new UserInfoVo(userId, infoId, cfFinishStatus);
            userInfoVo.setUserTagGroup(UserTagGroup.END_CASE);
            log.info("add tag to end case user userInfoVo:{}", userInfoVo);
            producer.send(new Message<>(MQTopicCons.CF,
                    MQTagCons.CF_ADD_WX_TAG_TO_USER, MQTagCons.CF_ADD_WX_TAG_TO_USER + "-" + infoId
                    + "-" + System.currentTimeMillis(), userInfoVo, DelayLevel.S1));
        } catch (Exception e) {
            log.error("CrowdfundingFinishService addTag", e);
        }
    }
}
