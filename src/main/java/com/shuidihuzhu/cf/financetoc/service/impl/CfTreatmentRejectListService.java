package com.shuidihuzhu.cf.financetoc.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.financetoc.service.ICfMaterialRejectListService;
import com.shuidihuzhu.cf.vo.v5.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
public class CfTreatmentRejectListService implements ICfMaterialRejectListService {



    @Override
    public List<CfMaterialAuditListView.AuditModifyEntry> getRejectModifyEntry(CfMaterialParam param) {

        List<CfMaterialAuditListView.AuditModifyEntry> allEntry = Lists.newArrayList();
        Map<Integer, Set<String>> rejectDetails = param.getRejectDetails();

        if (MapUtils.isEmpty(rejectDetails)) {
            log.warn("案例的诊断证明是驳回/修改状态不能找到驳回理由.infoUuid:{}", param.getInfoUuid());
            return allEntry;
        }

        int entryStatus = getEntryStatus(param.getMaterialAuditStatus());
        int suggestStatus = getEntrySuggestStatus(param.getMaterialAuditStatus());

        Set<Integer> suggestCodes = getSuggestCodes(param.getSuggestViews());

        if (rejectDetails.containsKey(MaterialRejectPositionType.DISEASE.getCode())
                || rejectDetails.containsKey(MaterialRejectPositionType.HOSPITAL_NAME.getCode())) {

            CfMaterialAuditListView.AuditModifyEntry modifyEntry = new CfMaterialAuditListView.AuditModifyEntry(
                    MaterialAuditEntry.HOSPITAL_INFO, entryStatus);
            List<CfMaterialAuditListView.FieldItemStatus> allFields = Lists.newArrayList();
            Map<Integer, Set<String>> curEntryRejects = Maps.newHashMap();

            if (rejectDetails.containsKey(MaterialRejectPositionType.DISEASE.getCode())) {
                allFields.add(new CfMaterialAuditListView.FieldItemStatus(CfMaterialFieldName.disease, entryStatus));
                curEntryRejects.put(MaterialRejectPositionType.DISEASE.getCode(),
                        rejectDetails.get(MaterialRejectPositionType.DISEASE.getCode()));
            }

            if (rejectDetails.containsKey(MaterialRejectPositionType.HOSPITAL_NAME.getCode())) {
                allFields.add(new CfMaterialAuditListView.FieldItemStatus(CfMaterialFieldName.diagnosisRegion, entryStatus));
                allFields.add(new CfMaterialAuditListView.FieldItemStatus(CfMaterialFieldName.diagnosisHospital, entryStatus));
                curEntryRejects.put(MaterialRejectPositionType.HOSPITAL_NAME.getCode(),
                        rejectDetails.get(MaterialRejectPositionType.HOSPITAL_NAME.getCode()));
            }

            modifyEntry.setAllFields(allFields);

            allEntry.add(modifyEntry);
        }

        if (hasTreatmentSuggest(suggestCodes)) {
            CfMaterialAuditListView.AuditModifyEntry modifyEntry = new CfMaterialAuditListView.AuditModifyEntry(
                    MaterialAuditEntry.MEDICAL_TREATMENT_SUGGEST, suggestStatus);
            List<CfMaterialAuditListView.FieldItemStatus> allFields = getMedicalItemstatus(suggestCodes, suggestStatus);
            modifyEntry.setAllFields(allFields);

//            modifyEntry.setModifySuggestDetails(param.getSuggestViews());
            allEntry.add(modifyEntry);

        } else if (rejectDetails.containsKey(MaterialRejectPositionType.MEDICAL_TREATMENT.getCode())) {
            CfMaterialAuditListView.AuditModifyEntry modifyEntry = new CfMaterialAuditListView.AuditModifyEntry(
                    MaterialAuditEntry.MEDICAL_TREATMENT, entryStatus);
            List<CfMaterialAuditListView.FieldItemStatus> allFields = Lists.newArrayList(
                    new CfMaterialAuditListView.FieldItemStatus(CfMaterialFieldName.medicalTreat, entryStatus));

            modifyEntry.setAllFields(allFields);
            Map<Integer, Set<String>> curEntryRejects = Maps.newHashMap();
            curEntryRejects.put(MaterialRejectPositionType.MEDICAL_TREATMENT.getCode(),
                    rejectDetails.get(MaterialRejectPositionType.MEDICAL_TREATMENT.getCode()));

            allEntry.add(modifyEntry);
        }

        return allEntry;
    }


    private List<CfMaterialAuditListView.FieldItemStatus> getMedicalItemstatus(Set<Integer> suggestCodes, int suggestStatus) {
        List<CfMaterialAuditListView.FieldItemStatus> allFields = Lists.newArrayList();
        if (suggestCodes.contains(MaterialModifySuggestType.HOSPITALIZATION_PROVE.getCode())) {
            allFields.add(new CfMaterialAuditListView.FieldItemStatus(CfMaterialFieldName.hospitalizationProve,
                    suggestStatus));
        }

        if (suggestCodes.contains(MaterialModifySuggestType.LEAVE_HOSPITAL_PROVE.getCode())) {
            allFields.add(new CfMaterialAuditListView.FieldItemStatus(CfMaterialFieldName.leaveHospital, suggestStatus));
        }

        if (suggestCodes.contains(MaterialModifySuggestType.DIAGNOSIS_PROVE.getCode())) {
            allFields.add(new CfMaterialAuditListView.FieldItemStatus(CfMaterialFieldName.diagnosisProve, suggestStatus));
        }

        if (suggestCodes.contains(MaterialModifySuggestType.CHARGE_NOTE.getCode())) {
            allFields.add(new CfMaterialAuditListView.FieldItemStatus(CfMaterialFieldName.changeNote, suggestStatus));
        }

        return allFields;
    }

    private boolean hasTreatmentSuggest(Set<Integer> suggestCodes) {

        if (CollectionUtils.isEmpty(suggestCodes)) {
            return false;
        }

        return suggestCodes.contains(MaterialModifySuggestType.HOSPITALIZATION_PROVE.getCode())
                || suggestCodes.contains(MaterialModifySuggestType.LEAVE_HOSPITAL_PROVE.getCode())
                || suggestCodes.contains(MaterialModifySuggestType.DIAGNOSIS_PROVE.getCode())
                || suggestCodes.contains(MaterialModifySuggestType.CHARGE_NOTE.getCode());
    }
}
