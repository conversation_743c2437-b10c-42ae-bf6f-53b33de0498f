package com.shuidihuzhu.cf.financetoc.service;

import brave.Span;
import brave.Tracer;
import com.google.common.base.Ticker;
import com.google.common.cache.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * update by zhangzhi 20180319
 */
@Slf4j
public abstract class AbstractCfCache<K, V> {

    private static final int nThreads = 4;
    private static final int DEFAULT_CAPACITY = 1000;
    private static final int DEFAULT_MAXIMUM_SIZE = 3000;
    private static final int DEFAULT_EXPIRE_AFTER_WRITE = 2 * 60;//原来默认是两分钟丢弃,同步获取新值；
    private static final int DEFAULT_TIME_REFRESH_AFTER_WRITE = 2 * 60;//现在默认两分钟异步刷新;

    private volatile LoadingCache<K, V> cache;
    private final byte[] lock = new byte[0];
    @Autowired
    private Tracer tracer;

    public LoadingCache<K, V> getCache() {
        if (this.cache == null) {
            synchronized (lock) {
                if (this.cache == null) {
                    int initialCapacity = this.getInitialCapacity();
                    int maximumSize = this.getMaximumSize();
                    int expireTimeInSeconds = this.getExpireTimeInSeconds();
                    cache = CacheBuilder.newBuilder()
                            .concurrencyLevel(nThreads).initialCapacity(initialCapacity).maximumSize(maximumSize)
                            //expireAfterWrite指定时间过后,expireAfterWrite是remove该key,下次访问是同步去获取返回新值;
                            .expireAfterWrite(expireTimeInSeconds, TimeUnit.SECONDS)
                            //而refreshAfterWrite则是指定时间后,不会remove该key,下次访问会触发刷新,新值没有回来时返回旧值
//                            .refreshAfterWrite(refreshTimeInSeconds, TimeUnit.SECONDS)
                            .ticker(Ticker.systemTicker()).recordStats()
                            .removalListener(new RemovalListener<Object, Object>() {
                                @Override
                                public void onRemoval(RemovalNotification<Object, Object> notification) {

                                }
                            })
                            .build(
                                    new CacheLoader<K, V>() {
                                        @Override
                                        public V load(K key) throws Exception {
                                            log.debug("theload-key={}", key);
                                            V v = queryData(key);
                                            return v;
                                        }

//                                        @Override
//                                        public ListenableFuture<V> reload(K key, V oldValue) throws Exception {
//                                            ListenableFutureTask<V> task = ListenableFutureTask.create(new Callable<V>() {
//                                                @Override
//                                                public V call() throws Exception {
//                                                    String spanName = SpanNameUtil.toLowerHyphen(this.getClass().getName());
//                                                    Span span = startOrContinueRenamedSpan(spanName);
//                                                    try (Tracer.SpanInScope ws = tracer.withSpanInScope(span.start())) {
//                                                        log.debug("reload-key={}", key);
//                                                        V v = queryData(key);
//                                                        return v;
//                                                    } finally {
//                                                        span.finish();
//                                                    }
//                                                }
//                                            });
//                                            tracingExecutors.execute(task);
//                                            return task;
//                                        }
                                    }
                            );
                }
            }
        }
        return cache;
    }

    protected abstract V queryData(@NotNull final K key);

    protected V getValue(@NotNull final K key) throws ExecutionException {
        return this.getCache().get(key);
    }

    protected void put(@NotNull final K key, @NotNull final V value) throws ExecutionException {
        this.getCache().put(key, value);
    }

    protected void refreshCache(List<String> keys) {
        this.getCache().invalidateAll(keys);
    }

    private CacheStats getCacheStat(LoadingCache loadingCache) {
        return loadingCache.stats();
    }

    protected int getInitialCapacity() {
        return DEFAULT_CAPACITY;
    }

    protected int getMaximumSize() {
        return DEFAULT_MAXIMUM_SIZE;
    }

    protected int getExpireTimeInSeconds() {
        return DEFAULT_EXPIRE_AFTER_WRITE;
    }

    protected int getRefreshTimeAfterWrite() {
        return DEFAULT_TIME_REFRESH_AFTER_WRITE;
    }

    private Span startOrContinueRenamedSpan(String spanName) {
        Span currentSpan = this.tracer.currentSpan();
        if (currentSpan != null) {
            return currentSpan.name(spanName);
        }
        return this.tracer.nextSpan().name(spanName);
    }
}

