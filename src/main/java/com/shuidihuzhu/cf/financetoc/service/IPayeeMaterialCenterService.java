package com.shuidihuzhu.cf.financetoc.service;

import com.shuidihuzhu.cf.model.crowdfunding.CfCharityPayee;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoHospitalPayee;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee;

import java.util.Set;

public interface IPayeeMaterialCenterService {

    void addOrUpdateCharity(int caseId, CfCharityPayee charity, Set<String> requireNames);

    void addOrUpdateHospitalAccount(int caseId, CrowdfundingInfoHospitalPayee hospitalPayee, Set<String> requireNames);

    void addOrUpdatePersonAccount(int caseId, CrowdfundingInfoPayee infoPayee, Set<String> requireNames);
}
