package com.shuidihuzhu.cf.financetoc.service.impl;

import com.shuidihuzhu.cf.app.constant.ApolloConstant;
import com.shuidihuzhu.cf.financetoc.service.IOssToCosTransService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URL;
import java.util.Random;


@Service
@RefreshScope
@Slf4j
public class OssToCosTransServiceImpl implements IOssToCosTransService {

    private static final Random random = new Random();
    /**
     * 流控比例，100为全量，0为关闭
     */
    private static final int ossToCosPercentage = 100;
    /**
     * 流控比例，100为全量，0为关闭
     */
    private static final int cosToOssPercentage = 0;

    @Resource
    private ApolloConstant apolloConstant;

    private String getDomain(String url) {
        if (org.apache.commons.lang3.StringUtils.isBlank(url)) {
            return url;
        } else {
            int length = url.length();
            if (url.startsWith("https")) {
                return parseUrl(url, length, 8);
            } else {
                return url.startsWith("http") ? parseUrl(url, length, 7) : parseUrl(url, length, 0);
            }
        }
    }

    private String parseUrl(String url, int length, int start) {
        if (org.apache.commons.lang3.StringUtils.isBlank(url)) {
            return url;
        } else {
            int end = url.indexOf(47, start);
            if (end < 0 || end > length) {
                end = url.indexOf(63);
                if (end < 0 || end > length) {
                    end = length;
                }
            }

            return url.substring(start, end);
        }
    }

    @Override
    public String convertToCosUrl(String ossUrl) {
        if (StringUtils.isBlank(ossUrl)){
            return ossUrl;
        }
        //oss转cos
        if (ossToCosPercentage != 0) {
            //判断图片url 是否是oss cf-images下的域名，如果不是，不处理.
            String domain = this.getDomain(ossUrl);
            if (StringUtils.isBlank(domain)){
                return ossUrl;
            }

            if ( !domain.equals(apolloConstant.getCosImageCdn())
                    && !domain.equals(apolloConstant.getOssImageCdn())){
                return ossUrl;
            }
            int percentage = Math.abs(random.nextInt(100));
            try {
                if (percentage >= this.ossToCosPercentage) {
                    if (apolloConstant.getCosImageCdn().equals(domain)) {
                        return ossUrl;
                    }
                    URL url = new URL(ossUrl);
                    String cosUrl = "https://" + apolloConstant.getCosImageCdn() + url.getPath();
                    if (!StringUtils.isEmpty(url.getQuery())) {
                        cosUrl = cosUrl + url.getQuery();
                    }
                    return cosUrl;
                }
                if (StringUtils.isBlank(ossUrl)) {
                    return ossUrl;
                }
                URL url = new URL(ossUrl);
                String newUrl = apolloConstant.getOssToCosDefaultCdnDomain() + url.getPath();
                if (!StringUtils.isEmpty(url.getQuery())) {
                    newUrl = newUrl + url.getQuery();
                }
                return newUrl;

            } catch (Exception e) {
                log.error("oss to cos invalid url. url:{};", ossUrl, e);
            }
        }
        //cos转oss 回滚用
        if (cosToOssPercentage != 0) {
            //判断图片url 是否是cos域名
            String domain = this.getDomain(ossUrl);
            if (StringUtils.isBlank(domain)) {
                return ossUrl;
            }
            if (!apolloConstant.getCosImageCdn().equals(domain) && !apolloConstant.getOriginImageCdn().equals(domain)) {
                return ossUrl;
            }
            int percentage = Math.abs(random.nextInt(100));
            if (percentage >= cosToOssPercentage) {
                return ossUrl;
            }

            if (StringUtils.isBlank(ossUrl)) {
                return ossUrl;
            }

            try {
                URL url = new URL(ossUrl);
                String newUrl = apolloConstant.getCosToOssDefaultCdnDomain() + url.getPath();

                if (!StringUtils.isEmpty(url.getQuery())) {
                    newUrl = newUrl + url.getQuery();
                }
                return newUrl;

            } catch (Exception e) {
                log.error("cos to oss invalid url. url:{};", ossUrl, e);
            }
        }

        return ossUrl;
    }
}
