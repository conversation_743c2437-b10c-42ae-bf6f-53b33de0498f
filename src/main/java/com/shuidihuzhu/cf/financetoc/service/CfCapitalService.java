package com.shuidihuzhu.cf.financetoc.service;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.finance.model.CfFinanceBcpModel;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.financetoc.biz.order.CrowdfundingOrderBiz;
import com.shuidihuzhu.cf.financetoc.biz.order.CrowdfundingPayRecordBiz;
import com.shuidihuzhu.cf.financetoc.delegate.ICFFinanceFeignDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord;
import com.shuidihuzhu.client.baseservice.pay.model.PayRpcResponse;
import com.shuidihuzhu.client.baseservice.pay.model.QueryOrderResult;
import com.shuidihuzhu.client.baseservice.pay.model.v3.QueryParamV3;
import com.shuidihuzhu.client.baseservice.pay.v3.PayQueryClientV3;
import com.shuidihuzhu.common.web.enums.PayStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

@Service
@Slf4j
public class CfCapitalService {
	public static final Logger LOGGER = LoggerFactory.getLogger(CfCapitalService.class);

	@Autowired
	private CfInfoMirrorService cfInfoMirrorService;
	@Autowired
	private PayCallbackService payCallbackService;
	@Autowired(required = false)
	private Producer producer;
	@Autowired
	private ICFFinanceFeignDelegate cfFinanceFeignDelegate;
	@Autowired
	private OrderOperateService orderOperateService;
	@Autowired
	private PayQueryClientV3 payQueryClientV3;
	@Autowired
	private CrowdfundingOrderBiz crowdfundingOrderBiz;
	@Autowired
	private CrowdfundingPayRecordBiz crowdfundingPayRecordBiz;
	@Autowired
	private CrowdfundingInfoBiz crowdfundingInfoBiz;
	@Autowired
	private ActivitySubsidyOrderService subsidyOrderService;

	public void paySuccess(CrowdfundingInfo crowdfundingInfo, CrowdfundingOrder order, int realAmount, String payUid, String thirdPayUid, int payBizType,int operatorId,
	                       int feeAmountInFen, String feePercent) {
		LOGGER.info("paySuccess 开始执行 案例id:{},amount:{},payUid:{},thirdPayUid:{}", order.getCrowdfundingId(), realAmount, payUid, thirdPayUid);
		try {
			//1，更新crowdfunding_pay_record_orderid_sharding_000分表
			orderOperateService.updatePayRecordSharding(payUid, PayStatus.PAY_SUCCESS.getCode(), order.getPayTime(), realAmount);
			//2，更新crowdfunding_order_crowdfundingid_sharding_xxx分表
			orderOperateService.updateCrowdfundingOrderSharding(order.getId(), PayStatus.PAY_SUCCESS.getCode(), order.getPayTime());
//			设置订单状态
			order.setPayStatus(PayStatus.PAY_SUCCESS.getCode());
			// 事务
			boolean result = payCallbackService.addAmount(crowdfundingInfo, realAmount);
			payCallbackService.stopCase(crowdfundingInfo, realAmount,operatorId);
			if (payCallbackService.addAmountComplete(crowdfundingInfo, realAmount)) {
				LOGGER.info("paySuccess cfInfoMirrorService.before begin:{}", order.getCrowdfundingId());
				CfOperatingRecord cfOperatingRecord = this.cfInfoMirrorService.before(crowdfundingInfo, crowdfundingInfo.getUserId(),
						crowdfundingInfo.getPayeeName(), CfOperatingRecordEnum.Type.AMOUNT_FULL, CfOperatingRecordEnum.Role.SYSTEM);
				this.cfInfoMirrorService.after(cfOperatingRecord);
			}
			LOGGER.info("paySuccess result:{}", result);
			if (result) {
				// 捐款成功 发送更新redis和cf_info_stat表的mq
				producer.send(new Message(MQTopicCons.CF, MQTagCons.PAY_SUCCESS_TO_UPDATE,
						MQTagCons.PAY_SUCCESS_TO_UPDATE + order.getId(), order));
				try {
					//判空
					if (StringUtils.isBlank(thirdPayUid)) {
						thirdPayUid = "";
					}
					CfFinanceBcpModel cfFinanceBcpModel = CfFinanceBcpModel.builder()
							.orderId(order.getId()).caseId(crowdfundingInfo.getId()).infoUuid(crowdfundingInfo.getInfoId())
							.payUid(payUid).payBizType(payBizType).thirdPayUid(thirdPayUid).payTime(order.getPayTime())
							.payAmountInFen(realAmount).batch(0).remark("支付回调").userId(order.getUserId())
							.feeAmount(feeAmountInFen)
							.feePercent(feePercent)
							.build();
					boolean res = cfFinanceFeignDelegate.addOrderToFinance(cfFinanceBcpModel);
					if (!res) {
						log.error("【financeV3】下单失败！caseId:{},payUid:{}", crowdfundingInfo.getId(), payUid);
					} else {
						log.info("【financeV3】下单成功！caseId:{},payUid:{}", crowdfundingInfo.getId(), payUid);
					}
				} catch (Exception e) {
					log.error("【financeV3】下单失败！服务异常,", e);
				}
			}
		} catch (Exception e) {
			LOGGER.error("支付回调", e);
		}
		LOGGER.info("paySuccess 开始完毕 案例id:{},amount:{},payUid:{}", order.getCrowdfundingId(), realAmount, payUid);
	}


	/**
	 * 1分钟补单
	 * 1、资金补单
	 * 2、olap支付金额统计
	 *
	 * @param orderId
	 * @param caseId
	 * @param infoUuid
	 * @param payUid
	 * @param thirdPayUid            支付中心单号
	 * @param callBackPayAmountInFen 支付回调金额
	 */
	@Async
	public void sendPayCheckMQ(long orderId, long userId, int caseId, String infoUuid, String payUid, String thirdPayUid,
							   int payBizType, int callBackPayAmountInFen, Date paySuccessTime,
							   int feeAmountInFen, String feePercent) {
		/**
		 * 捐款成功5分钟后校验一下对应数据记录的正确性
		 */
		if (StringUtils.isBlank(thirdPayUid)) {
			thirdPayUid = "";
		}
		CfFinanceBcpModel cfFinanceBcpModel = CfFinanceBcpModel.builder()
				.orderId(orderId).userId(userId).caseId(caseId).infoUuid(infoUuid)
				.payUid(payUid).payBizType(payBizType).thirdPayUid(thirdPayUid).payTime(paySuccessTime)
				.payAmountInFen(callBackPayAmountInFen).batch(0).remark("下单校验")
				.feePercent(feePercent)
				.feeAmount(feeAmountInFen)
				.build();
		log.info("发送补单mq:{}", cfFinanceBcpModel);
		try {
			producer.send(new Message(MQTopicCons.CF, MQTagCons.CF_PAY_SUCCESS_BCP_V1,
					MQTagCons.CF_PAY_SUCCESS_BCP_V1 + "_" + cfFinanceBcpModel.getOrderId(), cfFinanceBcpModel, DelayLevel.M1));
		} catch (Exception e) {
			LOGGER.error("checkout updateOrder method orderId={}", cfFinanceBcpModel.getOrderId(), e);
		}

	}

	@Async
	public void sendMQ(CrowdfundingOrder successOrder) {

		try {
			//获取订单记录的shareDv
			ActivitySubsidyOrderService.PaySuccessSubsidyModelV2 orderSubsidyModel = subsidyOrderService.getOrderSubsidyModel(successOrder.getId());
			if(orderSubsidyModel!=null){
				log.info("pay success msg shareDv:{},caseId:{}",orderSubsidyModel.getShareDv(),successOrder.getCrowdfundingId());
				successOrder.setShareDv(orderSubsidyModel.getShareDv());
			}

			// 单独捐款成功发送消息  如果后期还需要加消息 @see PayCallbackMsgConsumer
			producer.send(new Message(MQTopicCons.CF, MQTagCons.CF_PAY_ONLY_SUCCESS_MSG,
					MQTagCons.CF_PAY_ONLY_SUCCESS_MSG + "_" + successOrder.getId(), successOrder));

		} catch (Exception e) {
			LOGGER.error("send Pay succ msg Error!", e);
		}

	}

	public void sendPayCheckMQ(long crowdfundingOrderId) {
		CrowdfundingOrder crowdfundingOrder = crowdfundingOrderBiz.getById(crowdfundingOrderId);
		if (crowdfundingOrder == null) {
			return;
		}
		CrowdfundingPayRecord crowdfundingPayRecord = crowdfundingPayRecordBiz.getByOrderId(crowdfundingOrderId);
		if (crowdfundingPayRecord == null) {
			return;
		}
		QueryParamV3 queryParamV3 = new QueryParamV3();
		queryParamV3.setOrderNo(crowdfundingPayRecord.getPayUid());
		queryParamV3.setClientId(16);
		log.info("resultPayRpcResponse：orderId:{}", crowdfundingOrderId);
		PayRpcResponse<QueryOrderResult> resultPayRpcResponse = payQueryClientV3.queryOrderStatusWithoutThirdService(queryParamV3);
		log.info("resultPayRpcResponse:{}, orderId:{}", JSON.toJSONString(resultPayRpcResponse), crowdfundingOrderId);
		if (resultPayRpcResponse == null) {
			return;
		}
		QueryOrderResult queryOrderResult = null;
		if (resultPayRpcResponse.isSuccess()) {
			queryOrderResult = resultPayRpcResponse.getResult();
		}
		if (queryOrderResult == null) {
			return;
		}
		CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(crowdfundingOrder.getCrowdfundingId());
		if (crowdfundingInfo == null) {
			return;
		}
		//资金系统补单MQ
		this.sendPayCheckMQ(crowdfundingPayRecord.getCrowdfundingOrderId(), crowdfundingOrder.getUserId(),
				crowdfundingOrder.getCrowdfundingId(), crowdfundingInfo.getInfoId(), crowdfundingPayRecord.getPayUid(),
				queryOrderResult.getPayUid(), queryOrderResult.getWxBizType(), queryOrderResult.getRealAmount(),
				crowdfundingOrder.getPayTime(), queryOrderResult.getFee(), Optional.ofNullable(queryOrderResult.getRate()).map(BigDecimal::toString).orElse(""));
	}

}
