package com.shuidihuzhu.cf.financetoc.service.impl;

import com.shuidihuzhu.cf.client.feign.CaseEndMobileVerifyCodeFeignClient;
import com.shuidihuzhu.cf.financetoc.service.VerifyCodeService;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author：liuchangjun
 * @Date：2021/10/26
 */

@Service
@RefreshScope
@Slf4j
public class VerifyCodeServiceImpl implements VerifyCodeService {

    @Resource
    private CaseEndMobileVerifyCodeFeignClient client;

    @Override
    public Response checkVerifyCode(String mobile, String verifyCode, String clientIp) {
        return client.mobileVerifyCode(mobile, verifyCode, clientIp);
    }
}
