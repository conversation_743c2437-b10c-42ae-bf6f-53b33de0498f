package com.shuidihuzhu.cf.financetoc.service;

import com.shuidihuzhu.cf.enums.crowdfunding.CfInfoExtEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfPayOrigin;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingType;
import com.shuidihuzhu.cf.model.contribute.CfCombinePayInfo;
import com.shuidihuzhu.cf.vo.PayInfoVo;
import com.shuidihuzhu.client.baseservice.pay.enums.DepositTypeEnum;
import com.shuidihuzhu.client.baseservice.pay.enums.PayType;
import com.shuidihuzhu.client.baseservice.pay.model.PayResultV2;
import com.shuidihuzhu.common.web.enums.Platform;

/**
 * @author: lixuan
 * @date: 2018/3/19 12:12
 */
public interface PayBizForCf {

	PayResultV2 payInfoByNewV2(CrowdfundingType crowdfundingType, Platform platform, CfPayOrigin cfPayOriginEnum,
							   String payUid, int amountInFen, String openId, String body, String alipayShowUrl,
							   String alipayReturnUrl, String appId, long userId, DepositTypeEnum depositTypeEnum);


	PayInfoVo getPayInfoVoWithNew(CrowdfundingType crowdfundingType, PayType payTypeChannel, CfInfoExtEnum.PayType payTypeEnum,
								  Integer userThirdType, long userId);
	PayInfoVo getPayInfo(long userId, int userThirdType);

	PayResultV2 payCombineOrder(CfCombinePayInfo payInfo);

	PayResultV2 payInfoByNewV3(Platform platformEnum, CfPayOrigin cfPayOriginEnum, String payUid, Integer amountInFen, String openId, String payBody, String appId, long userId, DepositTypeEnum depositTypeEnum, int clientId);
}
