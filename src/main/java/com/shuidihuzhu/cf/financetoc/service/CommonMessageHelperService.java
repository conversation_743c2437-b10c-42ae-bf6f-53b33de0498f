package com.shuidihuzhu.cf.financetoc.service;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.event.OrderAddEvent;
import com.shuidihuzhu.cf.model.CaseEndModel;
import com.shuidihuzhu.cf.mq.model.OrderGuideToSdbModel;
import com.shuidihuzhu.cf.mq.payload.OrderAddPayload;
import com.shuidihuzhu.cf.mq.producer.MessageBuilder;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.infra.starter.rocketmq.core.ResultStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2018-07-16  14:30
 * <a href="https://wiki.shuiditech.com/pages/viewpage.action?pageId=22085813">delay-mq 文档</a>
 * @see MessageBuilder
 */
@Service
@Slf4j
@SuppressWarnings({"all"})
public class CommonMessageHelperService {

    @Autowired(required = false)
    private Producer producer;


    /**
     * 同步发送
     *
     * @param message
     * @return
     */
    public OpResult<MessageResult> send(Message message) {
        log.info("send {}", message);
        MessageResult sendResult = producer.send(message);
        if (sendResult == null) {
            log.error("sendResult null {}", message);
            return OpResult.createFailResult(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        ResultStatus status = sendResult.getStatus();
        if (status == null) {
            log.error("sendResult status null {}, {}", message, sendResult);
            return OpResult.createFailResult(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (status == ResultStatus.FAIL) {
            log.error("sendResult status fail {}", message, sendResult);
            return OpResult.createFailResult(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        return OpResult.createSucResult(sendResult);
    }

    /**
     * 筹下单支付导购转保险
     *
     * @param orderGuideToSdbModel
     * @return
     */
    public Message getOrderGuideToSdbMsg(OrderGuideToSdbModel orderGuideToSdbModel) {
        return MessageBuilder.create()
                .setTopic(MQTopicCons.INSURANCE_ORDER)
                .setTags(MQTagCons.TAG_UNIFIED_ORDER_SUCCESS)
                .setPayload(orderGuideToSdbModel)
                .build();
    }

    /**
     * 预下单成功消息
     *
     * @param orderAddPayload -
     * @return
     */
    public Message<OrderAddPayload> getOrderAddMsg(OrderAddPayload orderAddPayload) {
        return MessageBuilder.<OrderAddPayload>create()
                .setTopic(MQTopicCons.CF)
                .setTags(MQTagCons.CF_DONATION_ORDER_ADD_MSG)
                .setPayload(orderAddPayload)
                .build();
    }

    public Message<CaseEndModel> getCrowdfundingEndMsg(CaseEndModel caseEndModel) {
        return MessageBuilder.<CaseEndModel>create()
                .setTopic(MQTopicCons.CF)
                .setTags(MQTagCons.CF_CASE_END_FROM_FINANCE_TOC_MSG)
                .setPayload(caseEndModel)
                .build();
    }
}
