package com.shuidihuzhu.cf.financetoc.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.adminpure.feign.InitialAuditWorkOrderFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.initial.PatientToVolunteerConst;
import com.shuidihuzhu.cf.client.adminpure.model.initial.PatientToVolunteerParam;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.feign.CfFirstApproveFeignClient;
import com.shuidihuzhu.cf.client.feign.CfRejectForReportFeignClient;
import com.shuidihuzhu.cf.client.material.model.MaterialPlanVersion;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enhancer.utils.CfCipherUtil;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.finance.model.deposit.CfPayeeDepositAccount;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CfInfoExtBiz;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingAttachmentBiz;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingInfoStatusBiz;
import com.shuidihuzhu.cf.financetoc.biz.payee.CrowdfundingInfoPayeeBiz;
import com.shuidihuzhu.cf.financetoc.model.EmergencyCheckData;
import com.shuidihuzhu.cf.financetoc.util.BackCardUtil;
import com.shuidihuzhu.cf.financetoc.util.BeenCopyUtil;
import com.shuidihuzhu.cf.financetoc.util.CfIdCardUtil;
import com.shuidihuzhu.cf.financetoc.util.CrowdfundingUtil;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.*;
import com.shuidihuzhu.cf.param.raise.RaiseBasicInfoParam;
import com.shuidihuzhu.cf.response.crowdfunding.CrowdfundingInfoResponse;
import com.shuidihuzhu.cf.vo.CfFirsApproveMaterialVO;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.common.web.enums.LoginResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.IdCardUtil;
import com.shuidihuzhu.common.web.util.MobileUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by lixuan on 2016/12/06.
 */
@Slf4j
@RefreshScope
@Service
public class CrowdfundingInfoService {
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CrowdfundingAttachmentBiz crowdfundingAttachmentBiz;
    @Resource
    private CfHuKouService cfHuKouService;
    @Autowired
    private CfDepositCommonService cfDepositCommonService;
    @Autowired
    private CrowdfundingInfoStatusBiz crowdfundingInfoStatusBiz;
    @Autowired
    private CrowdfundingInfoPayeeBiz crowdfundingInfoPayeeBiz;
    @Autowired
    private CfInfoExtBiz cfInfoExtBiz;
    @Autowired
    private CryptoRelationService cryptoRelationService;
    @Autowired
    private InitialAuditWorkOrderFeignClient initialAuditWorkOrderFeignClient;
    @Autowired(required = false)
    private Producer producer;
    @Autowired
    private IPayeeMaterialCenterService payeeMaterialCenterService;
    @Autowired
    private CfBankCardVerifyService cfBankCardVerifyService;
    @Autowired
    private CfPaDepositPayeeAccountService cfPaDepositPayeeAccountService;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Resource
    private CfRejectForReportFeignClient cfRejectForReportFeignClient;
    @Resource
    private ClewPreproseMaterialFeignClient clewPreproseMaterialFeignClient;
    @Resource
    private CfCipherUtil cfCipherUtil;


    /**
     * 兼容现有逻辑  转换成vo
     * @param crowdfundingInfoPayee
     * @param userId
     * @param list
     * @return
     */
    public Response<CrowdfundingInfoResponse.Data> updatePayeeInfo(CrowdfundingInfoPayee crowdfundingInfoPayee ,
                                                                   long userId,
                                                                   List<CfCommonStoreModel> list,
                                                                   MaterialsParam materialsParam,
                                                                   String appVersion){
        int hasProveType = materialsParam.getHasProveType();
        CrowdfundingInfoPayeeVo crowdfundingInfoPayeeVo = new CrowdfundingInfoPayeeVo();
        BeanUtils.copyProperties(crowdfundingInfoPayee,crowdfundingInfoPayeeVo);

        crowdfundingInfoPayeeVo.setPayeeName(crowdfundingInfoPayee.getName());
        crowdfundingInfoPayeeVo.setIdType(UserIdentityType.getByCode(crowdfundingInfoPayee.getIdType()));
        crowdfundingInfoPayeeVo.setRelationType(CrowdfundingRelationType.getByCode(crowdfundingInfoPayee.getRelationType()));
        crowdfundingInfoPayeeVo.setMobile(shuidiCipher.decrypt(crowdfundingInfoPayee.getMobile()));
        crowdfundingInfoPayeeVo.setBankCard(shuidiCipher.decrypt(crowdfundingInfoPayee.getBankCard()));
        crowdfundingInfoPayeeVo.setIdCard(shuidiCipher.decrypt(crowdfundingInfoPayee.getIdCard()));
        if (StringUtils.isNotEmpty(crowdfundingInfoPayeeVo.getEmergencyPhone())){
            crowdfundingInfoPayeeVo.setEmergencyPhone(shuidiCipher.decrypt(crowdfundingInfoPayeeVo.getEmergencyPhone()));
        }

        Map<AttachmentTypeEnum, List<CrowdfundingAttachmentVo>> map = crowdfundingAttachmentBiz.getFundingAttachmentMap(crowdfundingInfoPayee.getCaseId());

        List<AttachmentTypeEnum> provesList =  Lists.newArrayList(  AttachmentTypeEnum.ATTACH_PAYEE_HUKOUBEN, AttachmentTypeEnum.ATTACH_PAYEE_JIEHUNZHENG,
                AttachmentTypeEnum.ATTACH_PAYEE_CHUSHENGZHENG, AttachmentTypeEnum.ATTACH_PAYEE_ZHENGMING, AttachmentTypeEnum.ATTACH_PAYEE_WEITUOSHU);
        //证明图片
        List<ProveVo> proves = provesList.stream().filter(r->CollectionUtils.isNotEmpty(map.get(r))).map(r->{
            ProveVo v = new ProveVo();
            v.setRelType(r.value());
            List<CrowdfundingAttachmentVo> l = map.get(r);
            List<String> attachments = l.stream().map(CrowdfundingAttachmentVo::getUrl).collect(Collectors.toList());
            v.setAttachments(attachments);
            return v;
        }).collect(Collectors.toList());
        crowdfundingInfoPayeeVo.setProves(proves);
        //身份证
        List<AttachmentTypeEnum> authenticationList =  Lists.newArrayList(AttachmentTypeEnum.ATTACH_PAYEE_ONLY_ID_CARD);
        List<ProveVo> authentication = authenticationList.stream().filter(r->CollectionUtils.isNotEmpty(map.get(r))).map(r->{
            ProveVo v = new ProveVo();
            v.setRelType(r.value());
            List<CrowdfundingAttachmentVo> l = map.get(r);
            List<String> attachments = l.stream().map(CrowdfundingAttachmentVo::getUrl).collect(Collectors.toList());
            v.setAttachments(attachments);
            return v;
        }).collect(Collectors.toList());
        crowdfundingInfoPayeeVo.setAuthentication(authentication);
        //手持身份证照片
        List<CrowdfundingAttachmentVo> cardList = map.get(AttachmentTypeEnum.ATTACH_PAYEE_ID_CARD);
        if (CollectionUtils.isNotEmpty(cardList)){
            crowdfundingInfoPayeeVo.setIdCardPhoto(cardList.get(0).getUrl());
        }
        List<CrowdfundingAttachmentVo> faceList = map.get(AttachmentTypeEnum.ATTACH_PAYEE_FACE_ID);
        if (CollectionUtils.isNotEmpty(faceList)){
            crowdfundingInfoPayeeVo.setIdCardPhoto(faceList.get(0).getUrl());
        }

        //如果没上传关系证明  才需要查询
        if (hasProveType == 0){
            //视频
            List<CrowdfundingAttachmentVo> videos = map.get(AttachmentTypeEnum.ATTACH_PAYEE_RELATION_VIDEO);
            if (CollectionUtils.isNotEmpty(videos)){
                crowdfundingInfoPayeeVo.setRelationVideos(videos.stream().map(CrowdfundingAttachmentVo::getUrl).collect(Collectors.toList()));
            }

            //合照
            List<CrowdfundingAttachmentVo> wts = map.get(AttachmentTypeEnum.ATTACH_PAYEE_WEITUOSHU_HEZHAO);
            if (CollectionUtils.isNotEmpty(wts)){
                crowdfundingInfoPayeeVo.setWeituoshuHezhao(wts.get(0).getUrl());
            }
        }
        //如果驳回的时候上传了身份证图片
        if (materialsParam.getCommonIdPic() == 1){
            crowdfundingInfoPayeeVo.setFaceIdResult(0);
        }

        //用提交参数覆盖
        list.stream().forEach(r-> BeenCopyUtil.jsonToClass(crowdfundingInfoPayeeVo,r.getStoreValue(),CrowdfundingInfoPayeeVo.class));
        //兼容前端驳回uuid没有的情况
        crowdfundingInfoPayeeVo.setInfoUuid(crowdfundingInfoPayee.getInfoUuid());

        return savePayeeInfo(crowdfundingInfoPayeeVo,userId,appVersion);
    }

    public Response<CrowdfundingInfoResponse.Data> savePayeeInfo(CrowdfundingInfoPayeeVo crowdfundingInfoPayeeVo,long userId,String appVersion){

        if (crowdfundingInfoPayeeVo == null || crowdfundingInfoPayeeVo.getInfoUuid() == null) {
            return NewResponseUtil.makeResponse(CfErrorCode.SYSTEM_PARAM_ERROR.getCode(),
                    CfErrorCode.SYSTEM_PARAM_ERROR.getMsg(), null);
        }

        if (userId <= 0) {
            log.warn("CrowdfundingV4InfoUpdateController addOrUpdatePayeeInfo userId is {}", userId);
            return NewResponseUtil.makeResponse(LoginResult.LOGIN.code, LoginResult.LOGIN.msg, null);
        }
        CrowdfundingInfo cfInfoSimpleModel = crowdfundingInfoBiz.getFundingInfo(crowdfundingInfoPayeeVo.getInfoUuid());
        if (null == cfInfoSimpleModel || userId != cfInfoSimpleModel.getUserId()) {
            return NewResponseUtil.makeError(CfErrorCode.CF_NOT_FOUND);
        }
        log.info("CrowdfundingV4InfoUpdateController addOrUpdatePayeeInfo crowdfundingInfoPayeeVo:{}",
                crowdfundingInfoPayeeVo);
        String payeeName = StringUtils.trim(crowdfundingInfoPayeeVo.getPayeeName());
        crowdfundingInfoPayeeVo.setPayeeName(StringUtils.trimToEmpty(payeeName));
        String mobile = StringUtils.trim(crowdfundingInfoPayeeVo.getMobile());
        String bankCard = StringUtils.trim(crowdfundingInfoPayeeVo.getBankCard());
        String bankName = StringUtils.trim(crowdfundingInfoPayeeVo.getBankName());
        // String bankBranchName = crowdfundingInfoPayeeVo.getBankBranchName();
        String idCard = StringUtils.trim(crowdfundingInfoPayeeVo.getIdCard());
        String idCardPhoto = StringUtils.trim(crowdfundingInfoPayeeVo.getIdCardPhoto());
        UserIdentityType idType = crowdfundingInfoPayeeVo.getIdType();
        CrowdfundingRelationType relationType = crowdfundingInfoPayeeVo.getRelationType();
        List<String> attachments = crowdfundingInfoPayeeVo.getAttachments();
        if (StringUtils.isBlank(bankName) || StringUtils.isBlank(payeeName) || StringUtils.isBlank(mobile)
                || relationType == null) {
            return NewResponseUtil.makeError(CfErrorCode.CF_INFO_PARAM_ERROR_PAYEE);
        }
        if (StringUtils.isBlank(bankCard) || !BackCardUtil.checkBankCard(bankCard)) {
            return NewResponseUtil.makeError(CfErrorCode.CF_BANK_CARD_VERIFY_FAILED);
        }
        if (MobileUtil.illegal(mobile)) {
            return NewResponseUtil.makeError(CfErrorCode.USER_ACCOUNT_MOBILE_ERROR);
        }
        if (StringUtils.isNotEmpty(crowdfundingInfoPayeeVo.getEmergencyPhone()) && MobileUtil.illegal(crowdfundingInfoPayeeVo.getEmergencyPhone())){
            return NewResponseUtil.makeError(CfErrorCode.USER_ACCOUNT_MOBILE_ERROR);
        }
        // 判断用户是否可以提交收款人关系视频
        // 新版本不判断
        if (!MaterialPlanVersion.is0906MaterialAudit(cfInfoSimpleModel.getMaterialPlanId())){
            if (!crowdfundingInfoPayeeBiz.checkCanSubmitRelationVideo(crowdfundingInfoPayeeVo) ) {
                return NewResponseUtil.makeError(CfErrorCode.SUBMIT_PAYEE_INFO_FAILED);
            }
        }
        if (relationType == CrowdfundingRelationType.self) {
            if (StringUtils.isEmpty(idCard) || idType == null) {
                return NewResponseUtil.makeError(CfErrorCode.CF_INFO_PARAM_ERROR_PAYEE);
            }
            if ((idType == UserIdentityType.identity && IdCardUtil.illegal(idCard))
                    || (idType != UserIdentityType.identity && !idCard.matches("[a-zA-Z0-9]{1,30}"))) {
                return NewResponseUtil.makeError(CfErrorCode.CF_PARAM_ERROR_PAYEE_ID_CARD_ERROR);
            }

        } else {
            if (StringUtils.isEmpty(idCard) || idType == null) {
                return NewResponseUtil.makeError(CfErrorCode.CF_INFO_PARAM_ERROR_PAYEE);
            }

            if (!crowdfundingInfoPayeeVo.faceIdResultPass() && StringUtils.isEmpty(idCardPhoto)){
                return NewResponseUtil.makeError(CfErrorCode.CF_INFO_PARAM_ERROR_PAYEE);
            }

            if ((idType == UserIdentityType.identity && IdCardUtil.illegal(idCard))
                    || (idType != UserIdentityType.identity && !idCard.matches("[a-zA-Z0-9]{1,30}"))) {
                return NewResponseUtil.makeError(CfErrorCode.CF_PARAM_ERROR_PAYEE_ID_CARD_ERROR);
            }
            if (!MaterialPlanVersion.is0906MaterialAudit(cfInfoSimpleModel.getMaterialPlanId())){

                if (CollectionUtils.isEmpty(attachments) && CollectionUtils.isEmpty(crowdfundingInfoPayeeVo.getRelationVideos())) {
                    return NewResponseUtil.makeError(CfErrorCode.CF_PARAM_ERROR_MISS_PAYEE_RELATION_PICS);
                }
            }
            if (MaterialPlanVersion.is0906MaterialAudit(cfInfoSimpleModel.getMaterialPlanId())){
                if (CollectionUtils.isEmpty(crowdfundingInfoPayeeVo.getProves()) && CollectionUtils.isEmpty(crowdfundingInfoPayeeVo.getRelationVideos())) {
                    return NewResponseUtil.makeError(CfErrorCode.CF_PARAM_ERROR_MISS_PAYEE_RELATION_PICS);
                }
                if (CollectionUtils.isNotEmpty(crowdfundingInfoPayeeVo.getProves())){
                    Optional optional = crowdfundingInfoPayeeVo.getProves().stream().filter(r->CollectionUtils.isEmpty(r.getAttachments())).findAny();
                    if (optional.isPresent()){
                        return NewResponseUtil.makeError(CfErrorCode.CF_PARAM_ERROR_MISS_PAYEE_RELATION_PICS);
                    }
                }

                if (crowdfundingInfoPayeeVo.getRelativesType() <= 0){
                    return NewResponseUtil.makeError(CfErrorCode.PAYEE_RELATION_ERROR);
                }
            }
        }

        //身份证不足18位不让提交（过滤掉15位的身份证）
        if (idType == UserIdentityType.identity && !CfIdCardUtil.isValidIdCard(idCard)) {
            return NewResponseUtil.makeError(CfErrorCode.USER_INFO_ID_CARD_ERROR);
        }
        if (idType == UserIdentityType.identity && CfIdCardUtil.getCurrentAge(idCard) < 18) {
            return NewResponseUtil.makeError(CfErrorCode.PAYEE_NOT_ALLOW_OF_AGE);
        }
        // 替换为 大写X
        idCard = idCard.replaceAll("x","X");
        crowdfundingInfoPayeeVo.setIdCard(idCard);
        CrowdfundingInfoResponse response = addOrUpdatePayeeInfo(userId, crowdfundingInfoPayeeVo,appVersion);
        return NewResponseUtil.makeResponse(response.getErrorCode().getCode(), response.getErrorCode().getMsg(),
                response.getData());
    }

    public CfErrorCode checkEmergencyError(String infoId, String emergency, String emergencyPhone) {
        log.info("checkEmergencyError infoId {} emergency {} emergencyPhone {}", infoId, emergency, emergencyPhone);

        // 获取案例基本数据
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoId);
        if (Objects.isNull(crowdfundingInfo)) {
            log.info("checkEmergencyError crowdfundingInfo not found");
            return CfErrorCode.CF_NOT_FOUND;
        }

        // 基础数据验证
        if (StringUtils.isEmpty(emergency) || StringUtils.isEmpty(emergencyPhone)) {
            log.info("checkEmergencyError emergency or emergencyPhone is empty");
            return CfErrorCode.SUCCESS;
        }

        // 获取所有必要数据
        EmergencyCheckData checkData = gatherEmergencyCheckData(crowdfundingInfo);
        if (checkData == null) {
            return CfErrorCode.SUCCESS; // 数据获取失败时不阻塞用户
        }

        // 执行校验
        return performEmergencyValidation(emergency, emergencyPhone, checkData);
    }

    private EmergencyCheckData gatherEmergencyCheckData(CrowdfundingInfo crowdfundingInfo) {
        try {
            RaiseBasicInfoParam raiseBasicInfo = getRaiseBasicInfo(crowdfundingInfo.getInfoId());
            CfFirsApproveMaterialVO approveInfo = getFirstApproveInfo(crowdfundingInfo.getInfoId());
            String preSelfMobile = getPreSelfMobile(crowdfundingInfo.getId());

            if (raiseBasicInfo == null || approveInfo == null) {
                return null;
            }

            return EmergencyCheckData.builder()
                    .selfRealName(Optional.ofNullable(approveInfo.getSelfRealName()).orElse(""))
                    .patientName(Optional.ofNullable(approveInfo.getPatientRealName()).orElse(""))
                    .selfMobile(Optional.ofNullable(raiseBasicInfo.getSelfMobile()).orElse(""))
                    .preSelfMobile(Optional.ofNullable(preSelfMobile).orElse(""))
                    .build();
        } catch (Exception e) {
            log.error("Failed to gather emergency check data for infoId: {}", crowdfundingInfo.getInfoId(), e);
            return null;
        }
    }

    private CfErrorCode performEmergencyValidation(String emergency, String emergencyPhone, EmergencyCheckData checkData) {
        // 校验姓名重复
        if (isNameDuplicated(emergency, checkData.getSelfRealName(), checkData.getPatientName())) {
            log.info("Emergency contact name duplicated: {} {} {}", emergency, checkData.getSelfRealName(), checkData.getPatientName());
            return CfErrorCode.EMERGENCY_NAME_REPEAT_ERROR;
        }

        // 校验手机号重复
        if (isPhoneDuplicated(emergencyPhone, checkData.getSelfMobile(), checkData.getPreSelfMobile())) {
            log.info("Emergency contact phone duplicated: {} {} {}", emergencyPhone, checkData.getSelfMobile(), checkData.getPreSelfMobile());
            return CfErrorCode.EMERGENCY_PHONE_REPEAT_ERROR;
        }

        return CfErrorCode.SUCCESS;
    }

    private boolean isNameDuplicated(String emergencyName, String selfName, String patientName) {
        return StringUtils.isNotEmpty(emergencyName) &&
                (emergencyName.equals(selfName) || emergencyName.equals(patientName));
    }

    private boolean isPhoneDuplicated(String emergencyPhone, String selfMobile, String preSelfMobile) {
        return StringUtils.isNotEmpty(emergencyPhone) &&
                (emergencyPhone.equals(selfMobile) || emergencyPhone.equals(preSelfMobile));
    }

    private RaiseBasicInfoParam getRaiseBasicInfo(String infoId) {
        return Optional.ofNullable(cfRejectForReportFeignClient.getRaiseBasicInfo(infoId))
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
    }

    private CfFirsApproveMaterialVO getFirstApproveInfo(String infoId) {
        return Optional.ofNullable(cfRejectForReportFeignClient.getFirstApproveInfo(infoId))
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
    }

    private String getPreSelfMobile(Integer caseId) {
        return Optional.ofNullable(clewPreproseMaterialFeignClient.selectMaterialByCaseIdForQC(caseId))
                .filter(Response::ok)
                .map(Response::getData)
                .map(PreposeMaterialModel.MaterialInfoVo::getRaiseMobile)
                .orElse("");
    }

    public static boolean areStringsEqualSimple(String emergencyInfo, String reducedValueV1, String reducedValueV2) {
        return !emergencyInfo.equals(reducedValueV1) && !emergencyInfo.equals(reducedValueV2);
    }


    /**
     * 填写收款人信息
     *
     * @param userId
     * @param crowdfundingInfoPayeeVo
     * @return
     */
    public CrowdfundingInfoResponse addOrUpdatePayeeInfo(long userId, CrowdfundingInfoPayeeVo crowdfundingInfoPayeeVo,String appVersion) {
        String infoUuid = crowdfundingInfoPayeeVo.getInfoUuid();
        CrowdfundingInfo crowdfundingInfo = this.crowdfundingInfoBiz.getFundingInfo(infoUuid);
        //在 银行卡鉴权时使用
        CrowdfundingInfo crowdfundingInfoOrg = new CrowdfundingInfo();
        BeanUtils.copyProperties(crowdfundingInfo, crowdfundingInfoOrg);
        if (crowdfundingInfo != null) {
            //若当前案例处于已提交或者是审核通过状态  不让用户更新
            if (crowdfundingInfo.getStatus() == CrowdfundingStatus.SUBMITTED ||
                    crowdfundingInfo.getStatus() == CrowdfundingStatus.CROWDFUNDING_STATED) {
                log.info("addOrUpdateBaseInfo info status is:{} infoUuid:{}", crowdfundingInfo.getStatus().value(), infoUuid);
                CrowdfundingInfoResponse response = new CrowdfundingInfoResponse();
                response.setErrorCode(CfErrorCode.CF_INFO_SUBMITTED);
                response.setInfoUuid(crowdfundingInfo.getInfoId());
                return response;
            }
        }

        CfErrorCode verifyResult = verifyInfoId(crowdfundingInfo, userId);
        if (!verifyResult.equals(CfErrorCode.SUCCESS)) {
            CrowdfundingInfoResponse response = new CrowdfundingInfoResponse();
            response.setErrorCode(verifyResult);
            return response;
        }

        int crowdfundingId = crowdfundingInfo.getId();
        String idCard = StringUtils.trim(crowdfundingInfoPayeeVo.getIdCard());
        String cryptoIdCard = oldShuidiCipher.aesEncrypt(idCard);
        UserIdentityType idType = crowdfundingInfoPayeeVo.getIdType();
        String idCardPhoto = StringUtils.trim(crowdfundingInfoPayeeVo.getIdCardPhoto());
        String bankCard = StringUtils.trim(crowdfundingInfoPayeeVo.getBankCard());
        String cryptoBankCard = oldShuidiCipher.aesEncrypt(bankCard);
        String bankName = StringUtils.trim(crowdfundingInfoPayeeVo.getBankName());
        String mobile = StringUtils.trim(crowdfundingInfoPayeeVo.getMobile());
        String cryptoMobile = oldShuidiCipher.aesEncrypt(mobile);
        String payeeName = StringUtils.trim(crowdfundingInfoPayeeVo.getPayeeName());
        List<String> attachments = crowdfundingInfoPayeeVo.getAttachments();
        CrowdfundingRelationType relationType = crowdfundingInfoPayeeVo.getRelationType();

        crowdfundingInfo.setPayeeName(StringUtils.trimToEmpty(payeeName));
        crowdfundingInfo.setPayeeIdCard(cryptoIdCard);
        crowdfundingInfo.setPayeeBankName(bankName);
        crowdfundingInfo.setPayeeBankBranchName("");
        crowdfundingInfo.setPayeeBankCard(cryptoBankCard);
        crowdfundingInfo.setPayeeMobile(cryptoMobile);
        crowdfundingInfo.setRelationType(relationType);
        crowdfundingInfo.setRelation(relationType.getDescription());
        CrowdfundingInfoPayee crowdfundingInfoPayee = this.crowdfundingInfoPayeeBiz.getByInfoUuid(infoUuid);

        // 银行卡鉴权 新的四要素，旧的三要素
        Response<CrowdfundingBankVerifyResultVo> resultVoResponse;
        CrowdfundingInfoResponse response = new CrowdfundingInfoResponse();
        if (cfDepositCommonService.isNeedDepositWithAcc(crowdfundingInfo.getId())) {

            // TODO 非三点几的版本提示 去H5 在 移动端上线后下掉
            if (StringUtils.isNotEmpty(appVersion) && !appVersion.startsWith("3")) {
                response.setErrorCode(CfErrorCode.CF_INFO_VERSION_ERROR_CREDIT_SUPPLEMENT);
                return response;
            }
            Response<CfPayeeDepositAccount> accountResponse = cfPaDepositPayeeAccountService.getOpenSuccessAccount(
                    payeeName, bankCard, idCard);
            log.info("openWithResponse:{}", JSON.toJSONString(accountResponse));
            if (accountResponse.notOk()) {
                response.setErrorCode(CfErrorCode.CF_PARAM_ERROR_PAYEE);
                response.setInfoUuid(crowdfundingInfo.getInfoId());
                return response;
            }
        } else {
            resultVoResponse = cfBankCardVerifyService.checkCardByThreeElements(
                    crowdfundingInfoOrg, crowdfundingInfoPayeeVo.getPayeeName(), crowdfundingInfoPayeeVo.getBankCard(),
                    crowdfundingInfoPayeeVo.getIdCard());
            log.info("openWithResponse:{}", JSON.toJSONString(resultVoResponse));
            if (resultVoResponse.notOk()) {
                response.setErrorCode(CfErrorCode.getByCode(resultVoResponse.getCode()));
                response.setInfoUuid(crowdfundingInfo.getInfoId());
                CrowdfundingInfoResponse.Data data = new CrowdfundingInfoResponse.Data();
                data.setExtra(resultVoResponse.getData());
                return response;
            }
        }

        response.setErrorCode(CfErrorCode.SUCCESS);

        if (crowdfundingInfoPayee == null) {

            crowdfundingInfoPayee = new CrowdfundingInfoPayee();
            crowdfundingInfoPayee.setBankBranchName("");
            crowdfundingInfoPayee.setBankCard(cryptoBankCard);
            crowdfundingInfoPayee.setBankName(bankName);
            crowdfundingInfoPayee.setIdCard(cryptoIdCard);
            crowdfundingInfoPayee.setIdType(UserIdentityType.getCode(idType));
            crowdfundingInfoPayee.setInfoUuid(infoUuid);
            crowdfundingInfoPayee.setMobile(cryptoMobile);
            crowdfundingInfoPayee.setName(payeeName);
            crowdfundingInfoPayee.setRelationType(CrowdfundingRelationType.getCode(relationType));
            crowdfundingInfoPayee.setCaseId(crowdfundingInfo.getId());
            crowdfundingInfoPayee.setEmergency(crowdfundingInfoPayeeVo.getEmergency());
            crowdfundingInfoPayee.setEmergencyPhone(oldShuidiCipher.aesEncrypt(crowdfundingInfoPayeeVo.getEmergencyPhone()));
            crowdfundingInfoPayee.setRelativesType(crowdfundingInfoPayeeVo.getRelativesType());
            crowdfundingInfoPayee.setFaceIdResult(crowdfundingInfoPayeeVo.getFaceIdResult());
            crowdfundingInfoPayee.setOtherIdPhoto(crowdfundingInfoPayeeVo.getOtherIdPhoto());

            this.crowdfundingInfoPayeeBiz.add(crowdfundingInfoPayee);

            if (crowdfundingInfoStatusBiz.getByInfoUuidAndType(infoUuid,
                    CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode()) == null) {
                // 提交收款人信息状态
                CrowdfundingInfoStatus crowdfundingInfoStatus = new CrowdfundingInfoStatus();
                crowdfundingInfoStatus.setInfoUuid(infoUuid);
                crowdfundingInfoStatus.setType(CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode());
                // crowdfundingInfoStatus.setStatus(CrowdfundingConstant.BaseStatus.valid.getScore());
                crowdfundingInfoStatus.setStatus(CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode());
                crowdfundingInfoStatus.setCaseId(crowdfundingId);
                this.crowdfundingInfoStatusBiz.add(crowdfundingInfoStatus);
            } else {
                this.crowdfundingInfoStatusBiz.updateByInfoId(infoUuid,
                        CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode(),
                        CrowdfundingInfoStatusEnum.UN_SUBMITTED);
            }
        } else {
            crowdfundingInfoPayee.setBankBranchName("");
            crowdfundingInfoPayee.setBankCard(cryptoBankCard);
            crowdfundingInfoPayee.setBankName(bankName);
            crowdfundingInfoPayee.setIdCard(cryptoIdCard);
            crowdfundingInfoPayee.setIdType(UserIdentityType.getCode(idType));
            crowdfundingInfoPayee.setMobile(cryptoMobile);
            crowdfundingInfoPayee.setName(payeeName);
            crowdfundingInfoPayee.setRelationType(CrowdfundingRelationType.getCode(relationType));
            crowdfundingInfoPayee.setRelativesType(crowdfundingInfoPayeeVo.getRelativesType());
            crowdfundingInfoPayee.setEmergency(crowdfundingInfoPayeeVo.getEmergency());
            crowdfundingInfoPayee.setEmergencyPhone(oldShuidiCipher.aesEncrypt(crowdfundingInfoPayeeVo.getEmergencyPhone()));
            crowdfundingInfoPayee.setFaceIdResult(crowdfundingInfoPayeeVo.getFaceIdResult());
            crowdfundingInfoPayee.setOtherIdPhoto(crowdfundingInfoPayeeVo.getOtherIdPhoto());
            this.crowdfundingInfoPayeeBiz.update(crowdfundingInfoPayee);

            this.crowdfundingInfoStatusBiz.updateByInfoId(infoUuid,
                    CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode(),
                    CrowdfundingInfoStatusEnum.UN_SUBMITTED);
        }

        // 判断并标记收款人是否内部正式顾问, 不需要解析返回值
        if (crowdfundingInfoPayee.getIdType() == UserIdentityType.identity.getCode()) {
            PatientToVolunteerParam param = PatientToVolunteerParam.builder()
                    .caseId(crowdfundingInfoPayee.getCaseId())
                    .payeeName(crowdfundingInfoPayee.getName())
                    .useType(PatientToVolunteerConst.Source.APPROVE)
                    .payeeIdCard(crowdfundingInfoPayee.getIdCard())
                    .build();
            OperationResult<Integer> resp = initialAuditWorkOrderFeignClient.patientToVolunteer(param);
            log.info("检查修改的收款人是否为内部正式顾问 param:{} response:{}", JSON.toJSONString(param), JSON.toJSONString(resp));
        }

        //********{此处是先更新了 案例表的数据，之后再去确认要不要执行 银行卡鉴权}*************
        this.crowdfundingInfoBiz.updatePayeeInfo(crowdfundingInfo);
        //收款人变更时，更新关系
        cryptoRelationService.sendPayeeInfoRelation(crowdfundingInfo);

        // 收款人变更时 发送消息
        producer.send(new Message<>(MQTopicCons.CF,
                MQTagCons.CF_BASE_INFO_CHANGE_TAG, MQTagCons.CF_BASE_INFO_CHANGE_TAG + "-" +
                crowdfundingInfo.getId()
                + "-" + System.currentTimeMillis(), crowdfundingInfo.getId(), DelayLevel.S5));

        payeeMaterialCenterService.addOrUpdatePersonAccount(crowdfundingInfo.getId(), crowdfundingInfoPayee, null);

        // 添加附件
        if (relationType != CrowdfundingRelationType.self) {

            List<AttachmentTypeEnum> typeList = Lists.newArrayList(AttachmentTypeEnum.ATTACH_PAYEE_ID_CARD,
                    AttachmentTypeEnum.ATTACH_PAYEE_RELATION, AttachmentTypeEnum.ATTACH_PAYEE_RELATION_VIDEO,
                    AttachmentTypeEnum.ATTACH_PAYEE_HUKOUBEN, AttachmentTypeEnum.ATTACH_PAYEE_JIEHUNZHENG,
                    AttachmentTypeEnum.ATTACH_PAYEE_CHUSHENGZHENG, AttachmentTypeEnum.ATTACH_PAYEE_ZHENGMING,
                    AttachmentTypeEnum.ATTACH_PAYEE_WEITUOSHU, AttachmentTypeEnum.ATTACH_PAYEE_ONLY_ID_CARD,
                    AttachmentTypeEnum.ATTACH_PAYEE_WEITUOSHU_HEZHAO,AttachmentTypeEnum.ATTACH_PAYEE_FACE_ID);

            this.crowdfundingAttachmentBiz.deleteByParentIdAndType(crowdfundingId, typeList);

            List<CrowdfundingAttachment> attachmentList = Lists.newArrayList();
            if (StringUtils.isNotEmpty(idCardPhoto)){
                attachmentList.add(new CrowdfundingAttachment(crowdfundingId, AttachmentTypeEnum.ATTACH_PAYEE_ID_CARD, idCardPhoto));
            }
            if (CollectionUtils.isNotEmpty(attachments)){

                for (int i = 0; i < attachments.size(); i++) {
                    String url = attachments.get(i);
                    if (StringUtils.isBlank(url)){
                        continue;
                    }
                    attachmentList.add(new CrowdfundingAttachment(crowdfundingId, AttachmentTypeEnum.ATTACH_PAYEE_RELATION,
                            url, i));
                }
            }

            // 收款人关系视频
            if (CollectionUtils.isNotEmpty(crowdfundingInfoPayeeVo.getRelationVideos()) &&
                    (relationType == CrowdfundingRelationType.spouse || relationType == CrowdfundingRelationType.other) ) {
                for (int i = 0; i < crowdfundingInfoPayeeVo.getRelationVideos().size(); ++i) {
                    String videoUrls = crowdfundingInfoPayeeVo.getRelationVideos().get(i);
                    attachmentList.add(
                            new CrowdfundingAttachment(crowdfundingId, AttachmentTypeEnum.ATTACH_PAYEE_RELATION_VIDEO, videoUrls, i));
                }
            }
            //身份验证
            if (CollectionUtils.isNotEmpty(crowdfundingInfoPayeeVo.getAuthentication())){
                crowdfundingInfoPayeeVo.getAuthentication().stream().forEach(r->{
                    for (int i=0;i< r.getAttachments().size();i++){
                        String url = r.getAttachments().get(i);
                        if (StringUtils.isBlank(url)){
                            continue;
                        }
                        attachmentList.add(new CrowdfundingAttachment(crowdfundingId, AttachmentTypeEnum.getAttachmentTypeEnum(r.getRelType()), url, i));
                    }
                });
            }
            //关系证明
            if (CollectionUtils.isNotEmpty(crowdfundingInfoPayeeVo.getProves())){
                crowdfundingInfoPayeeVo.getProves().stream().forEach(r->{
                    for (int i=0;i< r.getAttachments().size();i++){
                        String url = r.getAttachments().get(i);
                        if (StringUtils.isBlank(url)){
                            continue;
                        }
                        attachmentList.add(new CrowdfundingAttachment(crowdfundingId, AttachmentTypeEnum.getAttachmentTypeEnum(r.getRelType()), url, i));
                    }
                });
            }

            if (StringUtils.isNotEmpty(crowdfundingInfoPayeeVo.getWeituoshuHezhao())){
                attachmentList.add(new CrowdfundingAttachment(crowdfundingId, AttachmentTypeEnum.ATTACH_PAYEE_WEITUOSHU_HEZHAO, crowdfundingInfoPayeeVo.getWeituoshuHezhao()));
            }

            if (StringUtils.isNotEmpty(crowdfundingInfoPayeeVo.getFaceVideo())){
                attachmentList.add(new CrowdfundingAttachment(crowdfundingId, AttachmentTypeEnum.ATTACH_PAYEE_FACE_ID, crowdfundingInfoPayeeVo.getFaceVideo()));
            }

            this.crowdfundingAttachmentBiz.add(attachmentList);
        }

        // 保存｜更新 户口勾选状态
        if (Objects.nonNull(crowdfundingInfoPayeeVo.getHukouOption())) {
            HuKouOption oldOption = cfHuKouService.selectHuKouOptionByCaseId(crowdfundingId);
            if (Objects.isNull(oldOption)) {
                cfHuKouService.saveHuKouOption(crowdfundingInfoPayeeVo.getHukouOption(), crowdfundingId);
            } else {
                cfHuKouService.updateHuKouOption(crowdfundingInfoPayeeVo.getHukouOption(), crowdfundingId);
            }
        }

        //更新案例为认证通过
        if (response.getErrorCode().equals(CfErrorCode.SUCCESS)) {
            crowdfundingInfoBiz.updateVerifyStatus(crowdfundingId, BankCardVerifyStatus.passed,
                    "认证通过", "认证通过");
        }
        // 是否显示提交对话框
        boolean showSubmit = this.checkShowSubmit(crowdfundingInfo);
        response = new CrowdfundingInfoResponse();
        response.setErrorCode(CfErrorCode.SUCCESS);
        response.setInfoUuid(crowdfundingInfo.getInfoId());
        response.setShowSubmit(showSubmit);
        return response;
    }


    private CfErrorCode verifyInfoId(CrowdfundingInfo crowdfundingInfo, long requestUserId) {
        if (crowdfundingInfo == null) {
            return CfErrorCode.CF_NOT_FOUND;
        }
        if (crowdfundingInfo.getStatus() != CrowdfundingStatus.APPROVE_PENDING
                && crowdfundingInfo.getStatus() != CrowdfundingStatus.APPROVE_DENIED) {
            return CfErrorCode.CF_CAN_NOT_EDIT;
        }

        if (crowdfundingInfo.getUserId() != requestUserId) {
            return CfErrorCode.CF_INFO_PARAM_ERROR_IDENTITY_INVALID;
        }
        return CfErrorCode.SUCCESS;
    }

    /**
     * 检查是否需要显示提交,
     * 会对案例所需的全部数据进行核对，全部存在且没有一个拒绝时为true
     *
     * @param crowdfundingInfo
     * @return
     */
    public boolean checkShowSubmit(CrowdfundingInfo crowdfundingInfo) {
        if (crowdfundingInfo == null || StringUtils.isBlank(crowdfundingInfo.getInfoId())) {
            return false;
        }
        String infoUuid = crowdfundingInfo.getInfoId();
        List<CrowdfundingInfoStatus> infoStatuses = this.crowdfundingInfoStatusBiz.getByInfoUuid(infoUuid);
        if (CollectionUtils.isEmpty(infoStatuses) || infoStatuses.size() < 4) {
            return false;
        }
        Map<Integer, Integer> infoStatusMap = Maps.newHashMap();
        for (CrowdfundingInfoStatus infoStatus : infoStatuses) {
            infoStatusMap.put(infoStatus.getType(), infoStatus.getStatus());
        }
        if (infoStatusMap.size() < 4) {
            return false;
        }
        Integer status = infoStatusMap.get(CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT.getCode());
        if (status == null || status.intValue() == CrowdfundingInfoStatusEnum.REJECTED.getCode()) {
            return false;
        }
        status = infoStatusMap.get(CrowdfundingInfoDataStatusTypeEnum.PATIENT_INFO_SUBMIT.getCode());
        if (status == null || status.intValue() == CrowdfundingInfoStatusEnum.REJECTED.getCode()) {
            return false;
        }
        status = infoStatusMap.get(CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode());
        if (status == null || status.intValue() == CrowdfundingInfoStatusEnum.REJECTED.getCode()) {
            return false;
        }
        status = infoStatusMap.get(CrowdfundingInfoDataStatusTypeEnum.TREATMENT_INFO_SUBMIT.getCode());
        if (status == null || status.intValue() == CrowdfundingInfoStatusEnum.REJECTED.getCode()) {
            return false;
        }

        CfInfoExt currInfoExt = cfInfoExtBiz.getByInfoUuid(crowdfundingInfo.getInfoId());

        if (currInfoExt == null) {
            return true;
        }

        List<CrowdfundingInfoDataStatusTypeEnum> requiredDateTypes = CrowdfundingUtil.getRequiredCaseList(currInfoExt);
        if (requiredDateTypes.contains(CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT)) {
            status = infoStatusMap.get(CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT.getCode());
            if (status == null || status.intValue() == CrowdfundingInfoStatusEnum.REJECTED.getCode()) {
                return false;
            }
        }
        return true;
    }

}
