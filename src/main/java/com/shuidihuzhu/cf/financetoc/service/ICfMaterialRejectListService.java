package com.shuidihuzhu.cf.financetoc.service;

import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.vo.v5.CfMaterialAuditListView;
import com.shuidihuzhu.cf.vo.v5.CfMaterialParam;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Set;

public interface ICfMaterialRejectListService {

    List<CfMaterialAuditListView.AuditModifyEntry> getRejectModifyEntry(CfMaterialParam param);


    default int getEntryStatus(int minorStatus) {
        return minorStatus == CrowdfundingInfoStatusEnum.REJECTED.getCode() ?
                CfMaterialAuditListView.ModifyStatus.NEED_MODIFY.getCode() :
                CfMaterialAuditListView.ModifyStatus.HAS_MODIFY.getCode();
    }

    default int getEntrySuggestStatus(int minorStatus) {
        return minorStatus == CrowdfundingInfoStatusEnum.REJECTED.getCode() ?
                CfMaterialAuditListView.ModifyStatus.SUGGEST_MODIFY.getCode() :
                CfMaterialAuditListView.ModifyStatus.HAS_MODIFY.getCode();
    }

    default Set<Integer> getSuggestCodes(List<CfMaterialAuditListView.ModifySuggest> suggestViews) {
        Set<Integer> suggestCodes = Sets.newHashSet();

        if (CollectionUtils.isEmpty(suggestViews)) {
            return suggestCodes;
        }

        for (CfMaterialAuditListView.ModifySuggest suggest : suggestViews) {
            suggestCodes.add(suggest.getSuggestCode());
        }

        return suggestCodes;
    }


}
