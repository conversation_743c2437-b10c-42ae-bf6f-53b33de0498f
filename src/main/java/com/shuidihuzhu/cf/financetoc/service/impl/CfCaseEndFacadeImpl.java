package com.shuidihuzhu.cf.financetoc.service.impl;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.enums.crowdfunding.CfFinishStatus;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CfInfoExtBiz;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.financetoc.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.financetoc.mq.producer.CommonProducer;
import com.shuidihuzhu.cf.financetoc.service.CfCaseEndFacade;
import com.shuidihuzhu.cf.financetoc.service.CrowdfundingFinishService;
import com.shuidihuzhu.cf.financetoc.service.VerifyCodeService;
import com.shuidihuzhu.cf.model.CaseEndModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.util.CaseUtils;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@SuppressWarnings({"all"})
public class CfCaseEndFacadeImpl implements CfCaseEndFacade {

    @Resource
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Resource
    private CfInfoExtBiz cfInfoExtBiz;
    @Resource
    private CommonProducer commonProducer;
    @Resource
    private CrowdfundingFinishService crowdfundingFinishService;
    @Autowired
    private VerifyCodeService verifyCodeService;
    @Autowired
    private UserInfoDelegate userInfoDelegate;

    @Override
    public OpResult stopCase(int caseId, CfFinishStatus endType, int operatorId) {
        return stopCase(caseId, endType, operatorId, null, 0);
    }

    @Override
    public OpResult stopCase(int caseId, CfFinishStatus endType, int operatorId, String desc, int reasonType) {
        log.info("stopCase caseId:{}, endType:{}, operatorId:{}, currentUserId:{}, desc:{}",
                caseId, endType, operatorId, operatorId, desc);
        CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        String infoUuid = fundingInfo.getInfoId();
        long userId = fundingInfo.getUserId();

        if (CaseUtils.hasEnd(fundingInfo)){
            return OpResult.createSucResult();
        }
        // 更新案例结束时间
        this.crowdfundingInfoBiz.updateEndTime(caseId, new Date());

        // 更新结束状态
        this.cfInfoExtBiz.updateFinishStatusProtect(infoUuid, endType);

        // 老代码发送mq
        crowdfundingFinishService.addTag(userId, infoUuid, endType);
        return onCaseEnd(caseId, endType, operatorId, desc, reasonType);
    }


    @NotNull
    private OpResult onCaseEnd(int caseId, CfFinishStatus endType, int operatorId, String desc, int reasonType) {
        CaseEndModel data = new CaseEndModel(caseId, endType.getValue(), operatorId, desc, reasonType);
        commonProducer.crowdfundingEndMsg(data);
        // 发送案例结束springEvent
        return OpResult.createSucResult();
    }

    /**
     * 用户停止筹款时，验证验证码
     * @param userId
     * @param clientIp
     * @return
     */
    @Override
    public Response checkVerifyCode(long userId, String clientIp,String verifyCode) {
        UserInfoModel userInfoModels = userInfoDelegate.getUserInfoByUserId(userId);
        if(Objects.isNull(userInfoModels) || StringUtils.isBlank(userInfoModels.getCryptoMobile())){
            return NewResponseUtil.makeFail("无法查询手机号码");
        }
        return verifyCodeService.checkVerifyCode(userInfoModels.getCryptoMobile(),verifyCode,clientIp);
    }
}
