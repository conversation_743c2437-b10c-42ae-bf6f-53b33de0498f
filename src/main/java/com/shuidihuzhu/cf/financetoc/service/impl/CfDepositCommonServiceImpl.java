package com.shuidihuzhu.cf.financetoc.service.impl;

import com.shuidihuzhu.cf.finance.model.deposit.CfCaseDepositAccount;
import com.shuidihuzhu.cf.financetoc.biz.pa.CfCaseDepositAccountBiz;
import com.shuidihuzhu.cf.financetoc.service.CfDepositCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * @Author: l<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019/12/15 17:53
 */
@Service
@Slf4j
@RefreshScope
public class CfDepositCommonServiceImpl implements CfDepositCommonService {
	@Autowired
	private CfCaseDepositAccountBiz depositAccountBiz;

	@Override
	public boolean isNeedDepositWithAcc(int caseId) {
		CfCaseDepositAccount cfCaseDepositAccount = depositAccountBiz.getByCaseId(caseId);
        return cfCaseDepositAccount != null;
    }


}
