package com.shuidihuzhu.cf.financetoc.service;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.enums.combine.CombineCommonEnum;
import com.shuidihuzhu.cf.financetoc.biz.order.CfCombineOrderManageBiz;
import com.shuidihuzhu.cf.financetoc.dao.order.*;
import com.shuidihuzhu.cf.model.contribute.CfCombinePayUidInfo;
import com.shuidihuzhu.cf.model.contribute.CfContributeOrder;
import com.shuidihuzhu.cf.model.contribute.ContributeAddOrderDTO;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecordShardingModel;
import com.shuidihuzhu.common.web.enums.PayStatus;
import com.shuidihuzhu.common.web.enums.ValidEnum;
import com.shuidihuzhu.common.web.util.IpUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;

@Slf4j
@Service
@RefreshScope
public class OrderOperateService {

    @Autowired
    private CrowdfundingPayRecordShardingOrderIdDao crowdfundingPayRecordShardingOrderIdDao;
    @Autowired
    private CrowdfundingPayRecordShardingPayUidDao crowdfundingPayRecordShardingPayUidDao;
    @Autowired
    private CrowdfundingOrderShardingIdDao crowdfundingOrderShardingIdDao;
    @Autowired
    private CrowdfundingOrderNewDao crowdfundingOrderNewDao;
    @Autowired
    private CrowdfundingOrderShardingCrowdfundingIdNewDao crowdfundingOrderShardingCrowdfundingIdNewDao;
    @Autowired
    private CfCombineOrderManageBiz orderManageBiz;
    @Autowired
    private CfContributeOrderService contributeOrderService;

    private static final long switchToBeginNewOrderId = 2200000000L;

    /**
     * 2，更新crowdfunding_order_crowdfundingid_sharding_xxx分表
     *
     * @param id
     * @param payStatus
     * @param payTime
     * @return
     */
    public void updateCrowdfundingOrderSharding(Long id, Integer payStatus, Date payTime) {
        try {
            Long crowdfundingId = crowdfundingOrderShardingIdDao.getCrowdfundingIdById(id);
            if (crowdfundingId != null) {
                crowdfundingOrderShardingCrowdfundingIdNewDao.updatePayStatus(crowdfundingId, id, payStatus, payTime);
            } else {
                log.warn("newUpdatePayStatusWarn,id={},payStatus={},payTime={};", id, payStatus, payTime);
            }
        } catch (Exception e) {
            log.error("newUpdatePayStatusErr,id={},payStatus={},payTime={};", id, payStatus, payTime, e);
        }
    }

    /**
     * 1，更新crowdfunding_pay_record_orderid_sharding_000分表
     *
     * @param payUid
     * @param payStatus
     * @param callbackTime
     * @param realPayAmount
     */
    public void updatePayRecordSharding(String payUid, Integer payStatus, Date callbackTime, Integer realPayAmount) {
        try {
            CrowdfundingPayRecordShardingModel crowdfundingPayRecord = crowdfundingPayRecordShardingPayUidDao.getPayRecordShardingModelByPayUid(payUid);
            if (crowdfundingPayRecord != null) {
                crowdfundingPayRecordShardingOrderIdDao.updatePayStatus(Long.valueOf(crowdfundingPayRecord.getCrowdfundingOrderId()),
                        payUid, payStatus, callbackTime, realPayAmount);
            } else {
                log.warn("newUpdatePayStatusWarn,payUid={},payStatus={},callbackTime={},realPayAmount={};", payUid, payStatus, callbackTime, realPayAmount);
            }
        } catch (Exception e) {
            log.error("newUpdatePayStatusErr,payUid={},payStatus={},callbackTime={},realPayAmount={};", payUid, payStatus, callbackTime, realPayAmount, e);
        }
    }


    /**
     * 创建订单
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = CfDataSource.CF_ORDER_NEW_MASTER_DATASOURCE_TRANSACTION_MANAGER)
    public OrderAndPayRecord addOrderAndPayRecord(Integer crowdfundingId, long userId, Integer userThirdId,
                                                 int userThirdType, Integer amountInFen, String comment, String from, String selfTag,
                                                 boolean anonymous, String payUid, String payPlatform,int activityId,
                                                  String clientIp,Integer osType,String channel, CfCombinePayUidInfo combinePayUidInfo) {

        CrowdfundingOrder order = this.createOrder(crowdfundingId, userId, userThirdId, userThirdType, amountInFen, comment
                , clientIp, osType, channel, from, selfTag, anonymous, activityId);

        String recordPayUid = combinePayUidInfo == null ? payUid
                : combinePayUidInfo.getCaseOrderPayUidInfoList().get(0).getCaseSubPayUid();
        CrowdfundingPayRecord record = this.createCrowdfundingPayRecord(recordPayUid, order, payPlatform);

        // 创建打赏订单
        this.createContributeOrder(order, combinePayUidInfo, payPlatform);

        OrderAndPayRecord info =new OrderAndPayRecord();
        info.setCrowdfundingOrder(order);
        info.setCrowdfundingPayRecord(record);
        return  info;

    }

    private CrowdfundingPayRecord createCrowdfundingPayRecord(String recordPayUid, CrowdfundingOrder order, String payPlatform) {
        CrowdfundingPayRecord record = new CrowdfundingPayRecord();
        record.setPayUid(recordPayUid);
        record.setCrowdfundingOrderId(order.getId());
        record.setPayPlatform(payPlatform);
        record.setPrePayAmount(order.getAmount());
        record.setPayStatus(PayStatus.NOT_PAID.getCode());
        record.setCtime(new Date());
        record.setValid(ValidEnum.VALID.getValue());
        return record;
    }

    private CrowdfundingOrder createOrder(Integer crowdfundingId, long userId, Integer userThirdId, int userThirdType, Integer amountInFen, String comment, String clientIp, Integer osType, String channel, String from, String selfTag, boolean anonymous, int activityId) {
        CrowdfundingOrder order = new CrowdfundingOrder();
        order.setCrowdfundingId(crowdfundingId);
        order.setUserId(userId);
        order.setUserThirdId(userThirdId);
        order.setUserThirdType(userThirdType);
        order.setAmount(amountInFen);
        order.setComment(comment);
        order.setPayStatus(PayStatus.NOT_PAID.getCode());
        order.setCtime(new Date());
        order.setValid(ValidEnum.VALID.getValue());
        order.setIp(IpUtil.ip2Long(clientIp));
        order.setOsType(osType);
        order.setChannel(channel);
        order.setFrom(from);
        order.setSelfTag(selfTag);
        order.setAnonymous(anonymous);
        String code = generateOrderCodeV2(order);
        order.setCode(code);
        order.setActivityId(activityId);
        CrowdfundingOrder.CrowdfundingOrderIdHolder idHolder = new CrowdfundingOrder.CrowdfundingOrderIdHolder();
        crowdfundingOrderNewDao.addOrderId(idHolder);
        order.setId(switchToBeginNewOrderId + idHolder.getId());
        return order;
    }

    private void createContributeOrder(CrowdfundingOrder order, CfCombinePayUidInfo combinePayUidInfo, String payPlatform) {
        if (combinePayUidInfo != null && combinePayUidInfo.getCombineBizType() == CombineCommonEnum.CombineOrderBizType.CONTRIBUTE) {
            // 主子订单关系
            CfCombinePayUidInfo.CaseOrderPayUidInfo caseOrderPayUidInfo = combinePayUidInfo.getCaseOrderPayUidInfoList().get(0);
            caseOrderPayUidInfo.setOrderId(order.getId());
            orderManageBiz.insertCombineOrderList(combinePayUidInfo);

            CfContributeOrder contributeOrder = new CfContributeOrder();
            contributeOrder.setCaseId(order.getCrowdfundingId());
            contributeOrder.setUserId(order.getUserId());
            contributeOrder.setAnonymous(order.isAnonymous() ? 1 : 0);
            CfCombinePayUidInfo.ContributeOrderPayUidInfo contributeOrderPayUidInfo = combinePayUidInfo.getContributeOrderPayUidInfo();
            contributeOrder.setPrePayAmount(contributeOrderPayUidInfo.getContributeAmountInFen());
            contributeOrder.setPayUid(contributeOrderPayUidInfo.getContributeSubPayUid());
            contributeOrder.setThirdPayUid("");
            contributeOrder.setCallBackTime(new Date());
            contributeOrder.setComment("");
            CombineCommonEnum.CombineOrderType orderType = CombineCommonEnum.CombineOrderType.CASE_DONATE;
            contributeOrder.setContributeChannel(orderType.name());
            contributeOrder.setContributeSourceId("" + order.getId());
            contributeOrder.setPayPlatform(payPlatform);
            contributeOrder.setIp(order.getIp());
            contributeOrder.setOsType(Objects.requireNonNullElse(order.getOsType(), 0));
            contributeOrder.setSelfTag(Objects.requireNonNullElse(order.getSelfTag(), ""));
            contributeOrder.setUserThirdType(order.getUserThirdType());
            contributeOrder.setProductName(combinePayUidInfo.getCombineProductName());
            contributeOrder.setClientId(orderType.getClientId());
            contributeOrderService.addOrder(contributeOrder);
        }
    }

    private String generateOrderCodeV2(CrowdfundingOrder order) {
        int randomInt = ThreadLocalRandom.current().nextInt(99999);
        DecimalFormat df = new DecimalFormat("00000");
        String randomStr = df.format(randomInt);
        String composeStr = "";
        if (order.getUserId() > 0) {
            composeStr = System.currentTimeMillis() + "" + order.getUserId() + randomStr;
        } else {
            composeStr = System.currentTimeMillis() + "" + order.getUserThirdId() + randomStr;
        }

        BigInteger bigInteger = null;
        try {
            bigInteger = new BigInteger(composeStr);
        } catch (Exception e) {
            log.error("构造order的code出错, composeStr:{}", composeStr);
            return System.currentTimeMillis() + randomStr;
        }
        return bigInteger.toString(32);
    }

    /**
     * 捐献下单
     * @param userId
     * @param userThirdType
     * @param amountInFen
     * @param payUid
     * @param payPlatform
     * @param clientIp
     * @param osType
     * @param productName
     * @param contributeAddOrderDTO
     * @return
     */
    public CfContributeOrder addContributeOrderAndPayRecord(long userId, int userThirdType, Integer amountInFen,
                                                            String payUid, String payPlatform, String clientIp, Integer osType,
                                                             String productName,ContributeAddOrderDTO contributeAddOrderDTO) {
        CfContributeOrder contributeOrder = new CfContributeOrder();
        contributeOrder.setCaseId(0);
        contributeOrder.setUserId(userId);
        contributeOrder.setAnonymous(contributeAddOrderDTO.getAnonymous() ? 1 : 0);

        contributeOrder.setPrePayAmount(amountInFen);
        contributeOrder.setPayUid(payUid);
        contributeOrder.setThirdPayUid("");
        contributeOrder.setCallBackTime(new Date());
        contributeOrder.setComment("");
        CombineCommonEnum.CombineOrderType orderType = CombineCommonEnum.CombineOrderType.CONTRIBUTE;
        contributeOrder.setContributeChannel(orderType.name());
        contributeOrder.setContributeSourceId(contributeAddOrderDTO.getChannel());
        contributeOrder.setPayPlatform(payPlatform);
        contributeOrder.setIp(IpUtil.ip2Long(clientIp));
        contributeOrder.setOsType(Objects.requireNonNullElse(osType, 0));
        contributeOrder.setSelfTag(Objects.requireNonNullElse(contributeAddOrderDTO.getSelfTag(), ""));
        contributeOrder.setUserThirdType(userThirdType);
        contributeOrder.setProductName(productName);
        contributeOrder.setClientId(orderType.getClientId());
        contributeOrderService.addOrder(contributeOrder);
        return contributeOrder;
    }

    @Transactional(rollbackFor = Exception.class, transactionManager = CfDataSource.CF_ORDER_NEW_MASTER_DATASOURCE_TRANSACTION_MANAGER)
    public List<OrderAndPayRecord> addBatchOrderAndPayRecord(long userId, Integer userThirdId,
                                          int userThirdType, String comment, String from, String selfTag,
                                          boolean anonymous, String payUid, String payPlatform, int activityId,
                                          String clientIp, Integer osType, String channel, CfCombinePayUidInfo combinePayUidInfo) {
        if (combinePayUidInfo.getCombineBizType() != CombineCommonEnum.CombineOrderBizType.LOVE_MORE_DONATE) {
            throw new RuntimeException("合并支付类型错误");
        }
        List<OrderAndPayRecord> res = new ArrayList<>(10);
        List<CfCombinePayUidInfo.CaseOrderPayUidInfo> caseOrderPayUidInfoList = combinePayUidInfo.getCaseOrderPayUidInfoList();
        for (CfCombinePayUidInfo.CaseOrderPayUidInfo caseOrderPayUidInfo : caseOrderPayUidInfoList) {
            CrowdfundingOrder order = this.createOrder(caseOrderPayUidInfo.getCaseId(), userId, userThirdId, userThirdType, caseOrderPayUidInfo.getAmountInFen(), comment
                    , clientIp, osType, channel, from, selfTag, anonymous, activityId);
            CrowdfundingPayRecord record = this.createCrowdfundingPayRecord(caseOrderPayUidInfo.getCaseSubPayUid(), order, payPlatform);
            OrderAndPayRecord info = new OrderAndPayRecord();
            info.setCrowdfundingOrder(order);
            info.setCrowdfundingPayRecord(record);
            caseOrderPayUidInfo.setOrderId(order.getId());
            res.add(info);
        }
        orderManageBiz.insertCombineOrderList(combinePayUidInfo);
        return res;
    }

    @Data
    public static class OrderAndPayRecord {
        public CrowdfundingOrder crowdfundingOrder;
        public CrowdfundingPayRecord crowdfundingPayRecord;
    }

}
