package com.shuidihuzhu.cf.financetoc.service.impl;

import com.shuidihuzhu.cf.finance.enums.deposit.DepositStatusEnum;
import com.shuidihuzhu.cf.finance.model.deposit.CfPayeeDepositAccount;
import com.shuidihuzhu.cf.financetoc.delegate.ICfDepositAccountDelegate;
import com.shuidihuzhu.cf.financetoc.service.CfPaDepositPayeeAccountService;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: lianghongchao
 * @Date: 2019/12/16 14:25
 * <p>
 * 四要素银行信息校验
 */
@Service
@Slf4j
@RefreshScope
public class CfPaDepositPayeeAccountServiceImpl implements CfPaDepositPayeeAccountService {
	@Autowired
	private ICfDepositAccountDelegate cfDepositAccountDelegate;
	@Autowired
	private OldShuidiCipher oldShuidiCipher;

	/**
	 * 开户成功的资金账户
	 *
	 * @param payeeName
	 * @param bankCard
	 * @param idCard
	 * @return
	 */
	@Override
	public Response<CfPayeeDepositAccount> getOpenSuccessAccount(String payeeName, String bankCard, String idCard) {
		if (StringUtils.isEmpty(payeeName) || StringUtils.isEmpty(bankCard) || StringUtils.isEmpty(idCard)) {
			return NewResponseUtil.makeFail(null);
		}
		String encryptIdCard = oldShuidiCipher.aesEncrypt(idCard);
		String encryptBankCardNo = oldShuidiCipher.aesEncrypt(bankCard);
		List<CfPayeeDepositAccount> payeeDepositAccountList = cfDepositAccountDelegate.getPayeeAccount(encryptIdCard);
		if (CollectionUtils.isEmpty(payeeDepositAccountList)) {
			return NewResponseUtil.makeFail("不存在注册账户");
		}
		// 同名  同卡 且认证通过的
		payeeDepositAccountList = payeeDepositAccountList.stream()
				.filter(item -> null != item)
				.filter(item -> payeeName.equals(item.getUserName()))
				.filter(item -> encryptBankCardNo.equals(item.getBankCardNo()))
				.filter(item -> DepositStatusEnum.REGISTER_VERIFY_SUCCESS.getCode() == item.getDepositStatus())
				.collect(Collectors.toList());
		if (CollectionUtils.isEmpty(payeeDepositAccountList)) {
			return NewResponseUtil.makeFail("存在注册账户,但是未认证成功");
		}

		return NewResponseUtil.makeSuccess(payeeDepositAccountList.get(0));
	}
}
