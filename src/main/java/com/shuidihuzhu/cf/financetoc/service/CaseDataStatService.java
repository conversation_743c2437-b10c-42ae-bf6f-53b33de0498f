package com.shuidihuzhu.cf.financetoc.service;

import com.shuidihuzhu.cf.constants.AsyncPoolConstants;
import com.shuidihuzhu.cf.model.crowdfunding.vo.OrderResultVo;
import com.shuidihuzhu.client.baseservice.pay.model.PayResultV2;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class CaseDataStatService {
	@Resource(name = "cfOlapCaseStat")
	private RedissonHandler redissonHandler;

	/**
	 * 支付成功页上报
	 *
	 * @param shareDv
	 * @param resultVoResponse
	 */
	@Async(AsyncPoolConstants.SEND_PAY_SUCCESS_OLAP)
	public void sendPaySuccess(String shareDv, Response<OrderResultVo> resultVoResponse) {
		StopWatch stopWatch = StopWatch.createStarted();
		if (null == resultVoResponse) {
			log.info("参数异常 resultVoResponse 为空");
			return;
		}
		OrderResultVo orderResultVo = resultVoResponse.getData();
		if (null == orderResultVo) {
			log.info("参数异常 orderResultVo 为空");
			return;
		}
		PayResultV2 payResultV2 = orderResultVo.getPayInfo();
		if (null == payResultV2) {
			log.info("参数异常 payResultV2 为空");
			return;
		}
		String thirdPayUid = payResultV2.getPayServiceOrderId();
		if (StringUtils.isEmpty(shareDv)) {
			shareDv = "1000";
		}
		redissonHandler.setEX("data-olap-" + thirdPayUid, shareDv, 3L * 3600 * 1000);
		log.debug("sendPaySuccess 耗时:{}", stopWatch.getTime(TimeUnit.SECONDS));
	}

}
