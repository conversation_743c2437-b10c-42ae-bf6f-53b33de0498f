package com.shuidihuzhu.cf.financetoc.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.financetoc.dao.CfCommonStoreDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfCommonStoreModel;
import com.shuidihuzhu.cipher.ShuidiCipher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2020/7/30
 */
@Service
@Slf4j
public class CfCommonStoreService {

    @Autowired
    private CfCommonStoreDao cfCommonStoreDao;
    @Autowired
    private ShuidiCipher shuidiCipher;

    //材审相关类型
    private final static int type_cailiao = 1;

    private final static String id_card="idCard";

    private final static String mobile="mobile";

    private final static String bankCard="bankCard";



    /**
     * 保存材料相关
     * @param userId
     * @param key
     * @param value
     */
    public void saveCailiao(long userId,String key,String value,String infoUuid){

        value = encryptValue(value);
        key = getKey(key,infoUuid);
        save(userId,key,value,type_cailiao);
    }

    private String encryptValue(String value){
        //身份证加密
        if (value.contains(id_card)){
            JSONObject jsonObject =  JSON.parseObject(value);
            String card = jsonObject.getString(id_card);
            if (StringUtils.isNotEmpty(card)){
                card = shuidiCipher.encrypt(card);
            }
            jsonObject.put(id_card,card);
            value = jsonObject.toJSONString();
        }

        if (value.contains(mobile)){
            JSONObject jsonObject =  JSON.parseObject(value);
            String m = jsonObject.getString(mobile);
            m = shuidiCipher.encrypt(m);
            jsonObject.put(mobile,m);
            value = jsonObject.toJSONString();
        }

        if (value.contains(bankCard)){
            JSONObject jsonObject =  JSON.parseObject(value);
            String m = jsonObject.getString(bankCard);
            m = shuidiCipher.encrypt(m);
            jsonObject.put(bankCard,m);
            value = jsonObject.toJSONString();
        }
        return value;
    }

    /**
     * 根据key查询
     * @param userId
     * @param key
     * @return
     */
    public CfCommonStoreModel getByUserIdAndKey(long userId,String key,String infoUuid){
        key = getKey(key,infoUuid);
        CfCommonStoreModel cfCommonStoreModel = cfCommonStoreDao.getByUserIdAndKey(userId,key);
        if (cfCommonStoreModel == null){
            return null;
        }
        List<CfCommonStoreModel> list = decryptValue(Lists.newArrayList(cfCommonStoreModel));
        return list.get(0);
    }

    /**
     * 根据key查询
     * @param userId
     * @param keys
     * @return
     */
    public List<CfCommonStoreModel> getByKeys(long userId,List<String> keys,String infoUuid){
        if (userId <= 0 || CollectionUtils.isEmpty(keys)){
            return Lists.newArrayList();
        }

        List<CfCommonStoreModel> result = cfCommonStoreDao.getByKeys(userId,keys.stream().map(r->getKey(r,infoUuid)).collect(Collectors.toList()));

        result = decryptValue(result);

        return result;
    }

    private List<CfCommonStoreModel> decryptValue(List<CfCommonStoreModel> result){
        result.stream().filter(r->StringUtils.isNotEmpty(r.getStoreValue())).forEach(r->{

            if (r.getStoreValue().contains(id_card)){
                JSONObject jsonObject =  JSON.parseObject(r.getStoreValue());
                String old = jsonObject.getString(id_card);
                String newParam = shuidiCipher.decrypt(old);
                if (StringUtils.isEmpty(newParam)){
                    newParam = old;
                }
                jsonObject.put(id_card,newParam);
                r.setStoreValue(jsonObject.toJSONString());
            }

            if (r.getStoreValue().contains(mobile)){
                JSONObject jsonObject =  JSON.parseObject(r.getStoreValue());
                String old = jsonObject.getString(mobile);
                String newParam = shuidiCipher.decrypt(old);
                if (StringUtils.isEmpty(newParam)){
                    newParam = old;
                }
                jsonObject.put(mobile,newParam);
                r.setStoreValue(jsonObject.toJSONString());
            }

            if (r.getStoreValue().contains(bankCard)){
                JSONObject jsonObject =  JSON.parseObject(r.getStoreValue());
                String old = jsonObject.getString(bankCard);
                String newParam = shuidiCipher.decrypt(old);
                if (StringUtils.isEmpty(newParam)){
                    newParam = old;
                }
                jsonObject.put(bankCard,newParam);
                r.setStoreValue(jsonObject.toJSONString());
            }
        });
        return result;
    }

    private void save(long userId,String key,String value,int type){

        CfCommonStoreModel model = new CfCommonStoreModel();
        model.setUserId(userId);
        model.setStoreKey(key);
        model.setStoreValue(value);
        model.setStoreType(type);
        model.setUpdateTime(new Date());
        cfCommonStoreDao.insert(model);

    }


    private String getKey(String key,String infoUuid){

        //驳回时已经包含了 infoUuid  不需要封装key
        if (!key.contains(CfCaseMaterialListService.ENTRY_SPLIT)) {
            key = infoUuid + "_" + key;
        }
        return key;
    }

}
