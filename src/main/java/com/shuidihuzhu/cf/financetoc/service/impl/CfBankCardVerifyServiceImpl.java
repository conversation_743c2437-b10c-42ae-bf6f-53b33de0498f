package com.shuidihuzhu.cf.financetoc.service.impl;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.BankCardVerifyStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.financetoc.biz.payee.BankCardVerifyRecordBiz;
import com.shuidihuzhu.cf.financetoc.service.CfBankCardVerifyService;
import com.shuidihuzhu.cf.financetoc.util.CfVerifyBankCardCountUtil;
import com.shuidihuzhu.cf.model.crowdfunding.BankCardVerifyRecord;
import com.shuidihuzhu.cf.model.crowdfunding.BankCardVerifyResult;
import com.shuidihuzhu.cf.model.crowdfunding.CfVerifyBankCardCountResult;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingBankVerifyResultVo;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: lianghongchao
 * @Date: 2019/12/16 14:25
 * <p>
 */
@Service
@Slf4j
public class CfBankCardVerifyServiceImpl implements CfBankCardVerifyService {
    @Autowired
    private CfVerifyBankCardCountUtil cfVerifyBankCardCountUtil;
    @Autowired
    private BankCardVerifyRecordBiz bankCardVerifyRecordBiz;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    /**
     * 银行卡三要素校验
     *
     * @param crowdfundingInfo
     * @param payeeName
     * @param bankCard
     * @param idCard
     * @return
     */
    @Override
    public Response<CrowdfundingBankVerifyResultVo> checkCardByThreeElements(CrowdfundingInfo crowdfundingInfo,
                                                                             String payeeName, String bankCard, String idCard) {
        String cryptoBankCard = oldShuidiCipher.aesEncrypt(bankCard);
        String cryptoIdCard = oldShuidiCipher.aesEncrypt(idCard);
        Response<CrowdfundingBankVerifyResultVo> response = new Response<>();
        boolean hasCheckBankCard = cryptoBankCard.equals(crowdfundingInfo.getPayeeBankCard())
                && payeeName.equals(crowdfundingInfo.getPayeeName())
                && cryptoIdCard.equals(crowdfundingInfo.getPayeeIdCard())
                && BankCardVerifyStatus.passed.equals(crowdfundingInfo.getBankCardVerifyStatus());
        if (hasCheckBankCard) {
            response.setCode(CfErrorCode.SUCCESS.getCode());
            response.setMsg("案例表已经认证通过");
            return response;
        }
        // 2020年03月11日 一个案例的一个三要素认证成功之后 就不走三方数据接口
        List<BankCardVerifyRecord> bankCardVerifyRecordList = bankCardVerifyRecordBiz.getByCaseIdAndElements(payeeName,
                cryptoIdCard, cryptoBankCard, crowdfundingInfo.getId());
        if (bankCardVerifyRecordList.stream()
                .anyMatch(item -> item.getVerifyStatus().equals(BankCardVerifyStatus.passed))) {
            log.info("caseId:{} 已经认证通过，不再调用三方接口",crowdfundingInfo.getId());
            response.setCode(CfErrorCode.SUCCESS.getCode());
            response.setMsg("案例表已经认证通过");
            return response;
        }
        /**
         * 统一的银行卡校验,每个人的校验次数隔日刷新
         */
        CfVerifyBankCardCountResult checkResult = cfVerifyBankCardCountUtil.getBankCardVerifyCount(crowdfundingInfo.getUserId());
        if (checkResult.getCount() >= checkResult.getMax()) {
            return getMaxCountError(crowdfundingInfo.getInfoId(), checkResult.getCount());
        }
        return checkCardWithThird(crowdfundingInfo.getUserId(), payeeName, bankCard, idCard, crowdfundingInfo.getId(),
                checkResult, crowdfundingInfo.getInfoId());
    }


    /**
     * 与三方 进行银行卡鉴权校验
     *
     * @param userId
     * @param payeeName
     * @param bankCard
     * @param idCard
     * @param crowdfundingId
     * @param countResult
     * @param infoUuid
     * @return
     */
    private Response<CrowdfundingBankVerifyResultVo> checkCardWithThird(long userId, String payeeName, String bankCard,
                                                                        String idCard, int crowdfundingId,
                                                                        CfVerifyBankCardCountResult countResult, String infoUuid) {
        // 验证银行卡
        UserIdentityType identityType = UserIdentityType.identity;
        BankCardVerifyResult bankCardVerifyResult = this.bankCardVerifyRecordBiz.verify(payeeName, identityType, idCard,
                bankCard, crowdfundingId, userId, false);
        // 记录次数
        countResult.increaseCount();
        cfVerifyBankCardCountUtil.saveBankVerifyCountOfUser(userId, countResult.getCount());
        Response<CrowdfundingBankVerifyResultVo> response = new Response<>();
        if (bankCardVerifyResult == null) {
            log.warn("bankCardVerifyRecordBiz verify Error! bankCardVerifyResult is null");
            response = getVerifyResultResponse(infoUuid, "银行卡信息校验失败，请稍后再试",
                    "", "", CfErrorCode.CF_BANK_CARD_VERIFICATION_FAILED.getCode(),
                    StringUtils.EMPTY);
            return response;
        }
        if (bankCardVerifyResult.isOk()) {
            response.setCode(CfErrorCode.SUCCESS.getCode());
            response.setMsg("银行卡校验三方成功");
            return response;
        }
        /**
         * 填充失败信息
         */
        log.warn("bankCardVerifyRecordBiz verify Error! bankCardVerifyResult :{}",
                JSON.toJSONString(bankCardVerifyResult));
        // 业务日志
        String message = countResult.getMax() > 0
                ? "银行卡信息输入不正确，今日还剩" + (countResult.getMax() - countResult.getCount()) + "次验证机会"
                : "信息输入不正确";
        response = getVerifyResultResponse(infoUuid, message,
                bankCardVerifyResult.getFinalMessage(), bankCardVerifyResult.getResponseMsg(),
                CfErrorCode.CF_BANK_CARD_VERIFICATION_FAILED.getCode(), StringUtils.EMPTY);
        return response;
    }

    /***
     * 验证次数上限
     * @param infoUuid
     * @param count
     * @return
     */
    private Response<CrowdfundingBankVerifyResultVo> getMaxCountError(String infoUuid, int count) {
        Response<CrowdfundingBankVerifyResultVo> response = NewResponseUtil.makeError(
                CfErrorCode.CF_BANK_CARD_VERIFICATION_REACH_MAX_TIME);
        CrowdfundingBankVerifyResultVo bankVerifyResultVo = new CrowdfundingBankVerifyResultVo(
                "您的验证已达上限，请核对后明日填写", "", "");
        bankVerifyResultVo.setInfoUuid(infoUuid);
        response.setData(bankVerifyResultVo);
        return response;
    }

    /**
     * 构建数据验证的结果
     *
     * @param infoUuid
     * @param message
     * @param verifyMessage1
     * @param verifyMessage2
     * @param cfErrorCode
     * @param paSubAcctNo
     * @return
     */
    private Response<CrowdfundingBankVerifyResultVo> getVerifyResultResponse(String infoUuid, String message,
                                                                             String verifyMessage1, String verifyMessage2,
                                                                             int cfErrorCode, String paSubAcctNo) {
        Response<CrowdfundingBankVerifyResultVo> response = new Response<>();
        CrowdfundingBankVerifyResultVo bankVerifyResultVo = new CrowdfundingBankVerifyResultVo(message, verifyMessage1,
                verifyMessage2);
        bankVerifyResultVo.setPaSubAcctNo(paSubAcctNo);
        bankVerifyResultVo.setInfoUuid(infoUuid);
        response.setCode(cfErrorCode);
        response.setData(bankVerifyResultVo);
        return response;
    }
}
