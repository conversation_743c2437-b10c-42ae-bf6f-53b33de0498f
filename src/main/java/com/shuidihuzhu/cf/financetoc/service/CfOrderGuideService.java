package com.shuidihuzhu.cf.financetoc.service;

import com.shuidihuzhu.cf.mq.model.OrderGuideToSdbModel;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.baseservice.pay.enums.BizType;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * @Author: liujiawei
 * @Date: 2020/5/9  14:32
 * 微信支付导购相关
 */
@Service
@Slf4j
public class CfOrderGuideService {
    @Autowired
    private CommonMessageHelperService commonMessageHelperService;

    /**
     * 筹下单支付导购转保险
     *
     * @param payUid      我方单号
     * @param thirdPayUid 支付中心单号
     * @param userId userId
     */
    @Async
    public void payOrderGuideToSdb(String payUid, String thirdPayUid, long userId,
                                   String subPayUid, String subThirdPayUid) {
        if (StringUtils.isBlank(payUid) || StringUtils.isBlank(thirdPayUid) || userId <= 0) {
            return;
        }
        OrderGuideToSdbModel orderGuideToSdbModel = new OrderGuideToSdbModel();
        orderGuideToSdbModel.setDisplayId(payUid);
        orderGuideToSdbModel.setThirdPaySeq(thirdPayUid);
        orderGuideToSdbModel.setUserId(userId);

        orderGuideToSdbModel.setSubDisplayId(subPayUid);
        orderGuideToSdbModel.setSubThirdPaySeq(subThirdPayUid);
        //目前仅托管商户号做导购
        orderGuideToSdbModel.setBizType(BizType.CF_DEPOSIT.getCode());
        try {
            Message message = commonMessageHelperService.getOrderGuideToSdbMsg(orderGuideToSdbModel);
            OpResult<MessageResult> opResult = commonMessageHelperService.send(message);
            if (opResult.isSuccess()) {
                log.info("筹转保导购信息mq发送成功,{}", orderGuideToSdbModel);
            }
        } catch (Exception e) {
            log.error("发送导购信息MQ异常,{},", orderGuideToSdbModel, e);
        }
    }

}
