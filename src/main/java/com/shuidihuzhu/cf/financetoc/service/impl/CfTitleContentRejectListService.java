package com.shuidihuzhu.cf.financetoc.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.financetoc.service.ICfMaterialRejectListService;
import com.shuidihuzhu.cf.vo.v5.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
public class CfTitleContentRejectListService implements ICfMaterialRejectListService {

    @Override
    public List<CfMaterialAuditListView.AuditModifyEntry> getRejectModifyEntry(CfMaterialParam param) {

        List<CfMaterialAuditListView.AuditModifyEntry> allEntry = Lists.newArrayList();
        int entryStatus = getEntryStatus(param.getMaterialAuditStatus());

        CfMaterialAuditListView.AuditModifyEntry modifyEntry = new CfMaterialAuditListView.AuditModifyEntry(
                MaterialAuditEntry.ASK_FOR_HELP, entryStatus);

        List<CfMaterialAuditListView.FieldItemStatus> allFields = Lists.newArrayList();
        Map<Integer, Set<String>> rejectDetails = param.getRejectDetails();
        if (MapUtils.isEmpty(rejectDetails)) {
            log.info("案例的图文是驳回/修改状态不能找到驳回理由.infoUuid:{}", param.getInfoUuid());
            return allEntry;
        }

        if (rejectDetails.containsKey(MaterialRejectPositionType.CASE_TITLE.getCode())) {
            allFields.add(new CfMaterialAuditListView.FieldItemStatus(CfMaterialFieldName.title, entryStatus));
        }
        if (rejectDetails.containsKey(MaterialRejectPositionType.CASE_CONTENT.getCode())) {
            allFields.add(new CfMaterialAuditListView.FieldItemStatus(CfMaterialFieldName.content, entryStatus));
        }
        if (rejectDetails.containsKey(MaterialRejectPositionType.CASE_IMAGE.getCode())) {
            allFields.add(new CfMaterialAuditListView.FieldItemStatus(CfMaterialFieldName.images, entryStatus));
        }

        modifyEntry.setAllFields(allFields);
        allEntry.add(modifyEntry);

        return allEntry;
    }
}
