package com.shuidihuzhu.cf.financetoc.service;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceWriteFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.financetoc.biz.refund.CfDonorApplyRefundBiz;
import com.shuidihuzhu.cf.financetoc.biz.refund.CfDonorRefundBlacklistBiz;
import com.shuidihuzhu.cf.financetoc.util.ConfusionUtils;
import com.shuidihuzhu.cf.model.crowdfunding.pay.CfDonorRefundBlacklist;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.msg.util.MobileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 代码迁移from CfV5RefundController
 * <AUTHOR>
 */
@Slf4j
@Service
@RefreshScope
public class DonationOrderRefundFacade {
    @Autowired
    private CfDonorRefundBlacklistBiz cfDonorRefundBlacklistBiz;
    @Resource
    private CfFinanceWriteFeignClient cfFinanceWriteFeignClient;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Autowired
    private CfDonorApplyRefundBiz cfDonorApplyRefundBiz;


    public Response<String> submitRefund(long userId, int orderId, int amountInFen, String refundReason, String refundName,
                                         String mobile, String infoUuid, Long newOrderId) {
        log.info("submit-refund-apply orderId:{}, newOrderId:{}", orderId, newOrderId);
        if (newOrderId == 0 || amountInFen <= 0 || StringUtils.isEmpty(infoUuid)) {
            log.info("参数不合法");
            return NewResponseUtil.makeFail("无法退款，请联系客服");
        }
        long recoveredId = ConfusionUtils.recoverOrderId(newOrderId);
        log.info("submit-refund-apply recoveredId:{}", recoveredId);

        if (StringUtils.isEmpty(refundReason) || StringUtils.isEmpty(mobile)) {
            log.info("参数不合法");
            return NewResponseUtil.makeFail("请核对退款人姓名，退款原因，手机号是否填写");
        }
        if (StringUtils.isEmpty(refundName)) {
            refundName = "系统默认";
        }
        if (userId <= 0) {
            return NewResponseUtil.makeError(CfErrorCode.CF_USER_ACCOUNT_NO_LOGIN);
        }
        if (MobileUtil.illegal(mobile)) {
            log.info("手机号参数不合法");
            return NewResponseUtil.makeFail("请核对手机号是否填写正确");
        }

        Response<Boolean> response = this.donorCanApplyRefund(userId);
        if (response.notOk() || !response.getData()) {
            log.info("orderId:{}不允许操作退款", recoveredId);
            return NewResponseUtil.makeFail("无法提交申请，如有疑问请拨打************");
        }

        FeignResponse<Response> feignResponse = cfFinanceWriteFeignClient.submitRefundApply(Long.valueOf(recoveredId), amountInFen,
                refundReason, refundName, oldShuidiCipher.aesEncrypt(mobile), infoUuid, userId);
        log.info("orderId:{}\tfeignResponse:{}", recoveredId, JSON.toJSONString(feignResponse));
        if (null == feignResponse || feignResponse.notOk()) {
            log.info("feignResponse.orderId:{} 退款失败", recoveredId);
            return NewResponseUtil.makeFail("无法提交申请，如有疑问请拨打************");
        }
        response = feignResponse.getData();
        if (null == response || response.notOk()) {
            log.info("response.orderId:{} 退款失败", recoveredId);
            return NewResponseUtil.makeFail("无法提交申请，如有疑问请拨打************");
        }
        log.info("退款申请成功");
        return NewResponseUtil.makeSuccess("");
    }


    /**
     * 捐款人是否可以申请退款
     *
     * @param userId
     * @return
     */
    public Response<Boolean> donorCanApplyRefund(long userId) {
        CfDonorRefundBlacklist cfDonorRefundBlacklist = cfDonorRefundBlacklistBiz.getByUserId(userId);
        if (Objects.nonNull(cfDonorRefundBlacklist)) {
            log.info("userId:{} 在黑名单内", cfDonorRefundBlacklist.getId());
            return NewResponseUtil.makeSuccess(false);
        }

        boolean hasFunding = cfDonorApplyRefundBiz.hasFunding(userId);
        return NewResponseUtil.makeSuccess(hasFunding);
    }
}
