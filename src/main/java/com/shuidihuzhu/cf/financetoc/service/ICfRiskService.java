package com.shuidihuzhu.cf.financetoc.service;

import com.shuidihuzhu.cf.model.crowdfunding.CfCaseVisitConfig;
import com.shuidihuzhu.client.cf.risk.model.CfRiskQueryOperateParam;
import com.shuidihuzhu.client.cf.risk.model.enums.PhoneOperateEnum;
import com.shuidihuzhu.client.cf.risk.model.enums.RiskOperateSourceEnum;
import com.shuidihuzhu.client.cf.risk.model.enums.UserOperationEnum;
import com.shuidihuzhu.client.cf.risk.model.result.UserOperatorValidMultiUnit;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @time 2018/11/15 上午10:56
 * @desc
 */
public interface ICfRiskService {
    boolean operatorValid(long userId, int caseId, UserOperationEnum userOperationEnum);
}
