package com.shuidihuzhu.cf.financetoc.service.impl;

import com.shuidihuzhu.cf.financetoc.dao.crowdfunding.CfHuKouOptionDao;
import com.shuidihuzhu.cf.financetoc.service.CfHuKouService;
import com.shuidihuzhu.cf.model.crowdfunding.HuKouOptionDO;
import com.shuidihuzhu.cf.model.crowdfunding.vo.HuKouOption;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2024/6/11 2:09 PM
 */
@Slf4j
@Service
public class CfHuKouServiceImpl implements CfHuKouService {

    @Resource
    private CfHuKouOptionDao cfHuKouOptionDao;

    @Override
    public void saveHuKouOption(HuKouOption huKouOption, Integer caseId) {
        if (Objects.isNull(huKouOption)) {
            return;
        }
        HuKouOptionDO huKouOptionDO = buildOptionDO(huKouOption, caseId);
        cfHuKouOptionDao.saveHuKouOption(huKouOptionDO);
    }

    @Override
    public void updateHuKouOption(HuKouOption huKouOption, Integer caseId) {
        if (Objects.isNull(huKouOption)) {
            return;
        }

        HuKouOptionDO huKouOptionDO = buildOptionDO(huKouOption, caseId);
        cfHuKouOptionDao.updateHuKouOption(huKouOptionDO);
    }

    @Override
    public HuKouOption selectHuKouOptionByCaseId(Integer caseId) {
        HuKouOptionDO huKouOptionDO = cfHuKouOptionDao.selectByCaseId(caseId);
        if (Objects.isNull(huKouOptionDO)) {
            return null;
        }

        HuKouOption huKouOption = new HuKouOption();
        huKouOption.setHuhaoType(huKouOptionDO.getHuHaoType());
        huKouOption.setHukouType(huKouOptionDO.getHuKouType());
        huKouOption.setHukouMaterialType(huKouOptionDO.getMaterialType());
        return huKouOption;
    }

    private HuKouOptionDO buildOptionDO(HuKouOption huKouOption, Integer caseId) {
        HuKouOptionDO huKouOptionDO = new HuKouOptionDO();
        huKouOptionDO.setCaseId(caseId);
        huKouOptionDO.setHuHaoType(Optional.ofNullable(huKouOption.getHuhaoType()).orElse(0));
        huKouOptionDO.setHuKouType(Optional.ofNullable(huKouOption.getHukouType()).orElse(0));
        huKouOptionDO.setMaterialType(Optional.ofNullable(huKouOption.getHukouMaterialType()).orElse(0));
        return huKouOptionDO;
    }
}
