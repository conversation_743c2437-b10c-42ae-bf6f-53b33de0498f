package com.shuidihuzhu.cf.financetoc.service;

import com.google.common.collect.Lists;
import com.shuidi.weixin.common.bean.result.WxError;
import com.shuidi.weixin.common.exception.WxErrorException;
import com.shuidi.weixin.mp.api.WxMpService;
import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.constants.WxConstants;
import com.shuidihuzhu.cf.constants.crowdfunding.DSTokenInfo;
import com.shuidihuzhu.cf.enums.crowdfunding.UserTagEnum;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.financetoc.delegate.UserThirdDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.wx.biz.ShuidiWxService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Ahrievil
 * @date : 2018/5/10 16:26
 */
@Slf4j
@Service
public class UserWxTagService {

    private static final Integer THIRD_TYPE = WxConstants.FUNDRAISER_THIRD_TYPE;

    @Autowired
    private ShuidiWxService shuidiWxService;
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private UserThirdDelegate userThirdDelegate;
    @Autowired
    private Analytics analytics;

    public boolean addTagToEndChouKuan(List<UserThirdModel> userThirdModels, WxMpService wxMpService, UserTagEnum userTagEnum) {
        if (CollectionUtils.isEmpty(userThirdModels)) {
            return false;
        }
        log.debug("UserWxTagService addTagToEndChouKuan randomByGroup:{}", userTagEnum);
        List<List<UserThirdModel>> partition = Lists.partition(userThirdModels, 3000);
        partition.forEach(list -> {
            List<List<String>> userThirdPartition = Lists.partition(list.stream()
                    .map(UserThirdModel::getOpenId).collect(Collectors.toList()), 50);
            userThirdPartition.forEach(openIdList -> {
                try {
                    wxMpService.addTagToOpenIdList(userTagEnum.getTagId(), openIdList);
                } catch (WxErrorException e) {
                    handlerWxError(e, 0);
                } catch (Exception e) {
                    log.debug("UserWxTagService addTagToEndChouKuan error", e);
                }
            });
            List<Long> userIds = list.stream().map(UserThirdModel::getUserId).collect(Collectors.toList());
            userIds.forEach(val -> addTagToUser(val, userTagEnum));
        });
        return true;
    }


    public boolean addTagToUser(long userId, UserTagEnum tag) {
        analytics.profileSet(DSTokenInfo.NEW_TOKEN, String.valueOf(userId), DSTokenInfo.BIZ, DSTokenInfo.PROPER_TY_CHOUKUAN_STATUS, tag.getWord());
        return true;
    }

    public boolean endRaiseHandler(long userId) {
        if (userId <= 0) {
            return false;
        }
        List<CrowdfundingInfo> crowdfundingInfos = crowdfundingInfoBiz.selectByUserId(userId);
        UserThirdModel userThirdModel = userThirdDelegate.getThirdModelWithUserId(userId, THIRD_TYPE);
        if (userThirdModel == null) {
            return false;
        }
        Date now = new Date();
        WxMpService wxMpService = shuidiWxService.getWxService(THIRD_TYPE);
        if (crowdfundingInfos.stream().noneMatch(val -> val.getEndTime().after(now))) {
            List<Long> tagId;
            try {
                tagId = wxMpService.getTagIdListByOpenId(userThirdModel.getOpenId());
            } catch (WxErrorException e) {
                handlerWxError(e, userId);
                return false;
            } catch (Exception e) {
                log.debug("UserWxTagService endRaiseHandler error", e);
                return false;
            }
            if (tagId.stream().anyMatch(UserTagEnum::isTagIdContains)) {
                if (UserTagEnum.isConatinsByGroup(tagId, 1)) {
                    UserTagEnum pre = UserTagEnum.getTagIdByGroupInList(tagId, 1);
                    if (pre == null) {
                        return false;
                    }
                    int testGroup = pre.getTestGroup();
                    deleteTagFromOpenId(Lists.newArrayList(userThirdModel.getOpenId()), pre.getTagId(), wxMpService);
                    UserTagEnum byGroupAndTest = UserTagEnum.getByGroupAndTest(3, testGroup);
                    return addTagToEndChouKuan(Lists.newArrayList(userThirdModel), wxMpService, byGroupAndTest);
                } else if (UserTagEnum.isConatinsByGroup(tagId, 2)) {
                    UserTagEnum ing = UserTagEnum.getTagIdByGroupInList(tagId, 2);
                    if (ing == null) {
                        return false;
                    }
                    int testGroup = ing.getTestGroup();
                    deleteTagFromOpenId(Lists.newArrayList(userThirdModel.getOpenId()), ing.getTagId(), wxMpService);
                    UserTagEnum byGroupAndTest = UserTagEnum.getByGroupAndTest(3, testGroup);
                    return addTagToEndChouKuan(Lists.newArrayList(userThirdModel), wxMpService, byGroupAndTest);
                }
            } else {
                UserTagEnum userTagEnum = UserTagEnum.getRandomByGroup(3);
                return addTagToEndChouKuan(Lists.newArrayList(userThirdModel), wxMpService, userTagEnum);
            }
        }

        return true;
    }

    public void deleteTagFromOpenId(List<String> openIdList, long tagId, WxMpService wxMpService) {
        List<List<String>> partition = Lists.partition(openIdList, 50);
        partition.forEach(list -> {
            try {
                wxMpService.deleteTagFromOpenIdList(tagId, list);
            } catch (WxErrorException e) {
                log.debug("UserWxTagService deleteTagFromOpenId error", e);
            }
        });
    }

    public void handlerWxError(WxErrorException wxErrorException, long userId) {
        WxError error = wxErrorException.getError();
        if (error != null) {
            int errorCode = error.getErrorCode();
            if (errorCode == 43004 || errorCode == 50005) {
                log.debug("该用户没有关注，添加标签失败 userId：{}", userId);
            } else {
                log.debug("提阿尼啊标签失败", wxErrorException);
            }
        }
    }

}
