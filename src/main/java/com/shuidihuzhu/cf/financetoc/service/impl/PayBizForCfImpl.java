package com.shuidihuzhu.cf.financetoc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.enums.combine.CombineCommonEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfInfoExtEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfPayOrigin;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingType;
import com.shuidihuzhu.cf.financetoc.biz.WxMpConfigCacheBiz;
import com.shuidihuzhu.cf.financetoc.delegate.IPayClientRpcAdapter;
import com.shuidihuzhu.cf.financetoc.delegate.UserThirdDelegate;
import com.shuidihuzhu.cf.financetoc.service.PayBizForCf;
import com.shuidihuzhu.cf.financetoc.util.CFNowpayConfigUtil;
import com.shuidihuzhu.cf.financetoc.util.CrowdfundingUtil;
import com.shuidihuzhu.cf.financetoc.util.PayUtil;
import com.shuidihuzhu.cf.financetoc.util.PlatformUtil;
import com.shuidihuzhu.cf.model.contribute.CfCombinePayInfo;
import com.shuidihuzhu.cf.model.contribute.CfCombinePayUidInfo;
import com.shuidihuzhu.cf.vo.PayInfoVo;
import com.shuidihuzhu.client.baseservice.pay.enums.*;
import com.shuidihuzhu.client.baseservice.pay.model.PayResultV2;
import com.shuidihuzhu.client.baseservice.pay.model.PayRpcResponse;
import com.shuidihuzhu.client.baseservice.pay.model.v3.PayInfoParamV3;
import com.shuidihuzhu.client.baseservice.pay.model.v3.combine.*;
import com.shuidihuzhu.client.baseservice.pay.v3.CombinePayClientV3;
import com.shuidihuzhu.common.web.enums.Platform;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;
import com.shuidihuzhu.wx.model.WxMpConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * @author: lixuan
 * @date: 2018/3/19 12:12
 */
@Slf4j
@Service
public class PayBizForCfImpl implements PayBizForCf {

	@Autowired
	private UserThirdDelegate userThirdDelegate;
	@Autowired
	private CFNowpayConfigUtil cFNowpayConfigUtil;
	@Autowired
	private PayUtil payUtil;
	@Autowired
	private IPayClientRpcAdapter payClientRpcAdapter;
	@Autowired
	private WxMpConfigCacheBiz wxMpConfigCacheBiz;
	@Autowired
	private CombinePayClientV3 combinePayV3;

	@Override
	public PayResultV2 payCombineOrder(CfCombinePayInfo payInfo) {
		CombinePayWxParam wxParam = new CombinePayWxParam();
		wxParam.setOpenId(payInfo.getOpenId());
		wxParam.setWxAppId(payInfo.getWxAppId());

		BizType wxBizTypeEnum = getWxBizType(payInfo.getCrowdfundingType(), payInfo.getCfPayOriginEnum(), DepositTypeEnum.NEED);
		PayType payTypeEnum = getPayTypeEnum(payInfo.getCfPayOriginEnum());
		List<CombinePaySubInfoVO> subInfoVOList = Lists.newArrayList();

		CfCombinePayUidInfo combinePayInfo = payInfo.getCombinePayInfo();
		List<CfCombinePayUidInfo.CaseOrderPayUidInfo> caseOrderPayUidInfoList = combinePayInfo.getCaseOrderPayUidInfoList();
		CfCombinePayUidInfo.ContributeOrderPayUidInfo contributeOrderPayUidInfo = combinePayInfo.getContributeOrderPayUidInfo();
		// 资助打赏
		if (combinePayInfo.getCombineBizType() == CombineCommonEnum.CombineOrderBizType.CONTRIBUTE) {
			CfCombinePayUidInfo.CaseOrderPayUidInfo caseOrderPayUidInfo = caseOrderPayUidInfoList.get(0);
			CombinePaySubInfoVO casePaySubInfo = this.buildCasePaySubInfoVO(wxBizTypeEnum, caseOrderPayUidInfo);
			CombinePaySubInfoVO contributeSubInfo = buildContributePaySubInfoVO(combinePayInfo);
			subInfoVOList.add(casePaySubInfo);
			subInfoVOList.add(contributeSubInfo);
		} else if (combinePayInfo.getCombineBizType() == CombineCommonEnum.CombineOrderBizType.LOVE_MORE_DONATE) {
			subInfoVOList = caseOrderPayUidInfoList.stream()
					.map(item -> this.buildCasePaySubInfoVO(wxBizTypeEnum, item))
					.collect(Collectors.toList());
		}

		CombinePayParam payParam =
				CombinePayParam.builder()
						.clientId(payInfo.getClientId())
						.userId(payInfo.getUserId())
						.orderId(combinePayInfo.getParentPayUid())
						.combinePaySubInfoList(subInfoVOList)
						.payType(payTypeEnum.getCode())
						.confCode("10000000002")
						.bizType(wxBizTypeEnum.getCode())
						.combinePayWxParam(wxParam)
						.callbackUrl(cFNowpayConfigUtil.getCombinePayCallBackUrl())
						.platform(PlatformUtil.transToPay(payInfo.getPlatform()).getCode())
						.build();

		PayRpcResponse<CombinePayResult> combineResult = combinePayV3.unifiedOrder(payParam);
		log.info("合单支付的param:{} result:{}", JSON.toJSONString(payParam), JSON.toJSONString(combineResult));

		if (combineResult == null || combineResult.getResult() == null) {
			return null;
		}

		if (CollectionUtils.isNotEmpty(combineResult.getResult().getSubInfoVOS())) {
			for (CombinePayResultSubInfoVO subInfoVO : combineResult.getResult().getSubInfoVOS()) {
				caseOrderPayUidInfoList.stream()
						.filter(r -> Objects.equals(subInfoVO.getSubOrderId(), r.getCaseSubPayUid()))
						.findFirst()
						.ifPresent(r -> r.setCaseThirdSubPayUid(subInfoVO.getSubPayUid()));

				if (contributeOrderPayUidInfo != null && Objects.equals(subInfoVO.getSubOrderId(), contributeOrderPayUidInfo.getContributeSubPayUid())) {
					contributeOrderPayUidInfo.setContributeThirdSubPayUid(subInfoVO.getSubPayUid());
				}
			}
		}

		PayResultV2 resultV2 = new PayResultV2();
		resultV2.setPayServiceOrderId(combineResult.getResult().getPayServiceOrderId());
		resultV2.setUnifiedOrderType(combineResult.getResult().getUnifiedOrderType());
		resultV2.setSignature(combineResult.getResult().getSignature());
		resultV2.setRedirectUrl(combineResult.getResult().getRedirectUrl());

		return resultV2;
	}

	private CombinePaySubInfoVO buildCasePaySubInfoVO(BizType wxBizTypeEnum, CfCombinePayUidInfo.CaseOrderPayUidInfo caseOrderPayUidInfo) {
		CombinePaySubInfoVO casePaySubInfo = new CombinePaySubInfoVO();
		String productName = caseOrderPayUidInfo.getProductName();
		casePaySubInfo.setBizType(wxBizTypeEnum.getCode());
		casePaySubInfo.setAmountInFen(caseOrderPayUidInfo.getAmountInFen());
		casePaySubInfo.setSubOrderId(caseOrderPayUidInfo.getCaseSubPayUid());
		casePaySubInfo.setSubOrderName(payUtil.productName(productName));
		casePaySubInfo.setCaseId(CrowdfundingUtil.buildPingAnCode(productName));
		casePaySubInfo.setDeposit(DepositTypeEnum.NEED);
		casePaySubInfo.setCommission(false);
		return casePaySubInfo;
	}

	@NotNull
	private static CombinePaySubInfoVO buildContributePaySubInfoVO(CfCombinePayUidInfo combinePayInfo) {
		CfCombinePayUidInfo.ContributeOrderPayUidInfo contributeOrderPayUidInfo = combinePayInfo.getContributeOrderPayUidInfo();
		CombinePaySubInfoVO contributeSubInfo = new CombinePaySubInfoVO();
		contributeSubInfo.setAmountInFen(contributeOrderPayUidInfo.getContributeAmountInFen());
		contributeSubInfo.setBizType(14);
		contributeSubInfo.setSubOrderId(contributeOrderPayUidInfo.getContributeSubPayUid());
		contributeSubInfo.setSubOrderName(combinePayInfo.getCombineProductName());
		contributeSubInfo.setDeposit(DepositTypeEnum.NEEDLESS);
		contributeSubInfo.setCommission(false);
		return contributeSubInfo;
	}

	/**
	 * 捐献下单使用
	 * @param platform
	 * @param cfPayOriginEnum
	 * @param payUid
	 * @param amountInFen
	 * @param openId
	 * @param payBody
	 * @param appId
	 * @param userId
	 * @param depositTypeEnum
	 * @param clientId
	 * @return
	 */
	@Override
	public PayResultV2 payInfoByNewV3(Platform platform,
	                                  CfPayOrigin cfPayOriginEnum, String payUid,
	                                  Integer amountInFen, String openId, String payBody, String appId, long userId, DepositTypeEnum depositTypeEnum, int clientId) {
		log.info(
				"payInfoByNewV2 param : platform:{};cfPayOriginEnum:{};payUid:{};amountInFen:{};"
						+ "openId:{};body:{};appId:{};",
				platform, cfPayOriginEnum, payUid, amountInFen, openId, payBody,
				appId);
		String productName = payUtil.productName(payBody);
		//捐献的固定为14
		BizType wxBizTypeEnum = BizType.LIVE;
		AliBizType aliBizTypeEnum = getAliBizType();
		PayType payTypeEnum = getPayTypeEnum(cfPayOriginEnum);
		PayInfoParamV3 payInfoParamV3 = null;
		if(PayType.QPAY_MINIAPP.equals(payTypeEnum)){
			payInfoParamV3 = PayInfoParamV3.buildQPay(clientId, userId, payUid,
					payTypeEnum, wxBizTypeEnum, PlatformUtil.transToPay(platform), amountInFen, productName,
					"", appId, cFNowpayConfigUtil.getContributePayCallBackUrl())
					.buildDepositTypeEnum(depositTypeEnum)
					//true 是水滴垫付手续费 false 是用户垫付手续费
					.buildCommission(false);
		}else {
			payInfoParamV3 = PayInfoParamV3.buildWxAndAli(clientId, userId, payUid,
					payTypeEnum, wxBizTypeEnum, PlatformUtil.transToPay(platform), amountInFen, productName, openId,
					"", appId, aliBizTypeEnum, cFNowpayConfigUtil.getContributePayCallBackUrl())
					.buildDepositTypeEnum(depositTypeEnum)
					//true 是水滴垫付手续费 false 是用户垫付手续费
					.buildCommission(false);
		}


		log.info("下单参数:payInfoParamV3:{}", JSONObject.toJSONString(payInfoParamV3));
		PayResultV2 payResult = null;
		try {
			PayRpcResponse<PayResultV2> payRpcResponse = this.payClientRpcAdapter.unifiedOrder(payInfoParamV3);
			if(payRpcResponse != null) {
				payResult = payRpcResponse.getResult();
			}
		} catch (Exception e) {
			log.error("下单payInfoByNewV3 error:", e);
		}
		log.info("下单结果:payResult:{}", JSONObject.toJSONString(payResult));
		return payResult;
	}


	@Override
	public PayResultV2 payInfoByNewV2(CrowdfundingType crowdfundingType, Platform platform, CfPayOrigin cfPayOriginEnum,
									  String payUid, int amountInFen, String openId, String body, String showUrl,
									  String returnUrl, String appId, long userId, DepositTypeEnum depositTypeEnum) {
		log.info(
				"payInfoByNewV2 param : crowdfundingType:{};platform:{};cfPayOriginEnum:{};payUid:{};amountInFen:{};"
						+ "openId:{};body:{};showUrl:{};returnUrl:{};appId:{};",
				crowdfundingType, platform, cfPayOriginEnum, payUid, amountInFen, openId, body, showUrl, returnUrl,
				appId);
		String productName = payUtil.productName(body);
		BizType wxBizTypeEnum = getWxBizType(crowdfundingType, cfPayOriginEnum, depositTypeEnum);
		AliBizType aliBizTypeEnum = getAliBizType();
		PayType payTypeEnum = getPayTypeEnum(cfPayOriginEnum);
		PayInfoParamV3 payInfoParamV3 = null;
		if(PayType.QPAY_MINIAPP.equals(payTypeEnum)){
			payInfoParamV3 = PayInfoParamV3.buildQPay(16, userId, payUid,
					payTypeEnum, wxBizTypeEnum, PlatformUtil.transToPay(platform), amountInFen, productName,
					"", appId, cFNowpayConfigUtil.getPayServiceCallBackUrl())
					.buildDepositTypeEnum(depositTypeEnum)
					.buildCaseId(CrowdfundingUtil.buildPingAnCode(body))
					//true 是水滴垫付手续费 false 是用户垫付手续费
					.buildCommission(false);
		} else if (PayType.isAlipay(payTypeEnum)) {
			// 支付宝支付 aliBizType=118 与微信商户号一致
			payInfoParamV3 = PayInfoParamV3.buildWxAndAli(16, userId, payUid,
					payTypeEnum, wxBizTypeEnum.getCode(), PlatformUtil.transToPay(platform), amountInFen, productName, openId,
					"", appId, 118, cFNowpayConfigUtil.getPayServiceCallBackUrl())
					.buildDepositTypeEnum(depositTypeEnum)
					.buildConfCode("10000000003")
					.buildQuickBizType(CardBizType.QUICK_SDB)
					.buildCaseId(CrowdfundingUtil.buildPingAnCode(body))
					.buildCommission(false);
		} else {
			 payInfoParamV3 = PayInfoParamV3.buildWxAndAli(16, userId, payUid,
					payTypeEnum, wxBizTypeEnum, PlatformUtil.transToPay(platform), amountInFen, productName, openId,
					"", appId, aliBizTypeEnum, cFNowpayConfigUtil.getPayServiceCallBackUrl())
					 .buildDepositTypeEnum(depositTypeEnum)
					 .buildCaseId(CrowdfundingUtil.buildPingAnCode(body))
					 .buildCommission(false);
		}

		log.info("下单参数:payInfoParamV3:{}", JSONObject.toJSONString(payInfoParamV3));
		PayResultV2 payResult = null;
		try {
			PayRpcResponse<PayResultV2> payRpcResponse = this.payClientRpcAdapter.unifiedOrder(payInfoParamV3);
			if(payRpcResponse != null) {
				payResult = payRpcResponse.getResult();
			}
		} catch (Exception e) {
			log.error("下单payInfoByNewV3 error:", e);
		}
		log.info("下单结果:payResult:{}", JSONObject.toJSONString(payResult));
		return payResult;
	}



	public BizType getWxBizType(CrowdfundingType crowdfundingType, CfPayOrigin cfPayOriginEnum, DepositTypeEnum depositTypeEnum) {
		BizType bizTypeEnum = BizType.CF;
		switch (crowdfundingType) {
			case SERIOUS_ILLNESS:
				if (DepositTypeEnum.NEED.equals(depositTypeEnum)) {
					//托管案例统一使用托管商户号
					return BizType.CF_DEPOSIT;
				}
				switch (cfPayOriginEnum) {
					case HEALTH_SECURITY_MP_WX:
					case HEALTH_SECURITY_WAP_WX:
					case HEALTH_SECURITY_WB_WX:
					case HEALTH_SECURITY_QQ_WX:
					case HEALTH_SECURITY_TB_WX:
					case HEALTH_SECURITY_QQ_ALI:
					case HEALTH_SECURITY_TB_ALI:
					case HEALTH_SECURITY_WAP_ALI:
					case HEALTH_SECURITY_WB_ALI:
					case HEALTH_SECURITY_SP_WX:
						bizTypeEnum = BizType.HEALTH_SECURITY;
						break;
					case FUND_MPWX_CF:
					case FUND_WAPWX_CF:
						bizTypeEnum = BizType.CF_FUND;
						break;
					case QPAY_MINIAPP:
						bizTypeEnum = BizType.CF;
						break;
					default:
						bizTypeEnum = BizType.CF;
						break;
				}
				break;
			case DREAM:
			case GOODS:
				bizTypeEnum = BizType.WISH;
				break;
		}
		return bizTypeEnum;
	}

	private AliBizType getAliBizType() {
		AliBizType bizTypeEnum = AliBizType.NOWPAY_CF;
		return bizTypeEnum;
	}

	private PayType getPayTypeEnum(CfPayOrigin cfPayOriginEnum) {
		PayType payTypeEnum = PayType.WX_JSSDK;
		switch (cfPayOriginEnum) {
			case NOWPAY_MPWX_CF:
			case MPWX_CF:
			case FUND_MPWX_CF:
			case HEALTH_SECURITY_MP_WX:
				payTypeEnum = PayType.WX_JSSDK;
				break;

			case NOWPAY_WAPWX_CF:
			case WAPWX_CF:
			case WB_WX:
			case TB_WX:
			case QQ_WX:
			case FUND_WAPWX_CF:
			case HEALTH_SECURITY_WAP_WX:
			case HEALTH_SECURITY_WB_WX:
			case HEALTH_SECURITY_QQ_WX:
			case HEALTH_SECURITY_TB_WX:
				payTypeEnum = PayType.WX_H5;
				break;
			case HEALTH_SECURITY_WAP_ALI:
				payTypeEnum = PayType.ALIPAY_WAP;
				break;
			case WX_SP:
			case FUND_WX_SP:
			case HEALTH_SECURITY_SP_WX:
				payTypeEnum = PayType.WX_SP;
				break;
			case QPAY_MINIAPP:
				payTypeEnum = PayType.QPAY_MINIAPP;
				break;
			case ILLEGAL:
			default:
				break;
		}
		return payTypeEnum;
	}

	@Override
	public PayInfoVo getPayInfo(long userId, int userThirdType) {
		UserThirdModel userThird = this.userThirdDelegate.getThirdModelWithUserId(userId, userThirdType);
		if (userThird != null && StringUtils.isNotBlank(userThird.getOpenId())) {
			WxMpConfig wxMpConfig = wxMpConfigCacheBiz.getWxMpConfigFromCache(userThirdType);
			if (wxMpConfig != null && StringUtils.isNotBlank(wxMpConfig.getAppId())) {
				// 确保appId与openId可用后赋值
				PayInfoVo payInfoVo = new PayInfoVo(wxMpConfig.getAppId(), userThird.getOpenId(), -1,
						userThirdType, userThird.getUnionId());
				return payInfoVo;
			}
		}
		log.warn("userId:{}\tuserThirdType:{}无法获取到用户的账号信息\tuserThird.openId:{}",
                userId,userThirdType,userThird == null ? "null" : userThird.getOpenId());
		return null;
	}

	@Override
	public PayInfoVo getPayInfoVoWithNew(CrowdfundingType crowdfundingType, PayType payTypeChannel,
										 CfInfoExtEnum.PayType payTypeEnum, Integer userThirdType, long userId) {
		if (userThirdType == null) {
			switch (payTypeChannel) {
				case WX_SP:
					userThirdType = AccountThirdTypeEnum.WX_CF_SP.getCode();
					break;
				default:
					userThirdType = getDefault(crowdfundingType);
					break;
			}
		}

		if (payTypeChannel == PayType.WX_H5) {
			// 微信外不需要openId
			WxMpConfig wxMpConfig = null;
            if(userThirdType != 730) {
				wxMpConfig = wxMpConfigCacheBiz.getWxMpConfigFromCache(userThirdType);
			} else {
				wxMpConfig = wxMpConfigCacheBiz.getWxMpConfigFromCache(17);

			}
			PayInfoVo payInfoVo = new PayInfoVo(wxMpConfig.getAppId(), "", -1, userThirdType, "");
			return payInfoVo;
		}
		PayInfoVo payInfoVo = null;

		if (userThirdType != null) {
			payInfoVo = getPayInfo(userId, userThirdType);
		}
		if (payInfoVo == null) {
			payInfoVo = getPayInfo(userId, getDefault(crowdfundingType));
		}
		if (payInfoVo != null) {
			return payInfoVo;
		} else {
			log.info("userId:{} thirdType:{}找不对对应的payInfoVo", userId, userThirdType);
			return new PayInfoVo("", "", -1, getDefault(crowdfundingType), "");
		}
	}

	private int getDefault(CrowdfundingType crowdfundingType) {
		switch (crowdfundingType) {
			case SERIOUS_ILLNESS:
				return AccountThirdTypeEnum.WX_CF_SERIOUS_ILLNESS.getCode();
			case DREAM:
			case GOODS:
				return AccountThirdTypeEnum.WX_CF_DREAM.getCode();
		}
		return AccountThirdTypeEnum.WX_CF_SERIOUS_ILLNESS.getCode();
	}
}
