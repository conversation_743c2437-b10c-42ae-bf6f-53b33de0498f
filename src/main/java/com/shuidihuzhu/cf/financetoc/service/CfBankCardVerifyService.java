package com.shuidihuzhu.cf.financetoc.service;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingBankVerifyResultVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoPayeeVo;
import com.shuidihuzhu.cf.response.crowdfunding.CrowdfundingInfoResponse;
import com.shuidihuzhu.common.web.model.Response;

/**
 * @Author: lianghong<PERSON>o
 * @Date: 2019/12/16 14:14
 */
public interface CfBankCardVerifyService {

    /**
     * 银行卡三要素校验
     *
     * @param crowdfundingInfo
     * @param payeeName
     * @param bankCard
     * @param idCard
     * @return
     */
    Response<CrowdfundingBankVerifyResultVo> checkCardByThreeElements(CrowdfundingInfo crowdfundingInfo,
                                                                      String payeeName, String bankCard, String idCard);
}
