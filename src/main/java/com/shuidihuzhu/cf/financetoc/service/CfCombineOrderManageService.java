package com.shuidihuzhu.cf.financetoc.service;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.financetoc.dao.order.CfCombineOrderManageDao;
import com.shuidihuzhu.cf.model.contribute.CfCombineOrderManage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class CfCombineOrderManageService {

    @Autowired
    private CfCombineOrderManageDao combineOrderManageDao;

    public int addCombineOrderList(List<CfCombineOrderManage> combineList) {
        if (CollectionUtils.isEmpty(combineList)) {
            return 0;
        }

        return combineOrderManageDao.addCombineOrderList(combineList);
    }

    public int updateThirdPayUid(List<CfCombineOrderManage> combineList) {

        if (CollectionUtils.isEmpty(combineList)) {
            return 0;
        }
        int cnt = 0;
        for (CfCombineOrderManage orderManage : combineList) {
            cnt += combineOrderManageDao.updatePayStatus(orderManage.getParentThirdPayUid(),
                    orderManage.getSubThirdPayUid(),
                    orderManage.getSubPayUid());
        }
        return cnt;
    }

    public List<CfCombineOrderManage> selectByParentPayUid(String parentPayUid) {
        if (StringUtils.isBlank(parentPayUid)) {
            return Lists.newArrayList();
        }

        return combineOrderManageDao.selectByParentUids(Lists.newArrayList(parentPayUid));
    }

    public CfCombineOrderManage getByParentThirdPayUidAndOrderType(String parentThirdPayUid, int orderType) {
        if (StringUtils.isBlank(parentThirdPayUid) || orderType <= 0) {
            return null;
        }
        return combineOrderManageDao.getByParentThirdPayUidAndOrderType(parentThirdPayUid, orderType);
    }

    public CfCombineOrderManage getByOrderId(long orderId) {
        return combineOrderManageDao.getByOrderId(orderId);
    }
}
