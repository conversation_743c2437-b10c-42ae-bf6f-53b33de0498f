package com.shuidihuzhu.cf.financetoc.service;

import com.shuidihuzhu.cf.enums.crowdfunding.CfOrderListTypeEnum;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class CrowdfundingInfoDetailRedisService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CrowdfundingInfoDetailRedisService.class);

    public static final int REDIS_PAGE_NUM = 30;

    private static final String REDIS_DETAIL_LIST_NAME = "redis-detail-list-v3-";
    private static final String REDIS_DETAIL_PAGE_NAME = "redis-detail-page-v3-";

    @Resource(name = "cfInfoDetailRedissonHandler")
    private RedissonHandler cfRedissonHandler;

    private OrderRedisKey getKey(int pageNum, int crowdfundingId, long contextUserId, CfOrderListTypeEnum typeEnum) {
        String pageKeyName = getPagekey(pageNum, crowdfundingId, contextUserId, typeEnum);
        String listKeyName = getListKey(pageNum, crowdfundingId, contextUserId, typeEnum);
        return new OrderRedisKey(pageKeyName, listKeyName);
    }

    private class OrderRedisKey {
        public String pageKeyName;
        public String listKeyName;

        public OrderRedisKey(String pageKeyName, String listKeyName) {
            this.pageKeyName = pageKeyName;
            this.listKeyName = listKeyName;
        }
    }

    /**
     * 把好友的捐款记录置为无效.
     *
     * @param crowdfundingId
     * @param userId
     */
    @Async
    public void clearOrderRedis(int crowdfundingId, long userId) {
        try {
            for (int pageNum = 1; pageNum <= REDIS_PAGE_NUM; pageNum++) {
                OrderRedisKey friendOrderKey = getKey(pageNum, crowdfundingId, userId, CfOrderListTypeEnum.FRIENDS);
                OrderRedisKey otherOrderKey = getKey(pageNum, crowdfundingId, 0, CfOrderListTypeEnum.OTHER);
                cfRedissonHandler.del(friendOrderKey.pageKeyName);
                cfRedissonHandler.del(friendOrderKey.listKeyName);
                cfRedissonHandler.del(otherOrderKey.pageKeyName);
                cfRedissonHandler.del(otherOrderKey.listKeyName);
            }
        } catch (Exception e) {
            LOGGER.error("", e);
        }
    }

    /**
     * 把自己的捐款记录置为无效。
     *
     * @param crowdfundingId
     * @param userId
     */
    @Async
    public void clearSelfDetailRedis(int crowdfundingId, long userId) {
        try {
            for (int pageNum = 1; pageNum <= REDIS_PAGE_NUM; pageNum++) {
                OrderRedisKey key = getKey(pageNum, crowdfundingId, userId, CfOrderListTypeEnum.SELF);
                cfRedissonHandler.del(key.pageKeyName);
                cfRedissonHandler.del(key.listKeyName);
            }
        } catch (Exception e) {
            LOGGER.error("", e);
        }
    }

    private String getPagekey(int pageNum, int crowdfundingId, long userId, CfOrderListTypeEnum type) {
        return getKeyName(REDIS_DETAIL_PAGE_NAME, pageNum, crowdfundingId, userId, type);
    }

    private String getListKey(int pageNum, int crowdfundingId, long userId, CfOrderListTypeEnum type) {
        return getKeyName(REDIS_DETAIL_LIST_NAME, pageNum, crowdfundingId, userId, type);
    }

    private String getKeyName(String prefix, int pageNum, int crowdfundingId, long userId, CfOrderListTypeEnum type) {
        StringBuilder key = new StringBuilder()
                .append(prefix)
                .append(crowdfundingId).append("-")
                .append(pageNum).append("-")
                .append(type).append("-");

        if (type == CfOrderListTypeEnum.SELF ||
                type == CfOrderListTypeEnum.FRIENDS) {
            key.append(userId).append("-");
        }
        return key.toString();
    }
}
