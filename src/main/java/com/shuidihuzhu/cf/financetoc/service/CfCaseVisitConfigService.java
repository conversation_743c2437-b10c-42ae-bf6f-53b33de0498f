package com.shuidihuzhu.cf.financetoc.service;

import com.shuidihuzhu.client.cf.risk.model.enums.UserOperationEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * Created by wangsf on 18/4/17.
 */
@Service
@Slf4j
@RefreshScope
public class CfCaseVisitConfigService {


    @Autowired
    private ICfRiskService cfRiskService;

    public boolean canDonate(int caseId) {
        return cfRiskService.operatorValid(0, caseId, UserOperationEnum.ORDER);
    }
}
