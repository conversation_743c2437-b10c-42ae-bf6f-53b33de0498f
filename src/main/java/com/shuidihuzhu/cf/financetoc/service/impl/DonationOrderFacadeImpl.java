package com.shuidihuzhu.cf.financetoc.service.impl;

import brave.propagation.ExtraFieldPropagation;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.shuidihuzhu.cf.activity.feign.UserAccountFeignClient;
import com.shuidihuzhu.cf.activity.model.DonateCooperateFeeVO;
import com.shuidihuzhu.cf.app.constant.ApolloConstant;
import com.shuidihuzhu.cf.app.constant.ConfigConstant;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.combine.CombineCommonEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfInfoExtEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfPayOrigin;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingType;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CfInfoExtBiz;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.financetoc.biz.order.CrowdfundingOrderBiz;
import com.shuidihuzhu.cf.financetoc.biz.order.CrowdfundingPayRecordBiz;
import com.shuidihuzhu.cf.financetoc.biz.order.GoodsOrderExtBiz;
import com.shuidihuzhu.cf.financetoc.mq.producer.CommonProducer;
import com.shuidihuzhu.cf.financetoc.service.*;
import com.shuidihuzhu.cf.financetoc.util.CrowdfundingUtil;
import com.shuidihuzhu.cf.financetoc.util.PlatformUtil;
import com.shuidihuzhu.cf.model.contribute.*;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord;
import com.shuidihuzhu.cf.model.crowdfunding.vo.OrderResultVo;
import com.shuidihuzhu.cf.mq.payload.OrderAddPayload;
import com.shuidihuzhu.cf.param.order.LoveHelpCombineOrderParam;
import com.shuidihuzhu.cf.vo.PayInfoVo;
import com.shuidihuzhu.charity.client.api.ActivityVenueClient;
import com.shuidihuzhu.charity.client.api.CfManageClient;
import com.shuidihuzhu.charity.client.dto.CreateDonateBatchDTO;
import com.shuidihuzhu.client.baseservice.pay.enums.DepositTypeEnum;
import com.shuidihuzhu.client.baseservice.pay.enums.PayType;
import com.shuidihuzhu.client.baseservice.pay.enums.PayTypeNewEnum;
import com.shuidihuzhu.client.baseservice.pay.model.PayResultV2;
import com.shuidihuzhu.client.cf.risk.model.enums.UserOperationEnum;
import com.shuidihuzhu.common.web.constants.CrowdfundingCons;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.enums.Platform;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.IdGenUtil;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.CaseOrder;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RefreshScope
@Slf4j
@Service
@SuppressWarnings({"all"})
public class DonationOrderFacadeImpl implements DonationOrderFacade {
    private final Random random = new Random();

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CrowdfundingOrderBiz crowdfundingOrderBiz;
    @Autowired
    private CrowdfundingPayRecordBiz crowdfundingPayRecordBiz;
    @Autowired
    private PayBizForCf payBizForCf;
    @Autowired
    private GoodsOrderExtBiz goodsOrderExtBiz;
    @Autowired
    private CfInfoExtBiz cfInfoExtBiz;
    @Autowired
    private CfCaseVisitConfigService cfCaseVisitConfigService;
    @Autowired
    private CfFriendLoopServiceV2 friendLoopServiceV2;
    @Autowired
    private ICfRiskService cfRiskService;
    @Resource
    private Analytics analytics;
    @Resource
    private MeterRegistry meterRegistry;
    @Autowired
    private OrderOperateService orderOperateService;
    @Autowired
    private UserAccountFeignClient userAccountFeignClient;
    @Autowired
    private ActivitySubsidyOrderService activitySubsidyOrderService;
    @Autowired
    private ActivityVenueClient activityVenueClient;
    @Autowired
    private CfOrderGuideService cfOrderGuideService;
    @Autowired
    private CfManageClient cfManageClient;
    @Autowired
    private CommonProducer commonProducer;
    @Autowired
    private CfContributeOrderService contributeOrderService;
    @Resource
    private ApolloConstant apolloConstant;
    @Resource
    private ConfigConstant configConstant;

    /**
     * 抽取自 com.shuidihuzhu.cf.controller.api.v4.CrowdfundingV41OrderController#addOrder
     */
    @Override
    @NotNull
    public Response<OrderResultVo> createOrder(long userId, String infoUuid,
                                               double amt, String comment, String platform,
                                               String source, String selfTag, String showUrl,
                                               String returnUrl, Long userAddressId, Long gearId,
                                               Integer goodsCount, Boolean anonymous, Integer userThirdType,
                                               String splitFlowUuid, String shareSourceId, Integer payType,
                                               int activityId, boolean useSubsidy, int shareDv, String subActivityId,
                                               String clientIp, Integer osType, String channel, int blessingCardId
            , Integer contributeAmount, String bonfireUuid, List<LoveHelpCombineOrderParam> loveHelpCombineOrderParams) {
        Response<CrowdfundingInfo> mainCrowdfundingInfoRes = validateParam(userId, infoUuid);
        //如果校验错误  直接返回
        if(mainCrowdfundingInfoRes.notOk()) {
            return NewResponseUtil.makeResponse(mainCrowdfundingInfoRes.getCode(), mainCrowdfundingInfoRes.getMsg(), null);
        }
        // 从案例
        List<CrowdfundingInfo> subCrowdfundingInfoList = new ArrayList<>(10);
        if (CollectionUtils.isNotEmpty(loveHelpCombineOrderParams)) {
            if (loveHelpCombineOrderParams.size() > 9) {
                log.warn("合并支付不能超过10笔, 当前:{}", loveHelpCombineOrderParams.size());
                return NewResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
            }
            for (LoveHelpCombineOrderParam loveHelpCombineOrderParam : loveHelpCombineOrderParams) {
                Response<CrowdfundingInfo> subCrowdfundingInfoRes = validateParam(userId, loveHelpCombineOrderParam.getInfoUuid());
                if(subCrowdfundingInfoRes.notOk()) {
                    return NewResponseUtil.makeResponse(subCrowdfundingInfoRes.getCode(), subCrowdfundingInfoRes.getMsg(), null);
                }
                CrowdfundingInfo data = subCrowdfundingInfoRes.getData();
                subCrowdfundingInfoList.add(data);
                loveHelpCombineOrderParam.setCaseId(data.getId());
            }
        }
        Map<Integer, CrowdfundingInfo> subCrowdfundingMap = subCrowdfundingInfoList.stream().collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity()));

        CrowdfundingInfo mainCrowdfundingInfo = mainCrowdfundingInfoRes.getData();
        // comment
        comment = this.rebuildComment(comment);
        // from
        if(StringUtils.isEmpty(source) || source.length() > CrowdfundingCons.MAX_FROM_LEN) {
            source = null;
        }
        if(StringUtils.isEmpty(selfTag) || selfTag.length() > CrowdfundingCons.MAX_FROM_LEN) {
            selfTag = null;
        }
        anonymous = anonymous != null && anonymous;

        CrowdfundingType crowdfundingType = CrowdfundingType.fromValue(mainCrowdfundingInfo.getType());

        // 金额校验, 同时记录下
        Response<OrderResultVo> checkAmtResult = this.checkOrderAmt(amt, crowdfundingType);
        if (checkAmtResult.notOk()) {
            return checkAmtResult;
        }

        int caseId = mainCrowdfundingInfo.getId();
        PayType payTypeChannel = Optional.ofNullable(PayTypeNewEnum.fromCode(payType))
                .orElse(PayType.QPAY_MINIAPP.equals(PayType.fromCode(payType)) ? PayType.fromCode(payType) : null);

        Platform platformEnum = Platform.findByCode(platform);
        // 新支付
        CfInfoExt cfInfoExt = this.cfInfoExtBiz.getByInfoUuid(infoUuid);
        CfInfoExtEnum.PayType payTypeEnum = CfInfoExtEnum.PayType.WX_HEALTH_SECURITY;
        String productName = Optional.ofNullable(cfInfoExt)
                .map(CfInfoExt::getProductName)
                .filter(StringUtils::isNotBlank)
                .orElse(CrowdfundingUtil.getProductName(mainCrowdfundingInfo));
        // 填充子案例
        this.fillSubCaseProductName(loveHelpCombineOrderParams, subCrowdfundingMap);
        if(cfInfoExt != null) {
            payTypeEnum = CfInfoExtEnum.PayType.fromCode(cfInfoExt.getPayType());
        }
        CfPayOrigin cfPayOriginEnum = getNewPayOrigin(payTypeChannel, platformEnum, payTypeEnum);

        log.info("cfPayOriginEnum:{};userThirdType:{};splitFlowUuid:{}", cfPayOriginEnum, userThirdType, splitFlowUuid);
        if(CfPayOrigin.ILLEGAL == cfPayOriginEnum) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        PayInfoVo payInfoVo = null;
        if(PayType.isWeixin(payTypeChannel) || PayType.isQPay(payTypeChannel)) {
            payInfoVo = this.payBizForCf.getPayInfoVoWithNew(crowdfundingType, payTypeChannel, payTypeEnum, userThirdType, userId);
        }

        if (PayType.isAlipay(payTypeChannel)) {
            // 支付宝不需要传appId， 但是直接接口必填
            payInfoVo = new PayInfoVo("1", "", -1, userThirdType, "");
        }
        log.info("after change cfPayOriginEnum:{}", cfPayOriginEnum);
        int amountInFen = (int) MoneyUtil.changeYuanStrToFen(String.valueOf(amt), RoundingMode.HALF_UP);
        log.info("amountInFen:{}", amountInFen);
        String payUid = IdGenUtil.tradeNo();
        // 保存 order
        int userThirdId = -1;
        int userThirdTypeEnum = AccountThirdTypeEnum.EMPTY.getCode();
        if(payInfoVo != null) {
            userThirdId = payInfoVo.getUserThirdId();
            userThirdTypeEnum = payInfoVo.getUserThirdTypeEnum();
        }

        // 构造合并支付参数
        CfCombinePayUidInfo combinePayUidInfo = this.buildCombinePayInfo(contributeAmount, caseId, productName, amountInFen, loveHelpCombineOrderParams);

        OrderOperateService.OrderAndPayRecord mainOrderAndPayRecord;
        List<OrderOperateService.OrderAndPayRecord> allOrderAndPayRecords = new ArrayList<>(10);
        int newGoodsCount = goodsCount == null ? 0 : goodsCount;
        if (combinePayUidInfo == null || combinePayUidInfo.getCombineBizType() == CombineCommonEnum.CombineOrderBizType.CONTRIBUTE) {
            //订单表，事务
            mainOrderAndPayRecord = orderOperateService
                    .addOrderAndPayRecord(mainCrowdfundingInfo.getId(), userId, userThirdId
                            , userThirdTypeEnum, amountInFen, comment, source, selfTag, anonymous
                            , payUid, cfPayOriginEnum.getCode(), activityId, clientIp, osType, channel
                            , combinePayUidInfo);
            allOrderAndPayRecords.add(mainOrderAndPayRecord);
            //订单分表, 服务埋点
            Response<Void> saveOrderNewInstanceRes = saveOrderNewInstance(mainOrderAndPayRecord, mainCrowdfundingInfo, amountInFen
                    , splitFlowUuid, userAddressId, gearId, newGoodsCount
                    , shareSourceId, blessingCardId, shareDv);
            if(saveOrderNewInstanceRes.notOk()) {
                return NewResponseUtil.makeResponse(saveOrderNewInstanceRes.getCode(), saveOrderNewInstanceRes.getMsg(), null);
            }
        } else if (combinePayUidInfo.getCombineBizType() == CombineCommonEnum.CombineOrderBizType.LOVE_MORE_DONATE) {
            List<OrderOperateService.OrderAndPayRecord> orderAndPayRecords = orderOperateService
                    .addBatchOrderAndPayRecord(userId, userThirdId
                            , userThirdTypeEnum, comment, source, selfTag, anonymous
                            , payUid, cfPayOriginEnum.getCode(), activityId, clientIp, osType, channel
                            , combinePayUidInfo);
            allOrderAndPayRecords = orderAndPayRecords;
            mainOrderAndPayRecord = orderAndPayRecords.stream()
                    .filter(r -> r.getCrowdfundingOrder().getCrowdfundingId() == caseId)
                    .findFirst().orElse(null);
            if (mainOrderAndPayRecord == null) {
                return NewResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
            }
            for (OrderOperateService.OrderAndPayRecord orderAndPayRecord : orderAndPayRecords) {
                Integer crowdfundingId = orderAndPayRecord.getCrowdfundingOrder().getCrowdfundingId();
                CrowdfundingInfo info;
                if (crowdfundingId == caseId) {
                    info = mainCrowdfundingInfo;
                } else {
                    info = subCrowdfundingMap.get(crowdfundingId);
                }
                Response<Void> saveOrderNewInstanceRes = saveOrderNewInstance(orderAndPayRecord, info, amountInFen
                        , splitFlowUuid, userAddressId, gearId, newGoodsCount
                        , shareSourceId, blessingCardId, shareDv);
                if(saveOrderNewInstanceRes.notOk()) {
                    return NewResponseUtil.makeResponse(saveOrderNewInstanceRes.getCode(), saveOrderNewInstanceRes.getMsg(), null);
                }
            }
        } else {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
        }

        Long orderId = mainOrderAndPayRecord.getCrowdfundingOrder().getId();
        // 补贴相关, 只需要关心主案例即可
        this.doSaveSubsidyInfo(useSubsidy, userId, caseId, activityId, mainOrderAndPayRecord, shareDv, platformEnum);
        // 用户微信数据
        String appId = Optional.ofNullable(payInfoVo).map(PayInfoVo::getAppId).orElse(StringUtils.EMPTY);
        String openId = Optional.ofNullable(payInfoVo).map(PayInfoVo::getOpenId).orElse(StringUtils.EMPTY);

        PayResultV2 payInfo = this.buildPayInfo(crowdfundingType, platformEnum, cfPayOriginEnum, payUid, amountInFen,
                openId, productName, showUrl, returnUrl, appId, userId, combinePayUidInfo);
        // 删除好友转发缓存, 只关心主案例即可
        this.delShareFriendCash(userId, mainCrowdfundingInfo.getId());
        //托管订单支付导购--筹转保
        this.orderGuide2Sdb(payUid, payInfo, combinePayUidInfo, userId);
        //小善日主会场创建捐单数据
        this.loveDailyCreateOrder(mainCrowdfundingInfo, subCrowdfundingInfoList, activityId, userId
                , amountInFen, subActivityId
                , orderId, allOrderAndPayRecords);
        // 广告相关操作
        this.markCreativeFlag(mainCrowdfundingInfo.getId(), subCrowdfundingInfoList, orderId, allOrderAndPayRecords);
        // 资助打赏mq消息
        this.contributeMsg(combinePayUidInfo);
        // 发送下单事件 此处为MQ
        this.applicationEventSendOrderEvent(userId, channel, allOrderAndPayRecords, infoUuid);
        // 构造预下单结果
        return NewResponseUtil.makeSuccess(this.buildOrderResultVo(combinePayUidInfo, payUid
                , amountInFen, contributeAmount, productName, openId
                , payInfo, platformEnum, mainOrderAndPayRecord));
    }

    private void fillSubCaseProductName(List<LoveHelpCombineOrderParam> loveHelpCombineOrderParams, Map<Integer, CrowdfundingInfo> subCrowdfundingMap) {
        if (CollectionUtils.isEmpty(loveHelpCombineOrderParams) || MapUtils.isEmpty(subCrowdfundingMap)) {
            return ;
        }
        Map<Integer, CfInfoExt> caseIdMapping = this.cfInfoExtBiz.getCaseIdMapping(loveHelpCombineOrderParams
                .stream().map(LoveHelpCombineOrderParam::getCaseId)
                .distinct().collect(Collectors.toList()));

        for (LoveHelpCombineOrderParam loveHelpCombineOrderParam : loveHelpCombineOrderParams) {
            int caseId = loveHelpCombineOrderParam.getCaseId();
            CfInfoExt cfInfoExt = caseIdMapping.get(caseId);
            String productName = Optional.ofNullable(cfInfoExt)
                    .map(CfInfoExt::getProductName)
                    .filter(StringUtils::isNotBlank)
                    .orElse(CrowdfundingUtil.getProductName(subCrowdfundingMap.get(caseId)));
            loveHelpCombineOrderParam.setProductName(productName);
        }
    }

    private void applicationEventSendOrderEvent(long userId, String channel
            , List<OrderOperateService.OrderAndPayRecord> orderAndPayRecords, String infoUuid) {
        orderAndPayRecords.forEach(item -> {
            CrowdfundingOrder crowdfundingOrder = item.getCrowdfundingOrder();
            OrderAddPayload payload = new OrderAddPayload();
            payload.setChannel(channel);
            payload.setCaseId(crowdfundingOrder.getCrowdfundingId());
            payload.setOrderId(crowdfundingOrder.getId());
            payload.setUserId(userId);
            log.debug("orderAddMsg payload:{}", JSON.toJSONString(payload));
            commonProducer.orderAddMsg(payload);
        });
    }

    private PayResultV2 buildPayInfo(CrowdfundingType crowdfundingType, Platform platformEnum, CfPayOrigin cfPayOriginEnum
            , String payUid, Integer amountInFen, String openId, String productName, String showUrl, String returnUrl
            , String appId, long userId, CfCombinePayUidInfo cfCombinePayUidInfo) {
        PayResultV2 payInfo = null;
        if (cfCombinePayUidInfo == null) {
            // 普通支付预下单
            payInfo = payBizForCf.payInfoByNewV2(crowdfundingType, platformEnum, cfPayOriginEnum, payUid, amountInFen,
                    openId, productName, showUrl, returnUrl, appId, userId, DepositTypeEnum.NEED);
        } else {
            // 合并支付预下单
            payInfo = payBizForCf.payCombineOrder(
                    CfCombinePayInfo.builder()
                            .wxAppId(appId)
                            .openId(openId)
                            .userId(userId)
                            .platform(platformEnum)
                            .crowdfundingType(crowdfundingType)
                            .cfPayOriginEnum(cfPayOriginEnum)
                            .productName(productName)
                            .combinePayInfo(cfCombinePayUidInfo)
                            .clientId(CombineCommonEnum.CombineOrderType.CASE_DONATE.getClientId())
                            .build());
        }
        return payInfo;
    }

    private CfCombinePayUidInfo buildCombinePayInfo(Integer contributeAmount, int caseId, String productName, Integer amountInFen
            , List<LoveHelpCombineOrderParam> loveHelpCombineOrderParams) {

        if (Objects.nonNull(contributeAmount) && contributeAmount > 0) {
            return CfCombinePayUidInfo.buildCombinePayInfo(contributeAmount, caseId, amountInFen, productName);
        } else if (CollectionUtils.isNotEmpty(loveHelpCombineOrderParams)) {
            return CfCombinePayUidInfo.buildLoveHelpPayInfo(caseId, amountInFen, productName, loveHelpCombineOrderParams);
        } else {
            return null;
        }
    }

    private void delShareFriendCash(long userId, int caseId) {
        try {
            friendLoopServiceV2.delShareFriendCash(userId, caseId);
        } catch (Exception e) {
            log.error("", e);
        }
    }

    private OrderResultVo buildOrderResultVo(CfCombinePayUidInfo combinePayInfo, String payUid, Integer amountInFen
            , Integer contributeAmount, String productName, String openId
            , PayResultV2 payInfo, Platform platformEnum, OrderOperateService.OrderAndPayRecord orderAndPayRecord) {
        OrderResultVo vo = new OrderResultVo();
        vo.setTradeNo(combinePayInfo == null ? payUid : combinePayInfo.getParentPayUid());
        vo.setAmt((combinePayInfo == null ? crowdfundingPayRecordBiz.getDecimalAmount(amountInFen) :
                crowdfundingPayRecordBiz.getDecimalAmount(amountInFen + contributeAmount)));
        vo.setBody(productName);
        vo.setOpenId(openId);
        vo.setPayInfo(payInfo);
        vo.setPayType(platformEnum);
        vo.setOrderCode(orderAndPayRecord.getCrowdfundingOrder().getCode());
        return vo;
    }

    private void doSaveSubsidyInfo(boolean useSubsidy, long userId, int caseId, int activityId
            , OrderOperateService.OrderAndPayRecord orderAndPayRecord, int shareDv, Platform platformEnum) {
        DonateCooperateFeeVO donateCooperateFeeVO = null;
        // 捐助金查询
        if(useSubsidy) {
            donateCooperateFeeVO = getSubsidyFee(userId, caseId);
        }
        saveSubsidyInfo(activityId, donateCooperateFeeVO, orderAndPayRecord.getCrowdfundingOrder(), shareDv,
                platformEnum == Platform.QQ);
    }

    private void contributeMsg(CfCombinePayUidInfo combinePayInfo) {
        if (combinePayInfo != null && combinePayInfo.getCombineBizType() == CombineCommonEnum.CombineOrderBizType.CONTRIBUTE) {
            CfCombinePayUidInfo.ContributeOrderPayUidInfo contributeOrderPayUidInfo = combinePayInfo.getContributeOrderPayUidInfo();
            contributeOrderService.sendContributeActionMsg(contributeOrderPayUidInfo.getContributeSubPayUid(),
                    CfContributeMsgBody.OrderAction.PRE_PAY);
        }
    }

    /**
     * 小善日主会场创建订单数据
     */
    private void loveDailyCreateOrder(CrowdfundingInfo crowdfundingInfo, List<CrowdfundingInfo> subCrowdfundingInfoList
            , int activityId, long userId, Integer amountInFen, String subActivityId, Long orderId
            , List<OrderOperateService.OrderAndPayRecord> allOrderAndPayRecords) {
        boolean isActivity = activityId == 10 || activityId == 999 || activityId == 1000;
        if (!isActivity) {
            return;
        }
        try {
            if(CollectionUtils.isEmpty(subCrowdfundingInfoList)) {
                activityVenueClient.createDonate(crowdfundingInfo.getInfoId(), crowdfundingInfo.getId(), userId, amountInFen,
                        activityId, orderId, subActivityId);
            } else {
                Map<Integer, OrderOperateService.OrderAndPayRecord> caseIdMap = allOrderAndPayRecords.stream()
                        .collect(Collectors.toMap(r -> r.getCrowdfundingOrder().getCrowdfundingId()
                                , Function.identity(), (a, b) -> a));
                List<CreateDonateBatchDTO> params = new ArrayList<>(10);
                CreateDonateBatchDTO mainCase = CreateDonateBatchDTO.builder()
                        .infoId(crowdfundingInfo.getInfoId())
                        .caseId(crowdfundingInfo.getId())
                        .userId(userId)
                        .amountInFen(amountInFen)
                        .activityId(activityId)
                        .orderId(orderId)
                        .subActivityId(subActivityId)
                        .build();
                params.add(mainCase);
                subCrowdfundingInfoList.forEach(item -> {
                    int caseId = item.getId();
                    OrderOperateService.OrderAndPayRecord orderAndPayRecord = caseIdMap.get(caseId);
                    if (orderAndPayRecord == null) {
                        return;
                    }
                    params.add(CreateDonateBatchDTO.builder()
                            .infoId(item.getInfoId())
                            .caseId(caseId)
                            .userId(userId)
                            .amountInFen(orderAndPayRecord.getCrowdfundingOrder().getAmount())
                            .activityId(activityId)
                            .orderId(orderAndPayRecord.getCrowdfundingOrder().getId())
                            .subActivityId(subActivityId)
                            .build());
                });
                activityVenueClient.createDonateBatch(params);
            }
        } catch (Exception e) {
            log.error("frame charity create donate error..", e);
        }
    }

    /**
     * 导购转保
     */
    private void orderGuide2Sdb(String payUid, PayResultV2 payInfo, CfCombinePayUidInfo combinePayInfo, long userId) {
        String sdbPayUid = combinePayInfo == null ? payUid : combinePayInfo.getParentPayUid();
        String thirdPayUid = "";
        if(payInfo != null) {
            thirdPayUid = payInfo.getPayServiceOrderId();
        }
        CfCombinePayUidInfo.CaseOrderPayUidInfo caseOrderPayUidInfo = Optional.ofNullable(combinePayInfo)
                .filter(r -> CollectionUtils.isNotEmpty(r.getCaseOrderPayUidInfoList()))
                .map(CfCombinePayUidInfo::getCaseOrderPayUidInfoList)
                .orElse(Collections.emptyList())
                .stream()
                .filter(CfCombinePayUidInfo.CaseOrderPayUidInfo::isMain)
                .findFirst()
                .orElse(new CfCombinePayUidInfo.CaseOrderPayUidInfo());

        String subPayUid = caseOrderPayUidInfo.getCaseSubPayUid();
        String subThirdPayUid = caseOrderPayUidInfo.getCaseThirdSubPayUid();
        cfOrderGuideService.payOrderGuideToSdb(sdbPayUid, thirdPayUid, userId, subPayUid, subThirdPayUid);
    }

    private String rebuildComment(String comment) {
        comment = StringUtils.trimToEmpty(comment);
        if(StringUtils.isBlank(comment)) {
            comment = buildComment();
        } else if(comment.length() > CrowdfundingCons.MAX_COMMENT_LEN) {
            comment = comment.substring(0, CrowdfundingCons.MAX_COMMENT_LEN);
        }
        return comment;
    }

    private Response<OrderResultVo> checkOrderAmt(double amt, CrowdfundingType crowdfundingType) {
        try {
            if(StringUtils.isNotEmpty(apolloConstant.getSensitiveDonateMoney())) {
                List<Double> sensitiveDonateMoneyList = Splitter.on(',')
                        .splitToList(apolloConstant.getSensitiveDonateMoney())
                        .stream().map(Double::valueOf)
                        .collect(Collectors.toList());
                if(sensitiveDonateMoneyList.contains(amt)) {
                    meterRegistry.counter("cf-api-sensitive-money", "sensitive-money", String.valueOf(amt)).increment();
                    log.info("illegal amt:{}", amt);
                    return NewResponseUtil.makeError(CfErrorCode.TRY_AGAIN_FOR_ANOTHER_AMOUNT);
                }
            }
        } catch (Exception e) {
            log.error("check amt:{}, apollo.sensitive.donate.money:{}", amt, apolloConstant.getSensitiveDonateMoney(), e);
        }

        if(crowdfundingType == CrowdfundingType.GOODS) { // 实物众筹金额可以小于1
            if(amt < 0.00001 || amt > CrowdfundingCons.MAX_AMOUNT) {
                return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
            }
        } else { // 非实物众筹最少要一块钱
            if(amt < CrowdfundingCons.MIN_AMOUNT || amt > CrowdfundingCons.MAX_AMOUNT) {
                return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
            }
        }

        return NewResponseUtil.makeSuccess();
    }


    private Response<Void> saveSubsidyInfo(int activityId, @Nullable DonateCooperateFeeVO donateCooperateFeeVO, CrowdfundingOrder crowdfundingOrder, int shareDv, boolean fromQQ) {
        try {
            return activitySubsidyOrderService.saveOrderSubsidy(activityId, crowdfundingOrder.getId(),
                    donateCooperateFeeVO, shareDv, fromQQ);
        } catch (Exception e) {
            log.error("saveSubsidyInfo", e);
        }
        return NewResponseUtil.makeError(CfErrorCode.SYSTEM_ERROR);
    }

    private DonateCooperateFeeVO getSubsidyFee(long userId, int caseId) {
        try {
            RpcResult<DonateCooperateFeeVO> cooperateResp =
                    userAccountFeignClient.getUserAvailableExceedsCase(userId, caseId);
            if(cooperateResp.isFail()) {
                return null;
            }
            return cooperateResp.getData();
        } catch (Exception e) {
            log.error("getSubsidyFee error", e);
        }
        return null;
    }

    /**
     * 参数校验
     *
     * @return
     */
    private Response<CrowdfundingInfo> validateParam(long userId, String infoUuid) {

        if(userId <= 0) {
            return NewResponseUtil.makeError(CfErrorCode.CF_USER_ACCOUNT_NO_LOGIN);
        }

        // 0.参数验证
        if(infoUuid == null) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);

        if(crowdfundingInfo == null) {
            return NewResponseUtil.makeError(CfErrorCode.CF_NOT_FOUND);
        }

        if(userId == crowdfundingInfo.getUserId()) {
            return NewResponseUtil.makeError(CfErrorCode.CF_CAN_NOT_DONATE_FOR_SELF);
        }

        if(!crowdfundingInfoBiz.isRechargeable(crowdfundingInfo)) {
            return NewResponseUtil.makeError(CfErrorCode.CF_END);
        }

        if(!this.cfCaseVisitConfigService.canDonate(crowdfundingInfo.getId())) {
            return NewResponseUtil.makeError(CfErrorCode.CF_FIRST_REVIEW_NOT_PASSED);
        }

        if(!cfRiskService.operatorValid(userId, crowdfundingInfo.getId(), UserOperationEnum.ORDER)) {
            return NewResponseUtil.makeError(CfErrorCode.RISK_ORDER_PERMITTED);
        }

        return NewResponseUtil.makeSuccess(crowdfundingInfo);
    }

    private String buildComment() {
        int donationsCommentIndex = this.random.nextInt(10);
        JSONObject commentJsonObject = JSON.parseObject(configConstant.getDonationsComment());
        JSONArray conmmentLists = commentJsonObject.getJSONArray("comment");
        String comment = (String) conmmentLists.get(donationsCommentIndex);
        return comment;
    }


    private CfPayOrigin getNewPayOrigin(PayType payTypeChannel, Platform platformEnum, CfInfoExtEnum.PayType payTypeEnum) {
        return PlatformUtil.getCfPayOrigin(payTypeChannel, platformEnum, payTypeEnum);
    }

    //订单切换到新实例
    private Response<Void> saveOrderNewInstance(OrderOperateService.OrderAndPayRecord orderAndPayRecord, CrowdfundingInfo crowdfundingInfo, int amountInFen, String splitFlowUuid,
                                                Long userAddressId, Long gearId, int goodsCount, String shareSourceId, int blessingCardId, int shareDv) {
        CrowdfundingOrder order = orderAndPayRecord.getCrowdfundingOrder();
        CrowdfundingPayRecord record = orderAndPayRecord.getCrowdfundingPayRecord();
        int cfId = crowdfundingInfo.getId();
        String infoUuid = crowdfundingInfo.getInfoId();
        crowdfundingOrderBiz.addOrderNewInstance(order, splitFlowUuid, shareSourceId, crowdfundingInfo.getStatus().value(), crowdfundingInfo.getDataStatus(), blessingCardId);
        crowdfundingPayRecordBiz.addPayRecordNewInstance(record);
        CaseOrder caseOrder = new CaseOrder();
        try {
            if(CrowdfundingType.fromValue(crowdfundingInfo.getType()) == CrowdfundingType.GOODS) {
                Response goodsOrderResult = saveGoodOrderExt(infoUuid, order.getId(), userAddressId, gearId,
                        amountInFen, goodsCount);
                if(goodsOrderResult.getCode() != CfErrorCode.SUCCESS.getCode()) {
                    return NewResponseUtil.makeResponse(goodsOrderResult.getCode(), goodsOrderResult.getMsg(), null);
                }
            }

            caseOrder.setPay_time(Optional.ofNullable(order.getPayTime()).map(Date::getTime).orElse(0l));
            caseOrder.setCase_id(infoUuid);
            caseOrder.setInfo_id(Long.valueOf(cfId));
            caseOrder.setCreate_time(Optional.ofNullable(order.getCtime()).map(Date::getTime).orElse(0l));

            caseOrder.setCurrentStep(1);
            caseOrder.setTotalStep(2);
            caseOrder.setJoinValue(String.valueOf(order.getId()));
            caseOrder.setUser_tag(String.valueOf(order.getUserId()));
            caseOrder.setUser_tag_type(UserTagTypeEnum.userid);

            caseOrder.setThird_type(Optional.ofNullable(order.getUserThirdType()).map(Long::valueOf).orElse(0l));
            caseOrder.setOrder_id(Optional.ofNullable(order.getId()).orElse(0l));
            caseOrder.setDonate_amt(Optional.ofNullable(order.getAmount()).map(Long::valueOf).orElse(0l));
            caseOrder.setPay_seq(record.getPayUid());
            caseOrder.setIp(Optional.ofNullable(order.getIp()).map(String::valueOf).orElse(""));
            caseOrder.setIs_anonymous(Optional.ofNullable(order.isAnonymous()).map(String::valueOf).orElse(""));
            caseOrder.setPay_status(Optional.ofNullable(order.getPayStatus()).map(Long::valueOf).orElse(0l));
            caseOrder.setUser_tag(String.valueOf(order.getUserId()));
            caseOrder.setUser_tag_type(UserTagTypeEnum.userid);
            if(order.getActivityId() == 0) {
                caseOrder.setActivity_id("");
            } else {
                caseOrder.setActivity_id(String.valueOf(order.getActivityId()));
            }
            caseOrder.setShare_dv(Long.valueOf(shareDv));
            analytics.track(caseOrder);
            log.info("大数据打点上报,案例捐款成功-下单:{}", JSONObject.toJSONString(caseOrder));
        } catch (Exception e) {
            log.error("大数据打点上报异常,案例捐款成功-下单:{}", JSONObject.toJSONString(caseOrder), e);
        }
        return NewResponseUtil.makeSuccess(null);
    }


    private Response<Void> saveGoodOrderExt(String infoUuid, long orderId, Long userAddressId, Long gearId, int amountInFen,
                                            int goodsCount) {
        if(userAddressId == null && gearId == null) {
            return ResponseUtil.makeSuccess(null);
        }
        userAddressId = userAddressId == null ? 0 : userAddressId;
        gearId = gearId == null ? 0 : gearId;
        return this.goodsOrderExtBiz.save(infoUuid, orderId, userAddressId, gearId, amountInFen, goodsCount);
    }


    /**
     * 获取tracer中的扩展信息，拿取扩展信息
     * 参考 {@link Analytics}
     * 需求：如果走的广告系统，那么有创意ID（creative_id）
     * 标记此笔订单属于帮筹金额
     */
    private void markCreativeFlag(int caseId, List<CrowdfundingInfo> subCrowdfundingInfoList, long orderId, List<OrderOperateService.OrderAndPayRecord> allOrderAndPayRecords) {
        try {
            String commonMessage = ExtraFieldPropagation.get("x-common-message");
            log.info("commonMessage:{}", commonMessage);
            if(StringUtils.isEmpty(commonMessage)) {
                return;
            }
            commonMessage = new String(Base64.decodeBase64(commonMessage.getBytes()), "UTF-8");
            if(StringUtils.isEmpty(commonMessage)) {
                return;
            }
            try {
                JSONObject commonMessageObject = JSONObject.parseObject(commonMessage);
                JSONObject ac = commonMessageObject.getJSONObject("ac");
                if(Objects.isNull(ac)) {
                    return;
                }
                String pageId = ac.getString("c");
                log.info("pageId:{}", pageId);
                if(StringUtils.isBlank(pageId)) {
                    return;
                }
                if (CollectionUtils.isEmpty(allOrderAndPayRecords)) {
                    cfManageClient.markPlatformDonate(caseId, orderId);
                } else {
                    Map<Integer, Long> caseIdOrderIdMap = allOrderAndPayRecords.stream()
                            .collect(Collectors.toMap(item -> item.getCrowdfundingOrder().getCrowdfundingId()
                                    , item -> item.getCrowdfundingOrder().getId(), (a, b) -> b));
                    cfManageClient.markPlatformDonateBatch(caseIdOrderIdMap);
                }
            } catch (Exception e) {
                String[] arr = commonMessage.split("\\|");
                if(arr.length >= 8 && StringUtils.isNotBlank(arr[6])) {
                    cfManageClient.markPlatformDonate(caseId, orderId);
                }
            }
        } catch (Exception e) {
            log.warn("donate success parse commonMessage error...");
        }
    }


    /**
     * 捐献下单
     * @param contributeAddOrderDTO
     * @param userId
     * @param clientIp
     * @param osType
     * @return
     */
    @Override
    public Response<OrderResultVo> createContributeOrder(ContributeAddOrderDTO contributeAddOrderDTO, long userId, String clientIp, Integer osType) {

        // from
        if(contributeAddOrderDTO.getSource()!=null){
            String source = contributeAddOrderDTO.getSource();
            if(source.length() == 0 || source.length() > CrowdfundingCons.MAX_FROM_LEN) {
                contributeAddOrderDTO.setSource(null);
            }
        }
        if(contributeAddOrderDTO.getSelfTag() != null){
            String selfTag = contributeAddOrderDTO.getSelfTag();
            if(selfTag.length() == 0 || selfTag.length() > CrowdfundingCons.MAX_FROM_LEN) {
                contributeAddOrderDTO.setSelfTag(null);
            }
        }
        if(contributeAddOrderDTO.getAnonymous() == null){
            contributeAddOrderDTO.setAnonymous(false);
        }
        PayType payTypeChannel = PayTypeNewEnum.fromCode(contributeAddOrderDTO.getPayType());
        if(payTypeChannel == null) {
            payTypeChannel = PayType.QPAY_MINIAPP.equals(PayType.fromCode(contributeAddOrderDTO.getPayType())) ? PayType.fromCode(contributeAddOrderDTO.getPayType()) : payTypeChannel;
        }
        Platform platformEnum = Platform.findByCode(contributeAddOrderDTO.getPlatform());
        // 新支付
        CfInfoExtEnum.PayType payTypeEnum = CfInfoExtEnum.PayType.WX_HEALTH_SECURITY;
        String productName = "资助水滴平台";
        CfPayOrigin cfPayOriginEnum = getNewPayOrigin(payTypeChannel, platformEnum, payTypeEnum);

        log.info("cfPayOriginEnum:{};userThirdType:{};", cfPayOriginEnum, contributeAddOrderDTO.getUserThirdType());
        if(CfPayOrigin.ILLEGAL == cfPayOriginEnum) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        PayInfoVo payInfoVo = null;
        CrowdfundingType crowdfundingType = CrowdfundingType.SERIOUS_ILLNESS;
        if(PayType.isWeixin(payTypeChannel) || PayType.isQPay(payTypeChannel)) {
            payInfoVo = this.payBizForCf.getPayInfoVoWithNew(crowdfundingType, payTypeChannel, payTypeEnum, contributeAddOrderDTO.getUserThirdType(), userId);
        }
        log.info("after change cfPayOriginEnum:{}", cfPayOriginEnum);

        Integer amountInFen = (int) MoneyUtil.changeYuanStrToFen(String.valueOf(contributeAddOrderDTO.getAmt()), RoundingMode.HALF_UP);
        log.info("amountInFen:{}", amountInFen);
        String payUid = IdGenUtil.tradeNo();
        // 保存 order
        int userThirdTypeEnum = AccountThirdTypeEnum.EMPTY.getCode();
        if(payInfoVo != null) {
            userThirdTypeEnum = payInfoVo.getUserThirdTypeEnum();
        }
        //存库
        CfContributeOrder contributeOrder = orderOperateService.addContributeOrderAndPayRecord( userId, userThirdTypeEnum, amountInFen,
                payUid, cfPayOriginEnum.getCode(), clientIp, osType, productName,contributeAddOrderDTO);

        String payBody = productName;
        String appId = "";
        String openId = "";
        if(payInfoVo != null) {
            appId = payInfoVo.getAppId();
            openId = payInfoVo.getOpenId();
        }
        //调支付
        DepositTypeEnum depositTypeEnum = DepositTypeEnum.NEEDLESS;
        PayResultV2 payInfo = payBizForCf.payInfoByNewV3(platformEnum, cfPayOriginEnum, payUid, amountInFen,
                openId, payBody, appId, userId, depositTypeEnum, CombineCommonEnum.CombineOrderType.CONTRIBUTE.getClientId());
        OrderResultVo vo = new OrderResultVo();
        vo.setTradeNo(payUid);
        vo.setAmt(amountInFen);
        vo.setBody(payBody);
        vo.setOpenId(openId);
        vo.setPayInfo(payInfo);
        vo.setPayType(platformEnum);
        //捐献成功通知
        contributeOrderService.sendContributeActionMsg(contributeOrder.getPayUid(),
                CfContributeMsgBody.OrderAction.PRE_PAY);
        return ResponseUtil.makeSuccess(vo);
    }
}
