package com.shuidihuzhu.cf.financetoc.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.clinet.event.center.enums.BizParamEnum;
import com.shuidihuzhu.cf.clinet.event.center.enums.UserOperationTypeEnum;
import com.shuidihuzhu.cf.clinet.event.center.model.CfUserEvent;
import com.shuidihuzhu.cf.financetoc.biz.order.CrowdfundingOrderBiz;
import com.shuidihuzhu.cf.model.contribute.CfCombineOrderManage;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CfSharePromoteOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.client.baseservice.pay.model.v3.combine.callback.CombineInnerCallBack;
import com.shuidihuzhu.client.baseservice.pay.model.v3.combine.callback.CombineInnerSub;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/10/21
 */
@Slf4j
@Service
public class EventCenterService {

    @Autowired(required = false)
    private Producer producer;
    @Autowired
    private CrowdfundingOrderBiz crowdfundingOrderBiz;

    private static final String tag = com.shuidihuzhu.cf.clinet.event.center.constants.MQTagCons.USER_EVENT_FOR_EVENT_CENTER;
    private static final String topic = com.shuidihuzhu.cf.clinet.event.center.constants.MQTopicCons.CF;

    public void sendEventCenter(CrowdfundingInfo crowdfundingInfo, int type, Long userId, String name) {
        if (crowdfundingInfo == null) {
            return;
        }
        sendEventCenter(crowdfundingInfo.getUserId(), crowdfundingInfo.getInfoId(), crowdfundingInfo.getId(), type, userId, name);
    }

    public void sendEventCenter(CrowdfundingOrder successOrder, int type, CfInfoSimpleModel simpleModel, CfCombineOrderManage combineOrderManage) {
        if (simpleModel == null || successOrder == null) {
            return;
        }
        sendEventCenter(successOrder, type, simpleModel.getId(), simpleModel.getInfoId(), Optional.ofNullable(combineOrderManage)
                .map(CfCombineOrderManage::getBizType)
                .orElse(-1));
    }

    public void sendEventCenter(long operatorUserId, int type, CfInfoSimpleModel simpleModel) {
        if(simpleModel == null) {
            return;
        }
        sendEventCenter(operatorUserId, type, simpleModel.getId(), simpleModel.getInfoId());
    }

    public void sendEventCenter(int type, CfSharePromoteOrder cfSharePromoteOrder, String infoUuid) {
        if (cfSharePromoteOrder == null) {
            return;
        }
        sendEventCenter(cfSharePromoteOrder.getSourceUserId(), type, cfSharePromoteOrder.getInfoId(), cfSharePromoteOrder.getAmmountInYuan() * 100, infoUuid);
    }

    public void sendEventCenter(long operatorUserId, String infoUuid, int caseId, int type, Long userId, String name) {
        Map<String, String> dataMap = Maps.newHashMap();
        dataMap.put(BizParamEnum.FRIEND_NAME.getDesc(), name);
        dataMap.put(BizParamEnum.INFO_UUID.getDesc(), infoUuid);
        dataMap.put(BizParamEnum.ORDER_USER_ID.getDesc(), String.valueOf(userId));

        sendEventCenter(operatorUserId, type, caseId, dataMap);
    }

    public void sendEventCenter(int type, CrowdfundingInfo crowdfundingInfo) {
        if (crowdfundingInfo == null) {
            return;
        }
        sendEventCenter(crowdfundingInfo.getUserId(), type, crowdfundingInfo.getId(), crowdfundingInfo.getInfoId());
    }

    public void sendEventCenter(long operatorUserId, int type, int cfVersion) {
        Map<String, String> dataMap = Maps.newHashMap();
        dataMap.put(BizParamEnum.CF_VERSION.getDesc(), String.valueOf(cfVersion));
        dataMap.put(BizParamEnum.DRAFT_PAGE_FLAG.getDesc(), StringUtils.EMPTY);

        sendEventCenter(operatorUserId, type, 0, dataMap);
    }

    public void sendEventCenter(long operatorUserId, int type, int caseId, String infoId) {
        Map<String, String> dataMap = Maps.newHashMap();
        dataMap.put(BizParamEnum.INFO_UUID.getDesc(), infoId);

        sendEventCenter(operatorUserId, type, caseId, dataMap);
    }

    public void sendEventCenter(long operatorUserId, int type, int caseId, double ammountInYuan, String infoUuid) {

        Map<String, String> dataMap = Maps.newHashMap();
        dataMap.put(BizParamEnum.FIRST_DONATION.getDesc(), String.valueOf((int) ammountInYuan));
        dataMap.put(BizParamEnum.INFO_UUID.getDesc(), infoUuid);

        sendEventCenter(operatorUserId, type, caseId, dataMap);
    }

    public void sendEventCenter(long operatorUserId, int type, String infoUuid) {
        Map<String, String> dataMap = Maps.newHashMap();
        dataMap.put(BizParamEnum.INFO_UUID.getDesc(), infoUuid);

        sendEventCenter(operatorUserId, type, 0, dataMap);
    }

    public void sendEventCenter(CrowdfundingOrder successOrder, int type, int caseId, String infoId) {
        this.sendEventCenter(successOrder, type, caseId, infoId, -1);
    }

    public void sendEventCenter(CrowdfundingOrder successOrder, int type, int caseId, String infoId, int combineOrderBizType) {

        Map<String, String> dataMap = Maps.newHashMap();
        dataMap.put(BizParamEnum.INFO_UUID.getDesc(), infoId);
        dataMap.put(BizParamEnum.PAY_UID.getDesc(), successOrder.getPayUid());
        dataMap.put(BizParamEnum.ORDER_AMOUNT.getDesc(), successOrder.getAmount().toString());
        dataMap.put(BizParamEnum.ORDER_ID.getDesc(), successOrder.getId().toString());
        dataMap.put(BizParamEnum.ORDER_USER_ID.getDesc(), String.valueOf(successOrder.getUserId()));
        dataMap.put(BizParamEnum.COMBINE_ORDER_BIZ_TYPE.getDesc(), String.valueOf(combineOrderBizType));

        sendEventCenter(successOrder.getUserId(), type, caseId, dataMap);
    }

    public void sendEventCenterForAttention(long operatorUserId, long targetUserId, int caseId, String infoUuid, int attentionFrom, String targetId) {
        Map<String, String> dataMap = Maps.newHashMap();
        if (StringUtils.isNotBlank(infoUuid)) {
            dataMap.put(BizParamEnum.INFO_UUID.getDesc(), infoUuid);
        }

        dataMap.put(BizParamEnum.USER_ATTENTION_FROM.getDesc(), String.valueOf(attentionFrom));
        dataMap.put(BizParamEnum.USER_ATTENTION_TARGET_ID.getDesc(), targetId);

        sendEventCenter(operatorUserId, UserOperationTypeEnum.USER_ATTENTION.getCode(), caseId, dataMap, Lists.newArrayList(targetUserId));
    }


    private void sendEventCenter(long operatorUserId, int type, int caseId, Map<String, String> dataMap) {
        sendEventCenter(operatorUserId, type, caseId, dataMap, Lists.newArrayList());
    }

    private void sendEventCenter(long operatorUserId, int type, int caseId, Map<String, String> dataMap, List<Long> targetUserIds) {
        if (producer == null) {
            return;
        }

        try {
            CfUserEvent cfUserEvent = new CfUserEvent();
            cfUserEvent.setUserId(operatorUserId);
            cfUserEvent.setCaseId(caseId);
            cfUserEvent.setType(type);
            cfUserEvent.setTargetUserIds(targetUserIds);
            if (MapUtils.isNotEmpty(dataMap)) {
                cfUserEvent.setDataMap(dataMap);
            }

            producer.send(new Message<>(topic, tag, tag + "_" + caseId, cfUserEvent));
        } catch (Exception e) {
            log.error("sendEventCenter error. userId:{}, caseId:{}, type:{}, dataMap:{}, targetUserIds:{}", operatorUserId, caseId, type, dataMap, targetUserIds, e);
        }
    }

    /**
     * 合并支付消息
     *
     * @param mainCombineOrderManage -
     * @param combineCallBack -
     */
    @Async
    public void sendCombinePaySuccessEvent(CfCombineOrderManage mainCombineOrderManage, CombineInnerCallBack combineCallBack) {
        if (mainCombineOrderManage == null) {
            return;
        }
        int caseId = mainCombineOrderManage.getCaseId();
        // 捐款笔数
        int combineCount = combineCallBack.getSubOrders().size();
        // 支付总金额
        int amountInFen = combineCallBack.getSubOrders().stream().mapToInt(CombineInnerSub::getRealPayAmount).sum();
        CrowdfundingOrder crowdfundingOrder = crowdfundingOrderBiz.getById(mainCombineOrderManage.getOrderId());
        if (crowdfundingOrder == null) {
            return ;
        }
        long userId = crowdfundingOrder.getUserId();
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put(BizParamEnum.COMBINE_ORDER_COUNT.getDesc(), String.valueOf(combineCount));
        dataMap.put(BizParamEnum.ORDER_AMOUNT.getDesc(), MoneyUtil.buildBalance(amountInFen));
        dataMap.put(BizParamEnum.COMBINE_ORDER_BIZ_TYPE.getDesc(), String.valueOf(mainCombineOrderManage.getBizType()));
        try {
            CfUserEvent cfUserEvent = new CfUserEvent();
            cfUserEvent.setCaseId(caseId);
            cfUserEvent.setType(UserOperationTypeEnum.COMBINE_ORDER_SUCCESS.getCode());
            cfUserEvent.setUserId(userId);
            cfUserEvent.setTargetUserIds(Lists.newArrayList(userId));
            if (MapUtils.isNotEmpty(dataMap)) {
                cfUserEvent.setDataMap(dataMap);
            }
            producer.send(new Message<>(topic, tag, tag + "_" + caseId, cfUserEvent));
        } catch (Exception e) {
            log.error("sendEventCenter error. userId:{}, caseId:{}, dataMap:{}", userId, caseId, dataMap, e);
        }
    }
}
