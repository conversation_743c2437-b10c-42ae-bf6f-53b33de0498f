package com.shuidihuzhu.cf.financetoc.service;

import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
@RefreshScope
public class CfFriendLoopServiceV2 {
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;

    private String getRedissonKey(String key) {
        return key + "_friend_v3";
    }


    @Async
    public void delShareFriendCash(long userId, int crowdFundingId) {
        String key = this.getRedissonKey(userId + "_" + crowdFundingId + "_" + "share");
        cfRedissonHandler.del(key);
    }

}
