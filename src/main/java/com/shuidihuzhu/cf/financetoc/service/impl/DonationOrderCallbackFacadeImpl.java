package com.shuidihuzhu.cf.financetoc.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.enums.combine.CombineCommonEnum;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.financetoc.biz.order.CfCombineOrderManageBiz;
import com.shuidihuzhu.cf.financetoc.biz.order.CfContributeOrderBiz;
import com.shuidihuzhu.cf.financetoc.biz.order.CrowdfundingOrderBiz;
import com.shuidihuzhu.cf.financetoc.biz.order.CrowdfundingPayRecordBiz;
import com.shuidihuzhu.cf.financetoc.delegate.IPayClientRpcAdapter;
import com.shuidihuzhu.cf.financetoc.service.*;
import com.shuidihuzhu.cf.model.contribute.CfCombineOrderManage;
import com.shuidihuzhu.cf.model.contribute.CfPayInnerCallBack;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrderExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord;
import com.shuidihuzhu.client.baseservice.pay.model.PayInnerCallBack;
import com.shuidihuzhu.client.baseservice.pay.model.PayInnerCallBackInfo;
import com.shuidihuzhu.client.baseservice.pay.model.v3.combine.callback.CombineInnerCallBack;
import com.shuidihuzhu.client.baseservice.pay.model.v3.combine.callback.CombineInnerCallBackInfo;
import com.shuidihuzhu.client.baseservice.pay.model.v3.combine.callback.CombineInnerSub;
import com.shuidihuzhu.client.baseservice.pay.util.PayServiceCallbackUtil;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.enums.PayStatus;
import com.shuidihuzhu.common.web.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DonationOrderCallbackFacadeImpl implements DonationOrderCallbackFacade {

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CrowdfundingOrderBiz crowdfundingOrderBiz;
    @Autowired
    private CrowdfundingPayRecordBiz crowdfundingPayRecordBiz;
    @Autowired
    private GoodsOrderExtService goodsOrderExtService;
    @Autowired
    private CrowdfundingInfoDetailRedisService crowdfundingInfoDetailRedisService;
    @Autowired
    private CfCapitalService cfCapitalService;
    @Resource(name = "cfCoreRedissonHandler")
    private RedissonHandler cfCoreRedissonHandler;
    @Autowired
    private IPayClientRpcAdapter payClientRpcAdapter;
    @Autowired
    private CfCombineOrderManageBiz orderManageBiz;
    @Autowired
    private CfContributeOrderBiz contributeOrderBiz;
    @Autowired
    private EventCenterService eventCenterService;


    @Override
    public String handlePayCallback(PayInnerCallBack payInnerCallBack,int operatorId) {
        if (payInnerCallBack == null) {
            log.info("支付回调为空");
            return PayServiceCallbackUtil.ResponseBody.FAIL.getBody();
        }
        String identifier = null;
        String lockName = "cf-pay-callback-" + payInnerCallBack.getPayUid() + "-" + payInnerCallBack.getOrderId();
        try {
            identifier = cfCoreRedissonHandler.tryLock(lockName, 0, 60 * 1000L);
            if (StringUtils.isBlank(identifier)) {
                log.info("cf-pay-callback:{} get redisson lock failed", lockName);
                return PayServiceCallbackUtil.ResponseBody.FAIL.getBody();
            }
            /**
             * 非资金业务添加到MQ中,不要在此类添加！
             * @see CfDonationSuccessBizConsumer
             */
            boolean result = forBusiness(CfPayInnerCallBack.convertFromPayInner(payInnerCallBack), operatorId);
            if (result) {
                return PayServiceCallbackUtil.ResponseBody.SUCCESS.getBody();
            } else {
                return PayServiceCallbackUtil.ResponseBody.FAIL.getBody();
            }
        } catch (Exception e) {
            log.error("支付回调", e);
            return PayServiceCallbackUtil.ResponseBody.FAIL.getBody();
        } finally {
            log.info("cf-pay-callback callback end...");
            try {
                if (StringUtils.isNotBlank(identifier)) {
                    cfCoreRedissonHandler.unLock(lockName, identifier);
                }
            } catch (Exception e) {
                log.error("", e);
            }
        }
    }




    @Override
    public PayInnerCallBack buildPayCallBackParam(HttpServletRequest request) {
        // 获取通知数据需要从body中流式读取
        try {
            log.info("request map = {}", JSON.toJSONString(request.getParameterMap()));
            PayInnerCallBack payInnerCallBack = this.payClientRpcAdapter.verifyCallback(new PayInnerCallBackInfo(request));

            return payInnerCallBack;
        } catch (Exception e) {
            log.info("", e);
            return null;
        }
    }

    @Override
    public CombineInnerCallBack buildCombineCallBackParam(HttpServletRequest request) {
        try {
            log.info("合单支付回调 request map = {}", JSON.toJSONString(request.getParameterMap()));
            CombineInnerCallBack payInnerCallBack = new CombineInnerCallBackInfo(request).getCombineInnerCallBack();
            log.info("合单支付回调 callBack map = {}", JSON.toJSONString(payInnerCallBack));
            return payInnerCallBack;
        } catch (Exception e) {
            log.error("合单支付回调验签异常", e);
            return null;
        }
    }

    /**
     * 捐赠支付成功回调
     * @param payInnerCallBack
     * @param operatorId
     * @return
     */
    @Override
    public String handleContributePayCallback(PayInnerCallBack payInnerCallBack, int operatorId) {

        if (payInnerCallBack == null) {
            log.info("支付回调为空");
            return PayServiceCallbackUtil.ResponseBody.FAIL.getBody();
        }
        String identifier = null;
        String lockName = "cf-pay-callback-" + payInnerCallBack.getPayUid() + "-" + payInnerCallBack.getOrderId();
        try {
            identifier = cfCoreRedissonHandler.tryLock(lockName, 0, 60 * 1000L);
            if (StringUtils.isBlank(identifier)) {
                log.info("cf-contribute-pay-callback:{} get redisson lock failed", lockName);
                return PayServiceCallbackUtil.ResponseBody.FAIL.getBody();
            }
            boolean result = contributeOrderBiz.forContributeBusiness(CfPayInnerCallBack.convertFromPayInner(payInnerCallBack));
            if (result) {
                return PayServiceCallbackUtil.ResponseBody.SUCCESS.getBody();
            } else {
                return PayServiceCallbackUtil.ResponseBody.FAIL.getBody();
            }
        } catch (Exception e) {
            log.error("资助支付回调", e);
            return PayServiceCallbackUtil.ResponseBody.FAIL.getBody();
        } finally {
            log.info("cf-contribute-pay-callback callback end...");
            try {
                if (StringUtils.isNotBlank(identifier)) {
                    cfCoreRedissonHandler.unLock(lockName, identifier);
                }
            } catch (Exception e) {
                log.error("", e);
            }
        }

    }


    private boolean forBusiness(CfPayInnerCallBack payInnerCallBack, int operatorId) {

        String payUid = payInnerCallBack.getOrderId();
        CrowdfundingPayRecord payRecord = crowdfundingPayRecordBiz.getByPayUid(payUid);
        if (payRecord == null) {
            log.error("crowdfunding-payService callback pay record error, payUid:{}", payUid);
            return false;
        }

        CrowdfundingOrder order = crowdfundingOrderBiz.getById(payRecord.getCrowdfundingOrderId());
        if (order == null) {
            log.error("crowdfunding-payService callback order error, payUid:{}, orderId:{}", payUid,
                    payRecord.getCrowdfundingOrderId());
            return false;
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(order.getCrowdfundingId());
        if(crowdfundingInfo == null) {
            log.error("paySuccess crowdfundingInfo is null!!!");
            return false;
        }

        log.info("支付回调 payuid:{}", payRecord.getPayUid());
        //设置支付回调时间  封装mq消息需要使用的参数
        Date payTime = new Date();
        order.setPayTime(payInnerCallBack.getBusinessTime() == null ? payTime : payInnerCallBack.getBusinessTime());
        order.setPayUid(payUid);
        order.setCfAmount(crowdfundingInfo.getAmount());
        order.setInfoId(crowdfundingInfo.getInfoId());

        CrowdfundingOrderExt ext = crowdfundingOrderBiz.getCrowdfundingOrderExtByOrderId(order.getId(), order.getCtime());
        log.debug("CrowdfundingOrderExt: {}", JSON.toJSONString(ext));
        if(ext != null){
            order.setShareSourceId(ext.getShareSourceId());
        }
        //封装完毕

        boolean result = updateOrder(crowdfundingInfo,payRecord, order,
                payInnerCallBack.getAmountInFen(), payInnerCallBack.getPayUid(), payInnerCallBack.getBizType(),operatorId
                , payInnerCallBack.getFeeAmountInFen(), payInnerCallBack.getFeePercent());
        try {
            //order 里面更新了支付状态  可以直接发送
            clearRedis(order);
            // 支付成功后的业务流程
            cfCapitalService.sendMQ(order);
        } catch (Exception e) {
            log.error("支付回调", e);
        }
        return result;
    }


    private boolean updateOrder(CrowdfundingInfo crowdfundingInfo,CrowdfundingPayRecord payRecord,
                                CrowdfundingOrder order, int realAmount, String thirdPayUid, int payBizType,int operatorId,
                                int feeAmountInFen, String feePercent) {
        String payUid = payRecord.getPayUid();
        if (payRecord.getPayStatus().equals(PayStatus.PAY_SUCCESS.getCode())
                && order.getPayStatus().equals(PayStatus.PAY_SUCCESS.getCode())) {
            log.warn("crowdfunding-payService callback duplicate, payUid:{}, orderId:{}", payUid,
                    payRecord.getCrowdfundingOrderId());
            return true;
        }
        // 先更新实物库存
        // 废弃逻辑直接删除
        try {
            this.goodsOrderExtService.updateByCallback(order,crowdfundingInfo);
        } catch (Exception e) {
            log.error("GoodsOrderExtService updateByCallback Error!", e);
        }

        //资金系统补单MQ
        cfCapitalService.sendPayCheckMQ(order.getId(), order.getUserId(), order.getCrowdfundingId(),
                order.getInfoId(), order.getPayUid(),  thirdPayUid, payBizType, realAmount, order.getPayTime(), feeAmountInFen, feePercent);

        //更新订单
        cfCapitalService.paySuccess(crowdfundingInfo,order, realAmount, payUid, thirdPayUid, payBizType,operatorId, feeAmountInFen, feePercent);

        return true;
    }

    @Override
    public String handleCombinePayCallback(CombineInnerCallBack combineCallBack, int operatorId) {

        if (combineCallBack == null || CollectionUtils.isEmpty(combineCallBack.getSubOrders())) {
            log.info("支付回调为空 param:{}", JSON.toJSONString(combineCallBack));
            return PayServiceCallbackUtil.ResponseBody.FAIL.getBody();
        }

        String identifier = null;
        String lockName = "combine-pay-callback-" + combineCallBack.getPayUid() + "-" + combineCallBack.getOrderId();
        try {
            identifier = cfCoreRedissonHandler.tryLock(lockName, 0, 60 * 1000L);
            if (StringUtils.isBlank(identifier)) {
                log.info("combine-pay-callback:{} get redisson lock failed", lockName);
                return PayServiceCallbackUtil.ResponseBody.FAIL.getBody();
            }

            return updateCombineBusiness(combineCallBack, operatorId) ? PayServiceCallbackUtil.ResponseBody.SUCCESS.getBody()
                    : PayServiceCallbackUtil.ResponseBody.FAIL.getBody();

        } catch (Exception e) {
            log.error("支付回调执行异常 param:{}", JSON.toJSONString(combineCallBack), e);
            return PayServiceCallbackUtil.ResponseBody.FAIL.getBody();
        } finally {
            log.info("combine-pay-callback callback end...");
            try {
                if (StringUtils.isNotBlank(identifier)) {
                    cfCoreRedissonHandler.unLock(lockName, identifier);
                }
            } catch (Exception e) {
                log.error("combine-pay-callback异常", e);
            }
        }
    }

    private boolean updateCombineBusiness(CombineInnerCallBack combineCallBack, int operatorId) {
        Map<String, CfCombineOrderManage> combineMapping = orderManageBiz
                .selectMappingByParentUid(combineCallBack.getOrderId());
        if (!validate(combineCallBack, combineMapping)) {
            log.error("回调接口和业务接口订单数不一致callback:{} mapping:{}", JSON.toJSONString(combineCallBack),
                    JSON.toJSONString(combineMapping));
            // 发微信报警消息
            return false;
        }

        orderManageBiz.updatePaySuccess(combineCallBack, combineMapping);

        boolean result = true;
        // 合并支付业务场景
        CfCombineOrderManage main = null;
        for (CombineInnerSub innerSub : combineCallBack.getSubOrders()) {
            CfCombineOrderManage orderManage = combineMapping.get(innerSub.getSubOrderId());
            CombineCommonEnum.CombineOrderType orderType = CombineCommonEnum
                    .CombineOrderType.valueOfCode(orderManage.getOrderType());
            if (main == null && orderManage.getMain() == 1) {
                main = orderManage;
            }
            switch (orderType) {
                case CASE_DONATE:
                    result = result && forBusiness(CfPayInnerCallBack.convertFrom(combineCallBack, innerSub, orderType), operatorId);
                    break;
                case CONTRIBUTE:
                    result = result && contributeOrderBiz.forContributeBusiness(
                            CfPayInnerCallBack.convertFrom(combineCallBack, innerSub, orderType));
                    break;
            }
        }
        // 合并支付发送事件中心MQ
        eventCenterService.sendCombinePaySuccessEvent(main, combineCallBack);
        return result;
    }

    private boolean validate(CombineInnerCallBack combineCallBack,
                             Map<String, CfCombineOrderManage> combineMapping) {
        if (combineMapping.size() != combineCallBack.getSubOrders().size()) {
            // 发微信报警消息
            return false;
        }

        for (CombineInnerSub innerSub : combineCallBack.getSubOrders()) {
            if (combineMapping.get(innerSub.getSubOrderId()) == null) {
                return false;
            }
        }
        return true;
    }


    private void clearRedis(CrowdfundingOrder successOrder) {
        try {
            Stopwatch stopwatch = Stopwatch.createStarted();
            this.crowdfundingInfoDetailRedisService.clearSelfDetailRedis(successOrder.getCrowdfundingId(), successOrder.getUserId());
            this.crowdfundingInfoDetailRedisService.clearOrderRedis(successOrder.getCrowdfundingId(), successOrder.getUserId());
            log.debug("clearSelfDetailRedis cost {}ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));
        } catch (Exception e) {
            log.error("", e);
        }
    }

    @Override
    public PayInnerCallBack buildMockPayCallBackParam(HttpServletRequest request) {
        // 获取通知数据需要从body中流式读取
        try {
            log.info("request map = {}", JSON.toJSONString(request.getParameterMap()));
            PayInnerCallBackInfo payInnerCallBackInfo = new PayInnerCallBackInfo(request);
            PayInnerCallBack innerCallBack  = payInnerCallBackInfo.getPayInnerCallBack();
            Map<String, String> paramPair = Maps.newHashMap();
            paramPair.put("orderId", innerCallBack.getOrderId());
            paramPair.put("payUid", innerCallBack.getPayUid());
            paramPair.put("amountInFen", "" + innerCallBack.getAmountInFen());
            paramPair.put("businessTime", DateUtil.getLong2LStr(innerCallBack.getBusinessTime().getTime()));
            paramPair.put("businessInfo", innerCallBack.getBusinessInfo());
            if (StringUtils.isNotBlank(innerCallBack.getSignNo())) {
                paramPair.put("signNo", innerCallBack.getSignNo());
            }

            Map<String, String> localMap = Maps.newHashMap();
            localMap.putAll(paramPair);

            Map<String, String> localMapWithOutEmpty = Maps.newHashMap();
            for (Map.Entry<String, String> entry : localMap.entrySet()) {
                if (entry.getValue() != null && entry.getValue().length() > 0) {
                    localMapWithOutEmpty.put(entry.getKey(), entry.getValue());
                }
            }

            LinkedHashMap<String, String> sortedMap = Maps.newLinkedHashMap();
            List<Map.Entry<String, String>> entryList = Lists.newArrayList(localMapWithOutEmpty.entrySet());
            entryList.sort(new DonationOrderCallbackFacadeImpl.MapValueComparator());
            for (Map.Entry<String, String> tmpEntry : entryList) {
                sortedMap.put(tmpEntry.getKey(), tmpEntry.getValue());
            }
            String paramStr = "";
            for (String key : sortedMap.keySet()) {
                String value = sortedMap.get(key);
                if (value != null && value.length() > 0) {
                    paramStr = String.format("%s&%s=%s", paramStr, key, value);
                }
            }
            paramStr =  paramStr.substring(1);
            paramStr = String.format("%s&%s", paramStr,  MD5Util.getMD5HashValue("ZR$bloqFilfPa5xvJMbJgoHX^0Tn1rg7"));
            String sig = MD5Util.getMD5HashValue(paramStr);

            Field nameField = payInnerCallBackInfo.getClass().getDeclaredField("signature");
            nameField.setAccessible(true);
            nameField.set(payInnerCallBackInfo, sig);
            PayInnerCallBack payInnerCallBack = this.payClientRpcAdapter.verifyCallback(payInnerCallBackInfo);

            return payInnerCallBack;
        } catch (Exception e) {
            log.info("", e);
            return null;
        }
    }

    private static class MapValueComparator implements Comparator<Map.Entry<String, String>> {
        @Override
        public int compare(Map.Entry<String, String> me1, Map.Entry<String, String> me2) {
            return me1.getKey().compareTo(me2.getKey());
        }
    }

}
