package com.shuidihuzhu.cf.financetoc.service;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2018-06-15  13:41
 */
@Setter
@Getter
@Service
public class ApplicationService implements EnvironmentAware {

    private static final String ENV_PRODUCTION = "production";

    private Environment environment;

    public boolean isDevelopment() {
        return !isProduction();
    }

    public boolean isProduction() {
        return environment.acceptsProfiles(ENV_PRODUCTION);
    }

    public String get(String production, String develop) {
        return isProduction() ? production : develop;
    }

    public String getActiveProfile(){
        String[] profiles = environment.getActiveProfiles();
        String activeProfile = StringUtils.join(profiles, ",");
        return activeProfile;
    }

}
