package com.shuidihuzhu.cf.financetoc.service;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingInfoStatusBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CfCommonStoreModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.vo.v5.CfMaterialAuditListView;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * @Author: lianghongchao
 * @Date: 2018/11/05 19:21
 */
@Slf4j
@Service
public class CfMaterialStatusService {
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CrowdfundingInfoStatusBiz crowdfundingInfoStatusBiz;
    @Autowired
    private CfCaseMaterialListService cfCaseMaterialListService;
    @Autowired
    private CfCommonStoreService cfCommonStoreService;

    @Data
    public static class ItemReason {
        private int itemIds;
        private Map<Integer, String> reason;
    }


    /**
     * 检测驳回时  是否提交了
     * @param infoUuid
     * @param typeEnum
     * @param rejectCodes
     * @return
     */
    public boolean checkReject4Submit(String infoUuid,
                                      long userId,
                                      CrowdfundingInfoDataStatusTypeEnum typeEnum,
                                      List<Integer> rejectCodes){
        log.info("checkReject4Submit infoUuid={} typeEnum={} rejectCodes={}",infoUuid,typeEnum,rejectCodes);
        if (CollectionUtils.isEmpty(rejectCodes)){
            return true;
        }

        CrowdfundingInfo simpleModel = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        Map<Integer, Integer> dataTypeStatus = crowdfundingInfoStatusBiz.getMapByInfoUuid(simpleModel.getInfoId());

        List<CfMaterialAuditListView.AuditModifyEntry> list =  cfCaseMaterialListService.getAllAuditEntry(simpleModel,Lists.newArrayList(typeEnum),dataTypeStatus);

        if (CollectionUtils.isEmpty(list)){
            log.info("当前案例没有驳回入口.caseId:{} type:{}", simpleModel.getId(), typeEnum);
           return false;
        }
        Map<Integer,List<CfMaterialAuditListView.AuditModifyEntry>> map = list.stream().collect(Collectors.groupingBy(CfMaterialAuditListView.AuditModifyEntry::getDataType));

        List<CfMaterialAuditListView.AuditModifyEntry> result =  map.get(typeEnum.getCode());

        List<Integer> codes = result.stream().map(CfMaterialAuditListView.AuditModifyEntry::getEntryCode).collect(Collectors.toList());

        List<String> keys = codes.stream().map(code->CfCaseMaterialListService.getEntryKey(infoUuid,code)).collect(Collectors.toList());

        List<CfCommonStoreModel> storeModels = cfCommonStoreService.getByKeys(userId,keys,infoUuid);

        if (CollectionUtils.isEmpty(storeModels)){
            log.info("storeModels =null infoUuid={},keys={}",infoUuid,keys);
            return false;
        }

        Set<String> set = storeModels.stream().map(CfCommonStoreModel::getStoreKey).collect(Collectors.toSet());
        Optional<String> optional = keys.stream().filter(s->!set.contains(s)).findAny();
        boolean r = optional.isEmpty();
        log.info("reject check infoUuid={} codes={} set={} r={}",infoUuid,codes,set,r);
        return r;
    }

}
