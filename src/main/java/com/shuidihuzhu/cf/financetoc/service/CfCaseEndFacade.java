package com.shuidihuzhu.cf.financetoc.service;

import com.shuidihuzhu.cf.enums.crowdfunding.CfFinishStatus;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.common.web.model.Response;

/**
 * <AUTHOR>
 */
public interface CfCaseEndFacade {

    OpResult stopCase(int caseId, CfFinishStatus endType, int operatorId);

    /**
     *
     * @param desc 停止原因
     * @return
     */
    OpResult stopCase(int caseId, CfFinishStatus endType, int operatorId, String desc, int reasonType);

    Response checkVerifyCode(long userId, String clientIp,String verifyCode);
}
