package com.shuidihuzhu.cf.financetoc.service;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.enums.crowdfunding.CfFinishStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingType;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.financetoc.dao.crowdfunding.CfInfoStatDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Slf4j
@Service
public class PayCallbackService {

    @Autowired
    private CfInfoStatDao cfInfoStatDao;
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CfCaseEndFacade cfCaseEndFacade;

    @Transactional(rollbackFor = Exception.class,transactionManager = CfDataSource.CROWDFUNDIN_DATASOURCE_TRANSACTION_MANAGER)
    public boolean addAmount(CrowdfundingInfo info, int amount) {
        int id = info.getId();
        log.info("addAmount id: {} ,amount: {}", id, amount);
        cfInfoStatDao.addAmount(id, amount);
        boolean result = crowdfundingInfoBiz.addAmount(id, amount)>0;
        return result;
    }
    //停止筹款
    public void stopCase(CrowdfundingInfo info, int amount,int operatorId){
        if (addAmountComplete(info, amount)) {
            cfCaseEndFacade.stopCase(info.getId(), CfFinishStatus.FUNDING_REACH_TARGET,operatorId);
        }
    }

    public boolean addAmountComplete(CrowdfundingInfo info, int amount) {
        if ((info.getType() == null || info.getType() == CrowdfundingType.SERIOUS_ILLNESS.value())
                && info.getAmount() + amount >= info.getTargetAmount()) {
            return true;
        }
        return false;
    }

}
