package com.shuidihuzhu.cf.financetoc.service.impl;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.financetoc.service.ICfRiskService;
import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.cf.risk.client.CfRiskBlackListClient;
import com.shuidihuzhu.client.cf.risk.client.CfRiskClient;
import com.shuidihuzhu.client.cf.risk.client.CfRiskPlatformClient;
import com.shuidihuzhu.client.cf.risk.model.enums.UserOperationEnum;
import com.shuidihuzhu.client.cf.risk.model.result.UserOperatorValidUnit;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2018/11/15 上午10:58
 * @desc
 */
@Service
@Slf4j
@RefreshScope
public class CfRiskServiceImpl implements ICfRiskService {
    @Autowired
    private CfRiskClient cfRiskClient;

    @Override
    public boolean operatorValid(long userId, int caseId, UserOperationEnum operationEnum) {
        return queryOperateValid(userId, caseId, operationEnum);
    }


    private boolean queryOperateValid(long userId, int caseId, UserOperationEnum operationEnum) {
        try {
            Response<UserOperatorValidUnit> response = cfRiskClient.queryOperateValid(userId, caseId, operationEnum);

            boolean action = Objects.nonNull(response) && 0 == response.getCode() && Objects.nonNull(response.getData()) && response.getData().isResult();

            log.debug("CfRiskServiceImpl.call risk queryOperateValid SUCCESS userId:{},caseId:{},action:{},operation:{},result:{}", userId, caseId, action, operationEnum.getMsg(), JSON.toJSONString(response.getData()));

            // 明确返回false，才禁止
            return 0 != response.getCode() || !Objects.nonNull(response.getData()) || response.getData().isResult();

        } catch (Exception e) {
            log.error("CfRiskServiceImpl.call risk queryOperateValid EXCEPTION userId:{},caseId:{},operation:{}", userId, caseId, operationEnum.getMsg(), e);
            return true;
        }
    }
}
