package com.shuidihuzhu.cf.financetoc.service;

import com.shuidihuzhu.client.baseservice.pay.model.PayInnerCallBack;
import com.shuidihuzhu.client.baseservice.pay.model.v3.combine.callback.CombineInnerCallBack;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
public interface DonationOrderCallbackFacade {


    String handlePayCallback(PayInnerCallBack payInnerCallBack,int operatorId);

    String handleCombinePayCallback(CombineInnerCallBack payInnerCallBack,int operatorId);

    PayInnerCallBack buildPayCallBackParam(HttpServletRequest request);

    CombineInnerCallBack buildCombineCallBackParam(HttpServletRequest request);

    String handleContributePayCallback(PayInnerCallBack payInnerCallBack, int operatorId);

    PayInnerCallBack buildMockPayCallBackParam(HttpServletRequest request);

}
