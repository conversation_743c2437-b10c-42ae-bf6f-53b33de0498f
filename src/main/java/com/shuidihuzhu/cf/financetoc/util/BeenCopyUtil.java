package com.shuidihuzhu.cf.financetoc.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.lang.reflect.Field;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2020/7/30
 */
public final class BeenCopyUtil {

    /**
     * 只替换json里面有的key  忽略其他的
     * @param target
     * @param json
     * @param tClass
     * @param <T>
     * @return
     */
    public static <T> T jsonToClass(T target,String json,Class<T> tClass){

        if (target == null || StringUtils.isBlank(json)){
            return null;
        }
        JSONObject jsonObject = JSON.parseObject(json);
        Set<String> sourceSet = jsonObject.keySet().stream().collect(Collectors.toSet());

        Set<String> targetSet = getFiledName(target);

        targetSet.removeAll(sourceSet);

        String[] ignoreProperties = targetSet.stream().toArray(String[]::new);

        T source = JSON.parseObject(json,tClass);

        BeanUtils.copyProperties(source,target,ignoreProperties);

        return target;
    }


    private static Set<String> getFiledName(Object o){

        Field[] fields=o.getClass().getDeclaredFields();
        Set<String> set = Sets.newHashSet();
        for(int i=0;i<fields.length;i++){
            set.add(fields[i].getName());
        }
        //兼容父类字段
        Class superclass =o.getClass().getSuperclass();
        if( ! superclass.equals(Object.class)){
            fields= superclass.getDeclaredFields();
            for(int i=0;i<fields.length;i++){
                set.add(fields[i].getName());
            }
        }

        return set;
    }
}
