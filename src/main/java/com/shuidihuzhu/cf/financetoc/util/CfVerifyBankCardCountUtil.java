package com.shuidihuzhu.cf.financetoc.util;

import com.shuidihuzhu.cf.constants.crowdfunding.RedisKeyCons;
import com.shuidihuzhu.cf.financetoc.biz.CfRedisKvBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CfVerifyBankCardCountResult;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * Created by chao on 2017/8/16.
 */
@Component
@RefreshScope
public class CfVerifyBankCardCountUtil {

	private static final int MAX_BANK_VERIFY_TIMES = 10;

	@Autowired
	private CfRedisKvBiz cfRedisKvBiz;

	@Resource(name = "cfRedissonHandler")
	private RedissonHandler redissonHandler;

	private static final Logger LOGGER = LoggerFactory.getLogger(CfVerifyBankCardCountUtil.class);

	public CfVerifyBankCardCountResult getBankCardVerifyCount(long userId) {
		int count = queryBankVerifyCountOfUser(userId);
		Date lastModifyTime = querylastBankVerifyTimeOfUser(userId);
		LOGGER.info("CrowdfundingDreamService queryLastBankVerifyTime in redis :{}", lastModifyTime);

		//当天没有操作过, 或者上次操作已经是昨天，则将计数器重置
		if(lastModifyTime == null || DateUtil.getCurrentDate().after(lastModifyTime)) {
			count = 0;
		}

		LOGGER.info("CrowdfundingDreamService queryBankVerifyCountOfUser in redis :{}", count);

		String strMaxTime = cfRedisKvBiz.queryByKey("MAX_BANK_VERIFY_TIME", true);
		LOGGER.info("CrowdfundingDreamService MAX_BANK_VERIFY_TIME in redis KV:{}", strMaxTime);
        int max = MAX_BANK_VERIFY_TIMES;
        if(StringUtils.isNumeric(strMaxTime)) {
            max = Integer.parseInt(strMaxTime);
        }

		max = max < 0 ? MAX_BANK_VERIFY_TIMES : max;
		return new CfVerifyBankCardCountResult(count, max);
	}

	public int queryBankVerifyCountOfUser(long userId) {
		String key = RedisKeyCons.CF_INFO_KEY_USER_BANK_VERIFY.replace("#userId#", userId + "");
		Integer count = redissonHandler.get(key, Integer.class);
		return (count == null ? 0 : count);
	}

	public Date querylastBankVerifyTimeOfUser(long userId) {
		String key = RedisKeyCons.CF_INFO_KEY_USER_BANK_VERIFY_LAST_MODIFED.replace("#userId#", userId + "");
		Date date = redissonHandler.get(key, Date.class);
		return date;
	}

	public void saveBankVerifyCountOfUser(long userId, int count) {
		String key = RedisKeyCons.CF_INFO_KEY_USER_BANK_VERIFY.replace("#userId#", userId + "");
		redissonHandler.setEX(key, count, RedisKeyCons.CF_INFO_KEY_USER_BANK_VERIFY_TIME);
		key = RedisKeyCons.CF_INFO_KEY_USER_BANK_VERIFY_LAST_MODIFED.replace("#userId#", userId + "");
		redissonHandler.setEX(key, DateUtil.nowDate(), RedisKeyCons.CF_INFO_KEY_USER_BANK_VERIFY_TIME);
	}

    public boolean deleteBankVerifyCountOfUser(long userId) {
        String key = RedisKeyCons.CF_INFO_KEY_USER_BANK_VERIFY.replace("#userId#", userId + "");
        return redissonHandler.del(key);
    }
}
