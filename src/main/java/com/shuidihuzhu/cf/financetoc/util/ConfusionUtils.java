package com.shuidihuzhu.cf.financetoc.util;

import lombok.extern.slf4j.Slf4j;

/**
 * 混淆工具
 *
 * <AUTHOR>
 * @date 2019/05/22
 */
@Slf4j
public class ConfusionUtils {

    private static int factor = 0x0ff8e7c6;

    /**
     * 默认订单不超过二进制的48个1
     */
    public static final long ORDER_MAX = 0xffffffffffffL;

    /**
     * 混淆一个整型数
     *
     * @param id
     * @return
     */
    public static int confuse(int id) {
        int cipher = id ^ factor;
        return ((cipher & 0xff) << 24) | ((cipher & 0xff00) << 8) |
                ((cipher & 0xff0000) >> 8) | (cipher >> 24);
    }



    /**
     * 方法：{@link #confuse(int)}&nbsp;的逆运算
     *
     * @param id
     * @return
     */
    public static int recover(int id) {
        int de = ((id << 24) | ((id << 8) & 0xff0000) | ((id >> 8) & 0xff00) | (id >> 24) & 0xff);
        return de ^ factor;
    }

    /**
     * 订单ID加密 默认ID不能超过 {@link ORDER_MAX}
     *
     * @param id -
     * @return -
     */
    public static long confuseOrderId(long id) {
        if (id > ORDER_MAX) {
            throw new RuntimeException("参数过大, 不能使用当前加密方式");
        }
        id = id & ORDER_MAX;

        long cipher = id ^ factor;
        return ((cipher & 0xffffL) << 32) | (cipher & 0xffff0000L) |  (cipher >> 32);
    }

    public static long recoverOrderId(long id) {
        id = id & ORDER_MAX;
        long de = (((id & 0xffffL) <<  32) | (id & 0xffff0000L) | (id >> 32) & 0xffffL);
        return de ^ factor;
    }
}
