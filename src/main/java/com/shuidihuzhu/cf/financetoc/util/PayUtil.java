package com.shuidihuzhu.cf.financetoc.util;

import com.shuidihuzhu.cf.app.constant.WxCons;
import com.shuidihuzhu.common.web.constants.PayShowCons;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/10/9.
 */
@Component
public class PayUtil {

	@Autowired
	private Environment environment;

	public String getPayCallBackUrl(String body) {
		String callbackUrl = WxCons.PAY_CALLBACK;
		if (body.startsWith(PayShowCons.PAY_CROWDFUNDING_BODY)) {
			callbackUrl = WxCons.PAY_CALLBACK_CROWDFUNDING;
		} else if (body.startsWith(PayShowCons.PAY_BODY_CLOCKIN)) {
			callbackUrl = WxCons.PAY_CALLBACK_CLOCKIN;
		} else if (body.startsWith(PayShowCons.PAY_BODY_CLOCKIN_NEWYEAR)) {
			callbackUrl = WxCons.PAY_CALLBACK_CLOCKIN;
		}

		return callbackUrl;
	}

	public String productName(String productName) {
		if (environment.acceptsProfiles("production")) {
			return productName;
		} else {
			return "test_" + productName;
		}
	}

	public double amount(double amount) {
	    return amount;
//		if (environment.acceptsProfiles("production")) {
//			return amount;
//		} else {
//			return 0.01;
//		}
	}

}
