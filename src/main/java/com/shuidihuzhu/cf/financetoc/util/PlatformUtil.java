package com.shuidihuzhu.cf.financetoc.util;

import com.shuidihuzhu.cf.enums.crowdfunding.CfInfoExtEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfPayOrigin;
import com.shuidihuzhu.client.baseservice.pay.enums.PayType;
import com.shuidihuzhu.common.web.enums.PayChannel;
import com.shuidihuzhu.common.web.enums.PayOrigin;
import com.shuidihuzhu.common.web.enums.Platform;

public class PlatformUtil {

	public static com.shuidihuzhu.client.baseservice.pay.enums.Platform transToPay(Platform platform) {
		switch (platform) {
			case WX:
				return com.shuidihuzhu.client.baseservice.pay.enums.Platform.WX;
			case H5:
				return com.shuidihuzhu.client.baseservice.pay.enums.Platform.H5;
			case ANDROID:
				return com.shuidihuzhu.client.baseservice.pay.enums.Platform.ANDROID;
			case IOS:
				return com.shuidihuzhu.client.baseservice.pay.enums.Platform.IOS;
			case WX_SP:
				return com.shuidihuzhu.client.baseservice.pay.enums.Platform.WX_SP;
			case KS_MINI_PROGRAM:
				return com.shuidihuzhu.client.baseservice.pay.enums.Platform.KS_MINI_PROGRAM;
			case DEFAULT:
			default:
				return com.shuidihuzhu.client.baseservice.pay.enums.Platform.DEFAULT;
		}
	}

	public static CfPayOrigin getCfPayOrigin(PayChannel payChannel, Platform platformEnum,
	                                         CfInfoExtEnum.PayType payType) {
		if (payChannel == null) {
			return selectCfWxPayOrigin(platformEnum, payType);
		}
		switch (payChannel) {
			case alipay:
			case balance:
				throw new IllegalArgumentException("爱心筹个人渠道捐款只支持微信支付");
			case weixin:
				return selectCfWxPayOrigin(platformEnum, payType);
		}

		return CfPayOrigin.ILLEGAL;
	}

	public static PayOrigin getPayOrigin(PayChannel payChannel, Platform platformEnum) {
		if (payChannel == null) {
			return selectPayOrigin(platformEnum);
		}
		switch (payChannel) {
			case alipay:
			case balance:
				throw new IllegalArgumentException("爱心筹个人渠道捐款只支持微信支付");
			case weixin: {
				switch (platformEnum) {
					case WX:
						return PayOrigin.NOWPAY_MPWX_CF;
					case H5:
						return PayOrigin.NOWPAY_WAPWX_CF;
					case WX_SP:
						return PayOrigin.WX_SP;
					default:
						break;
				}
			}
		}

		return PayOrigin.ILLEGAL;
	}

	public static CfPayOrigin selectCfWxPayOrigin(Platform platform, CfInfoExtEnum.PayType payType) {
		if (platform == null) {
			return CfPayOrigin.MPWX_CF;
		}
		switch (platform) {
			case WX:
			case ANDROID:
			case IOS:
				switch (payType) {
					case WX_HEALTH_SECURITY:
						return CfPayOrigin.HEALTH_SECURITY_MP_WX;
					case WX_CF_FOUNDATION:
					case WX_CF:
					default:
						return CfPayOrigin.MPWX_CF;
				}
			case H5:
				switch (payType) {
					case WX_HEALTH_SECURITY:
						return CfPayOrigin.HEALTH_SECURITY_WAP_WX;
					case WX_CF_FOUNDATION:
					case WX_CF:
					default:
						return CfPayOrigin.WAPWX_CF;
				}
			case WEI_BO:
				switch (payType) {
					case WX_HEALTH_SECURITY:
						return CfPayOrigin.HEALTH_SECURITY_WB_WX;
					case WX_CF_FOUNDATION:
					case WX_CF:
					default:
						return CfPayOrigin.WB_WX;
				}
			case TIE_BA:
				switch (payType) {
					case WX_HEALTH_SECURITY:
						return CfPayOrigin.HEALTH_SECURITY_TB_WX;
					case WX_CF_FOUNDATION:
					case WX_CF:
					default:
						return CfPayOrigin.TB_WX;
				}
			case QQ:
				switch (payType) {
					case WX_HEALTH_SECURITY:
						return CfPayOrigin.HEALTH_SECURITY_QQ_WX;
					case WX_CF_FOUNDATION:
					case WX_CF:
					default:
						return CfPayOrigin.QQ_WX;
				}
			case WX_SP:
				switch (payType) {
					case WX_HEALTH_SECURITY:
						return CfPayOrigin.HEALTH_SECURITY_SP_WX;
					case WX_CF_FOUNDATION:
					case WX_CF:
					default:
						return CfPayOrigin.WX_SP;
				}
			case KS_MINI_PROGRAM:
				return CfPayOrigin.KS_MINI_PROGRAM;
			default:
				break;
		}
		return CfPayOrigin.MPWX_CF;
	}

	public static PayOrigin selectPayOrigin(Platform platform) {
		if (platform == null) {
			return PayOrigin.NOWPAY_MPWX_CF;
		}
		switch (platform) {
			case H5:
				return PayOrigin.NOWPAY_WAPWX_CF;
			case WX_SP:
				return PayOrigin.WX_SP;
			case WX:
			case ANDROID:
			case IOS:
				return PayOrigin.NOWPAY_MPWX_CF;
			default:
				break;
		}
		return PayOrigin.ILLEGAL;
	}

	public static CfPayOrigin getCfPayOrigin(PayType payTypeChannel, Platform platformEnum,
			CfInfoExtEnum.PayType payType) {
		if (payTypeChannel == null) {
			return selectCfWxPayOrigin(platformEnum, payType);
		}
		if (PayType.isAlipay(payTypeChannel)) {
			return selectCfAlipayOrigin(platformEnum, payType);
		}
		if (PayType.isWeixin(payTypeChannel)) {
			return selectCfWxPayOrigin(platformEnum, payType);
		}
		if(PayType.isQPay(payTypeChannel)){
			//qq钱包支付
			return CfPayOrigin.QPAY_MINIAPP;
		}
		return selectCfWxPayOrigin(platformEnum, payType);
	}

	private static CfPayOrigin selectCfAlipayOrigin(Platform platform, CfInfoExtEnum.PayType payType) {
		if (platform == null) {
			return CfPayOrigin.HEALTH_SECURITY_WAP_ALI;
		}
		switch (platform) {
			case H5:
				switch (payType) {
					case WX_HEALTH_SECURITY:
						return CfPayOrigin.HEALTH_SECURITY_WAP_ALI;
					default:
						return CfPayOrigin.WAP_ALI;
				}
			case WEI_BO:
				switch (payType) {
					case WX_HEALTH_SECURITY:
						return CfPayOrigin.HEALTH_SECURITY_WB_ALI;
					default:
						return CfPayOrigin.WB_ALI;
				}
			case TIE_BA:
				switch (payType) {
					case WX_HEALTH_SECURITY:
						return CfPayOrigin.HEALTH_SECURITY_TB_ALI;
					default:
						return CfPayOrigin.TB_ALI;
				}
			case QQ:
				switch (payType) {
					case WX_HEALTH_SECURITY:
						return CfPayOrigin.HEALTH_SECURITY_QQ_ALI;
					default:
						return CfPayOrigin.QQ_ALI;
				}
			case WX_SP:
				switch (payType) {
					case WX_HEALTH_SECURITY:
						return CfPayOrigin.HEALTH_SECURITY_SP_WX;
					default:
						return CfPayOrigin.WX_SP;
				}
			case JIN_QING_BANG:
				return CfPayOrigin.HEALTH_SECURITY_WAP_ALI;
			default:
				break;
		}
		return CfPayOrigin.WAP_ALI;
	}

	public static PayOrigin getPayOrigin(PayType payTypeChannel, Platform platformEnum) {
		if (payTypeChannel == null) {
			return selectPayOrigin(platformEnum);
		}
		switch (payTypeChannel) {
			case ALIPAY_APP:
			case ALIPAY_WAP:
			case WX_APP:
			case WX_H5:
			case WX_JSSDK:
			case WX_SP:
				switch (platformEnum) {
					case WX:
						return PayOrigin.NOWPAY_MPWX_CF;
					case H5:
						return PayOrigin.NOWPAY_WAPWX_CF;
					case WX_SP:
						return PayOrigin.WX_SP;
					default:
						break;
				}
			default:
				return PayOrigin.ILLEGAL;
		}
	}

}
