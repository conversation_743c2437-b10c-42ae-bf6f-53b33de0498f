package com.shuidihuzhu.cf.financetoc.util;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.constants.PayShowCons;
import com.shuidihuzhu.cf.enums.crowdfunding.CfVersion;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingType;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Slf4j
public class CrowdfundingUtil {

    private static Date OLD_CASE_WITHOUT_PROPERTY =new DateTime().withYear(2018).withMonthOfYear(8).withDayOfMonth(1).toDate();

    public static String getProductName(CrowdfundingInfo crowdfundingInfo) {
        String bodyPrefix = PayShowCons.PAY_SERIOUS_ILLNESS_BODY_PREFIX;
        CrowdfundingType crowdfundingType = CrowdfundingType.fromValue(crowdfundingInfo.getType());
        if (crowdfundingType != null && crowdfundingType != CrowdfundingType.SERIOUS_ILLNESS) {
            bodyPrefix = PayShowCons.PAY_DREAM_BODY_PREFIX;
        }
        try {
            String infoUuid = crowdfundingInfo.getInfoId();
            Date createTime = crowdfundingInfo.getCreateTime();
            if (createTime == null) {
                createTime = new Date();
            }
            return bodyPrefix + "_" + infoUuid.split("-")[0] + "_" + createTime.getTime();
        } catch (Exception e) {
            log.error("", e);
            return bodyPrefix + "_" + crowdfundingInfo.getId();
        }
    }

    /**
     * 如果cf_info_ext中有明确规定，那么以cf_info_ext中的表述为准
     * <p>
     * 如果是老案例，返回4项材料（含图文）
     * 如果是新案例，返回5项材料（含图文）
     *
     * @param cfInfoExt
     * @return
     */
    public static List<CrowdfundingInfoDataStatusTypeEnum> getRequiredCaseList(CfInfoExt cfInfoExt) {

        if (cfInfoExt == null) {
            return Lists.newArrayList();
        }
        //如果是明确指定了需要验证哪几项材料，以cf_info_ext中的为准
//        if (!StringUtils.equals(cfInfoExt.getNeedCaseList(), CfInfoExt.DEFAULT_APPROVE_CASE_CODE_LIST)) {
//            return CrowdfundingInfoDataStatusTypeEnum.parseCaseEnumByString(cfInfoExt.getNeedCaseList());
//        }
//
//        return Lists.newArrayList(
//                CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT,
//                CrowdfundingInfoDataStatusTypeEnum.PATIENT_INFO_SUBMIT,
//                CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT,
//                CrowdfundingInfoDataStatusTypeEnum.TREATMENT_INFO_SUBMIT,
//                CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT);

        return CfVersion.getRequiredCaseList(cfInfoExt.getNeedCaseList());
    }

    /**
     * 判断是否旧就案例
     *
     * @param cfInfoExt
     * @return
     * @deprecated 逐渐改为private static 方法，不再在新代码中使用（曾鋆 20180815）
     */
    @Deprecated
    public static boolean decideIsOldCrowdFunding(CfInfoExt cfInfoExt) {
        //对新旧案例的区分
        if (null == cfInfoExt || cfInfoExt.getDateCreated() == null) {
            log.info("案例为空");
            return false;
        }
        // fix 要加时间判断
        if (cfInfoExt.getDateCreated().before(OLD_CASE_WITHOUT_PROPERTY) && "1,2,3,4".equals(cfInfoExt.getNeedCaseList())) {
            //旧案例,主要是旧版APP的问题
            return true;
        }
        return false;
    }

    /**
     * 获取手机号掩码 eg:18201534934 => 182****4934
     *
     * @param phoneNumber
     * @return
     */
    public static String getTelephoneMask(String phoneNumber) {
        return StringUtils.isNotBlank(phoneNumber) ?
                phoneNumber.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2") :
                phoneNumber;
    }

    public static String getNameMask(String patientName) {
        if (StringUtils.isBlank(patientName) || patientName.length() <= 1) {
            return patientName;
        }

        return patientName.replaceAll("([\\d\\D]{1})(.*)", "$1" + createAsterisk(patientName.length() - 1));
    }

    /**
     * 案例是否结束
     * @param crowdfundingInfo
     * @return
     */
    public static Optional<Boolean> hasEnd(CrowdfundingInfo crowdfundingInfo) {
        return Optional.ofNullable(crowdfundingInfo)
                .map(CrowdfundingInfo::getEndTime)
                .map(a -> new Date().after(a));
    }

    /**
     * 案例是否已结束 默认已结束
     * @param crowdfundingInfo
     * @return
     */
    public static Boolean hasEnd(CrowdfundingInfo crowdfundingInfo, Boolean def) {
        return hasEnd(crowdfundingInfo).orElse(def);
    }

    private static String createAsterisk(int length) {
        StringBuffer stringBuffer = new StringBuffer();
        for (int i = 0; i < length; i++) {
            stringBuffer.append("*");
        }
        return stringBuffer.toString();
    }

    /**
     * 构造平安托管会员交易网代码
     *
     * @param productName 筹款案例商品名
     * @return
     */
    public static String buildPingAnCode(String productName) {
        if (StringUtils.isBlank(productName)) {
            return "";
        }
        try {
            return StringUtils.substringAfter(productName, "_");
        } catch (Exception e) {
            log.error("构造会员交易网代码异常productName:{},",productName, e);
        }
        return "";
    }

}
