package com.shuidihuzhu.cf.financetoc.util;

import com.shuidihuzhu.cf.activity.enums.DonateChannelEnum;

/**
 * <AUTHOR>
 */
public class SubsidyUtils {

    /**
     * 根据shareDv 计算channel
     * @param shareDv
     * @return
     */
    public static DonateChannelEnum parseChannel(int shareDv){
        if (shareDv < 0 || shareDv >= 1000) {
            return DonateChannelEnum.PUSH;
        }
        return DonateChannelEnum.SHARE;
    }
}
