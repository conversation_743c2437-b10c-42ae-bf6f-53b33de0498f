package com.shuidihuzhu.cf.financetoc.util;

import com.google.common.base.Preconditions;
import com.shuidihuzhu.common.web.model.IdcardInfoExtractor;
import com.shuidihuzhu.common.web.util.IdCardUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * @Author: duchao
 * @Date: 2018/8/15 下午2:44
 */
@Slf4j
public class CfIdCardUtil {

	/**
	 * 合法的身份证长度（水滴全系统不支持15位身份证）
	 */
	private final static int VALID_ID_CARD_LENGTH = 18;
	private final static int VALID_ID_CARD_LENGTH_15 = 15;

	/**
	 * 身份证的正则表达式（水滴全系统不支持15位身份证）
	 */
	private final static String VALID_ID_CARD_REG = "^\\d{17}[0-9Xx]$";
	private final static String VALID_ID_CARD_REG_15 = "^\\d{15}$";


	/**
	 * 校验身份证格式是否符合水滴的要求
	 * 水滴全系统不支持15位身份证
	 *
	 * @param idCard
	 * @return
	 */
	public static boolean isValidIdCard(String idCard){
	    //如果为空说明有问题
		if(StringUtils.isBlank(idCard)){
			return false;
		}
		//隐藏字符过滤
		idCard = filiterHideCode(idCard);

		if(idCard.length() != VALID_ID_CARD_LENGTH && idCard.length() != VALID_ID_CARD_LENGTH_15){
			return false;
		}

		if(!idCard.matches(VALID_ID_CARD_REG) && !idCard.matches(VALID_ID_CARD_REG_15)){
			return false;
		}

		return true;
	}

	public static int getAge(String idCard) {
		//隐藏字符过滤
		idCard = filiterHideCode(idCard);
		try {
			String year = new SimpleDateFormat("yyyy").format(new Date());
			if (idCard.length() == 18) {
				return Integer.parseInt(year) - Integer.parseInt(idCard.substring(6, 10));
			} else if (idCard.length() == 15) {
				return Integer.parseInt(year) - Integer.parseInt("19" + idCard.substring(6, 8));
			}
		} catch (Exception e) {
			log.error("身份证获取年龄失败", e);
		}
		return 0;
	}

	public static String getGender(String idCard) {
		String gender = "TA";
		if (StringUtils.isNotEmpty(idCard)) {
			IdcardInfoExtractor idcardInfoExtractor = new IdcardInfoExtractor(idCard);
			if (!IdCardUtil.illegal(idCard)) {
				gender = "男".equals(idcardInfoExtractor.getGender()) ? "他" : "她";
			}
		}
		return gender;
	}


	public static boolean containSpaceNumberCharacter(String content){
		Preconditions.checkNotNull(content);
		boolean result = true;

		for(char c : content.toCharArray()){
			//空格 https://baike.baidu.com/item/ASCII/309296?fr=aladdin
			if(c == 0x20){
				result = false;
				break;
			}

			//数字
			if(c >=0x30 && c<= 0x39){
				result = false;
				break;
			}

			//大写字母A-Z
			if(c >= 0x41 && c <= 0x5A){
				result = false;
				break;
			}

			//小写字母a-z
			if(c >= 0x61 && c <= 0x7A){
				result = false;
				break;
			}
		}

		return result;
	}

	public static boolean containSpaceNumber(String content){
		Preconditions.checkNotNull(content);
		boolean result = true;

		for(char c : content.toCharArray()){
			//空格 https://baike.baidu.com/item/ASCII/309296?fr=aladdin
			if(c == 0x20){
				result = false;
				break;
			}

			//数字
			if(c >=0x30 && c<= 0x39){
				result = false;
				break;
			}
		}

		return result;
	}

	public static int getCurrentAge(String idCard) {
		idCard = filiterHideCode(idCard);
		int age = 0;
		try {
			// 当前时间
			Calendar curr = Calendar.getInstance();
			// 生日
			Calendar born = Calendar.getInstance();
			if (idCard.length() == VALID_ID_CARD_LENGTH) {
				born.setTime(DateUtils.parseDate(idCard.substring(6, 14), "yyyyMMdd"));
			}else {
				born.setTime(DateUtils.parseDate("19" + idCard.substring(6, 12), "yyyyMMdd"));
			}
			// 年龄 = 当前年 - 出生年
			age = curr.get(Calendar.YEAR) - born.get(Calendar.YEAR);
			if (age <= 0) {
				return 0;
			}
			// 如果当前月份小于出生月份: age-1
			// 如果当前月份等于出生月份, 且当前日小于出生日: age-1
			int currMonth = curr.get(Calendar.MONTH);
			int currDay = curr.get(Calendar.DAY_OF_MONTH);
			int bornMonth = born.get(Calendar.MONTH);
			int bornDay = born.get(Calendar.DAY_OF_MONTH);
			if ((currMonth < bornMonth) || (currMonth == bornMonth && currDay <= bornDay)) {
				age--;
			}
		} catch (Exception e) {
			log.info("身份证获取周岁失败");
		}
		return age < 0 ? 0 : age;
	}

	public static String filiterHideCode(String idCard) {
		return idCard.replaceAll("\\p{C}", "").trim();
	}

	public static void main(String[] args) {
//		System.out.println(isValidIdCard("4115a1198612053959"));
//		System.out.println(isValidIdCard("6321231982-9270517"));
//		System.out.println(isValidIdCard("36082219921102645x"));
//		System.out.println(isValidIdCard("3608221988077265"));
//		System.out.println(isValidIdCard("36082219820707136a"));
//		System.out.println(isValidIdCard("36082219850204022"));
//		System.out.println(isValidIdCard("110114201703190528"));
//		String idCard = "\u202d1101142";
//		idCard = filiterHideCode(idCard);
//		System.out.println("====");
//		System.out.println(isValidIdCard("511027540816738"));
//		System.out.println(getAge("511027540816738"));
//		System.out.println(getCurrentAge("511027540816738"));
//		System.out.println(flipALlChar("2313sd324G1dd44"));

	}

	// 翻转身份证的最后一位大小写  比如 1323X-->1323x   1323x-->1323X
	public static String flipLastChar(String idCard) {

		if (StringUtils.isBlank(idCard)) {
			return idCard;
		}

		int lastIndex = idCard.length() - 1;
		char lastChar = idCard.charAt(lastIndex);

		if (Character.isDigit(lastChar)) {
			return idCard;
		}

		return idCard.substring(0, lastIndex) + (Character.isUpperCase(lastChar)
				? Character.toLowerCase(lastChar) : Character.toUpperCase(lastChar));
	}

	// 将身份证的最后一位转为大写  比如 1323x-->1323X   1323-->1323
	public static String convertLastCharUpper(String idCard) {

		if (StringUtils.isBlank(idCard)) {
			return idCard;
		}

		int lastIndex = idCard.length() - 1;
		char lastChar = idCard.charAt(lastIndex);

		if (Character.isDigit(lastChar) || Character.isUpperCase(lastChar)) {
			return idCard;
		}

		return idCard.substring(0, lastIndex) + Character.toUpperCase(lastChar);
	}


	public static String maskLastCountChar(String idCard, int lastCount) {

		if (StringUtils.isBlank(idCard)) {
			return idCard;
		}

		if (lastCount >= idCard.length()) {
			return "*".repeat(idCard.length());
		}

		return idCard.substring(0, idCard.length() - lastCount) + "*".repeat(lastCount);
	}

	public static String maskFirstCountChar(String idCard, int firstCount) {

		if (StringUtils.isBlank(idCard)) {
			return idCard;
		}

		if (firstCount >= idCard.length()) {
			return "*".repeat(idCard.length());
		}

		return  "*".repeat(firstCount) +  idCard.substring(firstCount);
	}

}
