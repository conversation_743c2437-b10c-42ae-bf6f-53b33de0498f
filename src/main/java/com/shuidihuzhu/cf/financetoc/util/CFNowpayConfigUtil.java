package com.shuidihuzhu.cf.financetoc.util;

import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Component
@RefreshScope
public class CFNowpayConfigUtil {

	private static final String SVC_DOMAIN = "http://cf-finance-toc-api.cf.svc";


	public String getPayServiceCallBackUrl() {
		return SVC_DOMAIN + "/api/cf/pay/callback";
	}

	public String getCombinePayCallBackUrl() {
		return  SVC_DOMAIN + "/api/cf/pay/combine/callback";
	}

	public String getContributePayCallBackUrl(){
		return  SVC_DOMAIN + "/api/cf/pay/contribute/callback";
	}
}
