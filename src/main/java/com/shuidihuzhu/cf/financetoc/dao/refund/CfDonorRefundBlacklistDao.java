package com.shuidihuzhu.cf.financetoc.dao.refund;

import com.shuidihuzhu.cf.model.crowdfunding.pay.CfDonorRefundBlacklist;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

/**
 * @Author: l<PERSON><PERSON><PERSON>chao
 * @Date: 2019/3/18 16:41
 */
@DataSource(DS.CF_SLAVE)
public interface CfDonorRefundBlacklistDao {
    CfDonorRefundBlacklist getByUserId(long userId);
}
