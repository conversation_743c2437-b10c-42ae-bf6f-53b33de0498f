package com.shuidihuzhu.cf.financetoc.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;
import java.util.Set;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/10/18.
 */
@DataSource(DS.CF)
public interface CrowdFundingVerificationDao {

	int saveCrowdFundingVerification (CrowdFundingVerification crowdFundingVerification);

	int updateCrowdFundingVerification(@Param("verifyId") int verifyId,
	                                    @Param("crowdFundingInfoId") String crowdFundingInfoId, @Param("oldValid") int oldValid, @Param("newValid") int newValid);

	int updateCrowdFundingVerificationDescription(@Param("verifyId") int verifyId, @Param("description") String description);

	@DataSource(DS.CF_SLAVE)
	List<CrowdFundingVerification> queryCrowdFundingVerificationByInfoUuid(@Param("crowdFundingInfoId") String crowdFundingInfoId, @Param("limit") int limit);

	@DataSource(DS.CF_SLAVE)
	List<CrowdFundingVerification> queryAllCrowdFundingVerificationByInfoUuid(@Param("crowdFundingInfoId") String crowdFundingInfoId);

	@DataSource(DS.CF_SLAVE)
	Integer countCrowdFundingVerificationByInfoUuidAndVerifyUserId(@Param("verifyUserId") long verifyUserId, @Param("crowdFundingInfoId") String crowdFundingInfoId);

	@DataSource(DS.CF_SLAVE)
	Integer countCrowdFundingVerificationByInfoUuid(@Param("crowdFundingInfoId") String crowdFundingInfoId);

	@DataSource(DS.CF_SLAVE)
	Integer countCrowdFundingVerificationByInfoUuidAndOpenId(@Param("openId") String openId,
															 @Param("crowdFundingInfoId") String crowdFundingInfoId);

	@DataSource(DS.CF_SLAVE)
	Integer countCrowdFundingVerificationByVerifyUserId(@Param("verifyUserId") long verifyUserId, @Param("crowdFundingInfoId") String crowdFundingInfoId);

	@DataSource(DS.CF_SLAVE)
	Integer countCrowdFundingVerificationByVerifyUserIdNotWithValid(@Param("verifyUserId") long verifyUserId, @Param("crowdFundingInfoId") String crowdFundingInfoId);

	@DataSource(DS.CF_SLAVE)
	List<CrowdFundingVerification> getListByAnchorId(@Param("crowdFundingInfoId") String crowdFundingInfoId,
	                                                 @Param("anchorId") int anchorId, @Param("limit") int limit);
	/**
	 * 获取指定时间区间内的证实
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	@DataSource(DS.CF_SLAVE)
	List<CrowdFundingVerification> getListBetween(@Param("startTime") Timestamp startTime,
	                                              @Param("endTime") Timestamp endTime,
	                                              @Param("limitStart") int limitStart,
	                                              @Param("limitSize") int limitSize);

	@DataSource(DS.CF_SLAVE)
	List<CrowdFundingVerification> getListByRelationships(@Param("crowdfundingInfoId") String infoUuid,
	                                                      @Param("anchorId") int anchorId,
	                                                      @Param("limit") int limit,
	                                                      @Param("relationships") List<Integer> relationships);
	@DataSource(DS.CF_SLAVE)
	List<CrowdFundingVerification> getByRelationships(@Param("crowdfundingInfoId") String infoUuid,
	                                                      @Param("limit") int limit,
	                                                      @Param("relationships") List<Integer> relationships);
	@DataSource(DS.CF_SLAVE)
	List<CrowdFundingVerification> getFriendsVerification(@Param("crowdfundingInfoId") String infoUuid,
	                                                      @Param("friends") Set<Long> friends,
	                                                      @Param("limit") int limit);

	@DataSource(DS.CF_SLAVE)
	List<CrowdFundingVerification> getFriendsVerificationAll(@Param("crowdfundingInfoId") String infoUuid,
														  @Param("friends") Set<Long> friends);

	@DataSource(DS.CF_SLAVE)
	List<CrowdFundingVerification> getFriendsVerificationByTime(@Param("crowdfundingInfoId") String infoUuid,
															 	@Param("friends") Set<Long> friends,
																@Param("startTime") Timestamp startTime,
																@Param("endTime") Timestamp endTime);

	/**
	 * 查指定关系类型的证实的数目
	 * @param infoUuid
	 * @param relationships
	 * @return
	 */
	@DataSource(DS.CF_SLAVE)
	Integer countVerificationByInfoUuidAndRelationShip(@Param("crowdfundingInfoId") String infoUuid,
													   @Param("relationships") List<Integer> relationships);

	@DataSource(DS.CF_SLAVE)
	List<CrowdFundingVerification> getByVerifyUserIds(@Param("verifyUserids") List<Long> verifyUserIds,
	                                                  @Param("infoUuid") String infoUuid);

	@DataSource(DS.CF_SLAVE)
	List<CrowdFundingVerification> listByVerifyUserId(@Param("verifyUserId") long verifyUserId, @Param("anchorId") int anchorId, @Param("limit") int limit);

	@DataSource(DS.CF_SLAVE)
	List<CrowdFundingVerification> queryByVerifyUserId(@Param("verifyUserId") long verifyUserId);

	@DataSource(DS.CF_SLAVE)
	List<CrowdFundingVerification> getByOffset(@Param("offset") int offset,
	                                           @Param("limit") int limit);

	/**
	 * 查询指定用户针对所有case的证实
	 * @param verifyUserId
	 * @param infoUuids
	 * @return
	 */
	@DataSource(DS.CF_SLAVE)
	List<CrowdFundingVerification> getListByVerifyUserIdAndInfoIds(@Param("verifyUserId") long verifyUserId,
	                                                               @Param("infoUuids") List<String> infoUuids,
	                                                               @Param("limit") int limit);

	int updateUserName(@Param("verifyUserId") long verifyUserId, @Param("userName") String userName);

	@DataSource(DS.CF_SLAVE)
	int countVerifyCfByUserId(@Param("verifyUserId") long verifyUserId);

	@DataSource(DS.CF_SLAVE)
	CrowdFundingVerification getLastSuccessOne(@Param("verifyUserId") long verifyUserId);

	@DataSource(DS.CF_SLAVE)
	CrowdFundingVerification getById(@Param("id") Integer id);

    int updateValid(@Param("valid") int valid, @Param("id") int id);

	@DataSource(DS.CF_SLAVE)
	CrowdFundingVerification findByVerifyUserIdAndInfoId(@Param("verifyUserId") long verifyUserId,
	                                                     @Param("crowdfundingInfoId") String infoUuid);

	@DataSource(DS.CF_SLAVE)
	List<Long> getVerifyUserIdsByInfoIds(@Param("infoIds") List<String> infoIds);

	@DataSource(DS.CF_SLAVE)
	List<CrowdFundingVerification> getByVerifyUserIdAndInfoUuid(@Param("verifyUserId") long verifyUserId,
													  @Param("infoUuid") String infoUuid);

	@DataSource(DS.CF_SLAVE)
	CrowdFundingVerification getVerifyUserIdById(@Param("id") long id);


	@DataSource(DS.CF_SLAVE)
	int queryByVerifyUserIdAndInfoId(@Param("verifyUserId") long verifyUserId,
														 @Param("crowdfundingInfoId") String infoUuid);
	List<CrowdFundingVerification> getVerifyByIds(@Param("ids") List<Long> ids);

	@DataSource(DS.CF_SLAVE)
	CrowdFundingVerification getByUserIdAndInfoUuid(@Param("verifyUserId") long verifyUserId,
													@Param("infoUuid") String infoUuid);

	@DataSource(DS.CF_SLAVE)
	int getVerfiyNumToday(@Param("infoUuids") List<String> infoIds,
						  @Param("time") String time);


	@DataSource(DS.CF_SLAVE)
	List<CrowdFundingVerification> getListByPatientUserIdAndAnchorId(@Param("patientUserId") long patientUserId, @Param("crowdFundingInfoId") String crowdFundingInfoId,
													 @Param("anchorId") int anchorId, @Param("limit") int limit);


	int updateVerifyUserId(@Param("id") long id,
							 @Param("verifyUserId") long verifyUserId);

	@DataSource(DS.CF_SLAVE)
	int countCrowdFundingVerificationByVerifyUserIdAndTime(@Param("startTime") Timestamp startTime,
														   @Param("endTime") Timestamp endTime,
														   @Param("verifyUserId") long verifyUserId);

	@DataSource(DS.CF_SLAVE)
	List<CrowdFundingVerification> getVerificationList(@Param("verificationIdList") List<Long> verificationIdList);

	@DataSource(DS.CF_SLAVE)
	CrowdFundingVerification selectById(@Param("id") Long id);

}
