package com.shuidihuzhu.cf.financetoc.dao.order;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.dto.OrderSearchDto;
import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.crowdfunding.CaseDonationStatisticsModel;
import com.shuidihuzhu.cf.model.crowdfunding.CfUserOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrderCount;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfCountSumVo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: wanghui
 * @time: 2019/3/22 5:50 PM
 * @description: 以crowdfundingid分表的 dao
 */

@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_NEW_SLAVE)
public interface CrowdfundingOrderShardingCrowdfundingIdDao {


	List<CrowdfundingOrder> getByUserId(@Param("userIds") Set<Long> userIds,
										@Param("crowdfundingId") Long crowdfundingId);

	CrowdfundingOrder getById(@Param("crowdfundingId") long crowdfundingId, @Param("id") Long id);

	@DataSource(CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
	List<CrowdfundingOrder> getAllPayByCrowdfundingIdsAndUserIdsWithSuffixTableName(@Param("sharding") String sharding, @Param("ids") List<Long> ids);
}
