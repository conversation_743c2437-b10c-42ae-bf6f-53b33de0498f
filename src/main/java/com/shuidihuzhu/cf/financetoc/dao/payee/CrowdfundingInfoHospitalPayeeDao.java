package com.shuidihuzhu.cf.financetoc.dao.payee;

import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoHospitalPayee;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/9/26.
 */
@DataSource(DS.CF)
public interface CrowdfundingInfoHospitalPayeeDao {

    //保存
    int save(CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee);

    //更新
    int update(CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee);

    //根据infoUuid查找
    @DataSource(DS.CF_SLAVE)
    CrowdfundingInfoHospitalPayee getByInfoUuid(@Param("infoUuid") String infoUuid);

}
