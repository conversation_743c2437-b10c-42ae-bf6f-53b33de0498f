package com.shuidihuzhu.cf.financetoc.dao.refund;

import com.shuidihuzhu.cf.model.crowdfunding.pay.CfDonorRefundRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

@DataSource(DS.CF)
public interface CfDonorRefundRecordDao {

    int add(CfDonorRefundRecord cfDonorRefundRecord);

    @DataSource(DS.CF_SLAVE)
    CfDonorRefundRecord getByUserId(long usrId);
}
