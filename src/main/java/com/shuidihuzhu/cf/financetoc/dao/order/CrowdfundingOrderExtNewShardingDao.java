package com.shuidihuzhu.cf.financetoc.dao.order;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrderExt;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * 订单扩展分表
 *
 * <AUTHOR>
 * @since 2023-03-02 7:57 下午
 **/
@DataSource(CfDataSource.CF_ORDER_NEW_MASTER_DATASOURCE)
public interface CrowdfundingOrderExtNewShardingDao {

    /**
     * add
     *
     * @param crowdfundingOrderExt -
     * @return -
     */
    int add(@Param("crowdfundingOrderExt") CrowdfundingOrderExt crowdfundingOrderExt, @Param("sharding") String sharding);

    /**
     * getByOrderId
     *
     * @param orderId -
     * @param sharding -
     * @return -
     */
    @DataSource(CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
    CrowdfundingOrderExt getByOrderId(@Param("orderId") long orderId, @Param("sharding") String sharding);
}
