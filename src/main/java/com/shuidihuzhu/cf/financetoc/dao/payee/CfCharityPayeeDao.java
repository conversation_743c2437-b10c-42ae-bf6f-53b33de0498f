package com.shuidihuzhu.cf.financetoc.dao.payee;

import com.shuidihuzhu.cf.model.crowdfunding.CfCharityPayee;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @DATE 2018/11/15
 */
@DataSource(DS.CF)
public interface CfCharityPayeeDao {

    @DataSource(DS.CF_SLAVE)
    CfCharityPayee getByCaseUUid(@Param("uuid") String uuid);

    int insert(CfCharityPayee cfCharityPayee);

    int update(CfCharityPayee cfCharityPayee);
}
