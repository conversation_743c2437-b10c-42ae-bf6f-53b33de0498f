package com.shuidihuzhu.cf.financetoc.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfInfoBlessing;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface CfInfoBlessingDao {

	int insert(CfInfoBlessing record);
	@DataSource(DS.CF_SLAVE)
	CfInfoBlessing selectByUserId(@Param("userId")long userId, @Param("infoUuid") String infoUuid);
	@DataSource(DS.CF_SLAVE)

	CfInfoBlessing selectBySelfTag(@Param("selfTag") String selfTag, @Param("infoUuid") String infoUuid);

	int blessingByUserId(@Param("userId")long userId, @Param("infoUuid") String infoUuid);

	int blessingBySelfTag(@Param("selfTag") String selfTag, @Param("infoUuid") String infoUuid);

	int unBlessingByUserId(@Param("userId")long userId, @Param("infoUuid") String infoUuid);

	int unBlessingBySelfTag(@Param("selfTag") String selfTag, @Param("infoUuid") String infoUuid);

	@DataSource(DS.CF_SLAVE)
	List<CfInfoBlessing> selectByInfoUuid(@Param("infoUuid") String infoUuid, @Param("anchorId") int anchorId, @Param("limit") int limit);

	@DataSource(DS.CF_SLAVE)
	List<CfInfoBlessing> selectBlessByUserId(@Param("userId") long userId);

	int updateUserIdByIds(@Param("ids") List<Integer> ids, @Param("userId") long toUserId);
}
