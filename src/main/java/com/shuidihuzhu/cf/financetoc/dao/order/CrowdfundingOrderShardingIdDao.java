package com.shuidihuzhu.cf.financetoc.dao.order;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: wanghui
 * @time: 2019/3/22 5:45 PM
 * @description:  以id 分表的dao
 */

@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_MASTER)
public interface CrowdfundingOrderShardingIdDao {

	int addCrowdfundingOrderShardingModel(CrowdfundingOrder order);

	@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_SLAVE)
	Long getCrowdfundingIdById(@Param("id") Long id);
}
