package com.shuidihuzhu.cf.financetoc.dao.order;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.contribute.CfCombineOrderManage;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(CfDataSource.CF_ORDER_NEW_MASTER_DATASOURCE)
public interface CfCombineOrderManageDao {

    int addCombineOrderList(@Param("manageList") List<CfCombineOrderManage> combineList);

    int updatePayStatus(@Param("parentThirdUid") String parentThirdUid,
                        @Param("subThirdPayUid") String subThirdPayUid,
                        @Param("subPayUid") String subPayUid);

    List<CfCombineOrderManage> selectByParentUids(@Param("parentPayUids") List<String> parentPayUids);

    @DataSource(CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
    CfCombineOrderManage getByParentThirdPayUidAndOrderType(@Param("parentThirdPayUid") String parentThirdPayUid, @Param("orderType") int orderType);

    CfCombineOrderManage getByOrderId(@Param("orderId") long orderId);
}
