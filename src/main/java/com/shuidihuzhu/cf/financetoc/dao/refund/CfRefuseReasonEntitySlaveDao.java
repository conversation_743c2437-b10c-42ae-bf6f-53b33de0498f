package com.shuidihuzhu.cf.financetoc.dao.refund;

import com.shuidihuzhu.cf.model.crowdfunding.casematerial.CfRefuseReasonEntitySlave;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@DataSource(DS.CF_SLAVE)
public interface CfRefuseReasonEntitySlaveDao {
    List<CfRefuseReasonEntitySlave> selectByReasonIds(@Param("set") Set set);
}
