package com.shuidihuzhu.cf.financetoc.dao.order;

import com.shuidihuzhu.cf.model.goods.GoodsOrderExt;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(DS.CF)
public interface GoodsOrderExtDao {

	int save(GoodsOrderExt goodsOrderExt);

	@DataSource(DS.CF_SLAVE)
	GoodsOrderExt get(@Param("orderId") long orderId);

	int updateSuccessPayStatus(@Param("orderId") long orderId);

}
