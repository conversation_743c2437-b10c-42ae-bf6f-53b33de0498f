package com.shuidihuzhu.cf.financetoc.dao.order;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.CfBlessingCardOrder;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/25  10:59 上午
 */
@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_NEW_MASTER)
public interface CfBlessingCardOrderDao {
    int insert(@Param("orderId") long orderId, @Param("blessingCardId") int blessingCardId,@Param("anonymousValid") int anonymousValid);
}
