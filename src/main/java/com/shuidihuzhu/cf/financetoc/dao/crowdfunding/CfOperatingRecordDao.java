package com.shuidihuzhu.cf.financetoc.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(DS.CF)
public interface CfOperatingRecordDao {
	int save(CfOperatingRecord cfOperatingRecord);

	int updateResion(@Param("id") long id,@Param("financialComment") String financialComment);
}
