package com.shuidihuzhu.cf.financetoc.dao.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by wuxin<PERSON> on 6/21/16.
 */
@DataSource(DS.CF)
public interface CrowdfundingAttachmentDao {

	@DataSource(DS.CF_SLAVE)
	List<CrowdfundingAttachment> getFundingAttachment(int parentId);

	int add(List<CrowdfundingAttachment> crowdfundingAttachmentList);

	int deleteByParentIdAndType(@Param("parentId") int parentId, @Param("typeList") List<AttachmentTypeEnum> typeList);
}
