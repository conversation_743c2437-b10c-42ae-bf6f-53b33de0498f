package com.shuidihuzhu.cf.financetoc.dao.order;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @author: wanghui
 * @time: 2019/3/22 5:50 PM
 * @description: 以crowdfundingid分表的 dao
 */

@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_NEW_MASTER)
public interface CrowdfundingOrderShardingCrowdfundingIdNewDao {

    int addOrder(CrowdfundingOrder order);

    int updatePayStatus(@Param("crowdfundingId") long crowdfundingId, @Param("id") long id, @Param("payStatus") int payStatus, @Param("payTime") Date payTime);

    List<CrowdfundingOrder> getListByUserId(@Param("crowdfundingId") long crowdfundingId, @Param("userId") long userId,
                                            @Param("limit") int limit);

    void updateUserId(@Param("order") CrowdfundingOrder order);

}
