package com.shuidihuzhu.cf.financetoc.dao.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.BankCardVerifyStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


/**
 * Created by wuxinlong on 6/21/16.
 */

@DataSource(DS.CF)
public interface CrowdfundingInfoDao {

    int updatePayeeInfo(CrowdfundingInfo crowdfundingInfo);

    int updateRelationType(CrowdfundingInfo crowdfundingInfo);

    CrowdfundingInfo getFundingInfo(String infoId);

    CrowdfundingInfo getFundingInfoById(int id);

    int addAmount(@Param("id") int id, @Param("amount") int amount);

    int updateEndTime(@Param("id") int id, @Param("endTime") Date endTime);

    int updateVerifyStatus(@Param("id") int id,
                           @Param("verifyStatus") BankCardVerifyStatus verifyStatus,
                           @Param("bankCardVerifyMessage") String bankCardVerifyMessage,
                           @Param("bankCardVerifyMessage2") String bankCardVerifyMessage2);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingInfo> selectByUserId(@Param("userId") long userId);

    int updateUserId(CrowdfundingInfo crowdfundingInfo);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingInfo> getByUserId(long userId);

}
