package com.shuidihuzhu.cf.financetoc.dao.order;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecordShardingModel;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;


/**
 * @author: wanghui
 * @time: 2019/3/12 8:56 PM
 * @description:  以payuid分表的dao
 */

@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_MASTER)
public interface CrowdfundingPayRecordShardingPayUidDao {

	int addPayRecord(CrowdfundingPayRecord payRecord);

	@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_SLAVE)
	CrowdfundingPayRecordShardingModel getPayRecordShardingModelByPayUid(@Param("payUid") String payUid);
}
