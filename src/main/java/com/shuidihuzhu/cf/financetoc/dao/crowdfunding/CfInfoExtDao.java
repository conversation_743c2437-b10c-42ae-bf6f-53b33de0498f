package com.shuidihuzhu.cf.financetoc.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@DataSource(DS.CF)
public interface CfInfoExtDao {

    @DataSource(DS.CF_SLAVE)
    CfInfoExt getByInfoUuid(@Param("infoUuid") String infoUuid);

    @DataSource(DS.CF_SLAVE)
    List<CfInfoExt> getByCaseIds(@Param("list") Collection<Integer> caseIds);

    int updateFinishStatus(@Param("infoUuid") String infoUuid, @Param("finishStatus") int finishStatus);

}
