package com.shuidihuzhu.cf.financetoc.dao;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 缙云打标记录
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/3/18 3:40 下午
 */
@DataSource(DS.CF)
public interface JinYunMarkRecordDao {
    int updateFinishStatus(@Param("finishStatus") int finishStatus, @Param("caseId") int caseId);
}
