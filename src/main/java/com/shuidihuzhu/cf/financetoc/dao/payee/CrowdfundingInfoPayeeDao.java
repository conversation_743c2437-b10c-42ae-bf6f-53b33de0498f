package com.shuidihuzhu.cf.financetoc.dao.payee;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface CrowdfundingInfoPayeeDao {

	int add(CrowdfundingInfoPayee crowdfundingInfoPayee);

	int update(CrowdfundingInfoPayee crowdfundingInfoPayee);

    @DataSource(DS.CF_SLAVE)
    CrowdfundingInfoPayee getByInfoUuid(@Param("infoUuid") String infoUuid);
}
