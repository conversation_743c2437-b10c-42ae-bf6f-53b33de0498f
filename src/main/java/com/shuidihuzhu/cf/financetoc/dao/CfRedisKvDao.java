package com.shuidihuzhu.cf.financetoc.dao;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.common.web.model.RedisKv;
import org.apache.ibatis.annotations.Param;

/**
 * Created by wangsf on 17/2/16.
 */
@DataSource(DS.CF)
public interface CfRedisKvDao {

	@DataSource(DS.CF_SLAVE)
	String queryValueByKey(@Param("key") String key);
}
