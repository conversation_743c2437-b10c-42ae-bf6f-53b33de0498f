package com.shuidihuzhu.cf.financetoc.dao.order;


import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.contribute.CfContributeOrder;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@DataSource(CfDataSource.CF_ORDER_NEW_MASTER_DATASOURCE)
public interface CfContributeOrderDao {

    int addContributeOrder(CfContributeOrder contributeOrder);

    int updatePaySuccess(@Param("payUid") String payUid,
                         @Param("thirdPayUid") String thirdPayUid,
                         @Param("orderStatus") Integer orderStatus,
                         @Param("callbackTime") Date callbackTime,
                         @Param("realPayAmount") Integer realPayAmount,
                         @Param("feeAmount") Integer feeAmount);

    List<CfContributeOrder> selectByPayUids(@Param("payUids") List<String> payUids);

    @DataSource(CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
    List<CfContributeOrder> selectByUserIds(@Param("userIds") List<Long> userIds,
                                            @Param("statusList") List<Integer> statusList);

    int updateUserByIds(@Param("ids") List<Long> ids, @Param("toUserId") long toUserId);

}
