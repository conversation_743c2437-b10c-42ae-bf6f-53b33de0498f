package com.shuidihuzhu.cf.financetoc.dao.order;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @author: wanghui
 * @time: 2019/3/12 8:56 PM
 * @description:  以orderid分表的dao
 */

@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_SLAVE)
public interface CrowdfundingPayRecordShardingOrderIdDao {

	@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_MASTER)
	int addPayRecord(CrowdfundingPayRecord payRecord);

	@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_MASTER)
	int updatePayStatus(@Param("crowdfundingOrderId") Long crowdfundingOrderId,@Param("payUid") String payUid,
						@Param("payStatus") Integer payStatus,@Param("callbackTime") Date callbackTime,
						@Param("realPayAmount") Integer realPayAmount);

	@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_MASTER)
	CrowdfundingPayRecord getByOrderId(@Param("crowdfundingOrderId") Long orderId);

	List<CrowdfundingPayRecord> getByOrderIdsAndPayUids(@Param("orderIds")List<Long> orderIds,@Param("payUids") List<String> payUids);
}
