package com.shuidihuzhu.cf.financetoc.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.vo.CfFirsApproveMaterialVO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @Author: duchao
 * @Date: 2018/8/4 下午4:04
 */
@DataSource(DS.CF)
public interface CfFirstApproveMaterialDao {

	@DataSource(DS.CF_SLAVE)
	CfFirsApproveMaterial getByInfoUuid(@Param("infoUuid") String infoUuid);

	@DataSource(DS.CF_SLAVE)
	List<CfFirsApproveMaterial> getByUserId(@Param("userId") long userId);

	int updateUserIdByIds(@Param("ids") List<Integer> ids, @Param("userId") long userId);
}
