package com.shuidihuzhu.cf.financetoc.dao.refund;

import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonMsg;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by Ahrievil on 2017/7/31
 */
@DataSource(DS.CF)
public interface CfRefuseReasonMsgDao {
	@DataSource(DS.CF_SLAVE)
	CfRefuseReasonMsg selectByInfoUuidAndType(@Param("infoUuid") String infoUuid, @Param("type") int type);
}
