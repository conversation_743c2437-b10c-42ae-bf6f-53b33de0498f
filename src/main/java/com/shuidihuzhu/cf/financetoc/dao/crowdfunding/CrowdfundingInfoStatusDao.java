package com.shuidihuzhu.cf.financetoc.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface CrowdfundingInfoStatusDao {

	int add(CrowdfundingInfoStatus crowdfundingInfoStatus);

	int updateByType(@Param("infoUuid") String infoUuid, @Param("type") int type, @Param("status") int status);

	int update(@Param("infoUuid") String infoUuid, @Param("status") int status);

	@DataSource(DS.CF_SLAVE)
	List<CrowdfundingInfoStatus> getByInfoUuid(@Param("infoUuid") String infoUuid);

	CrowdfundingInfoStatus getByInfoUuidAndType(@Param("infoUuid") String infoUuid, @Param("type") int type);
}
