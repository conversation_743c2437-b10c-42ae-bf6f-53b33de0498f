package com.shuidihuzhu.cf.financetoc.dao;

import com.shuidihuzhu.cf.model.goods.GoodsGear;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(DS.CF)
public interface GoodsGearDao {

	int updateNum(@Param("id") long id, @Param("goodsCount") int goodsCount);

	@DataSource(DS.CF_SLAVE)
	GoodsGear getById(@Param("id") long id);
}
