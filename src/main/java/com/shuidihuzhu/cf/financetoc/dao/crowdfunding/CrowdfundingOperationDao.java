package com.shuidihuzhu.cf.financetoc.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(DS.CF)
public interface CrowdfundingOperationDao {

	int add(CrowdfundingOperation crowdfundingOperation);

	int update(CrowdfundingOperation crowdfundingOperation);

	int updateOperation(@Param("infoUuid") String infoUuid,
	           @Param("operation") int operation,
	           @Param("operatorId") int operatorId,
	           @Param("reason") String reason);

	@DataSource(DS.CF_SLAVE)
	CrowdfundingOperation getByInfoId(@Param("infoId") String infoId);
}
