package com.shuidihuzhu.cf.financetoc.controller.feign;

import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceEmergencyFeignClient;
import com.shuidihuzhu.cf.finance.client.model.CheckEmergencyParam;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.financetoc.service.CrowdfundingInfoService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2025/6/5 17:08
 */
@Slf4j
@RestController
public class CfFinanceEmergencyFeignController implements CfFinanceEmergencyFeignClient {

    @Resource
    private CrowdfundingInfoService crowdfundingInfoService;

    @Override
    public FeignResponse<CfErrorCode> checkEmergencyError(CheckEmergencyParam checkEmergencyParam) {

        return new FeignResponse<>(crowdfundingInfoService.checkEmergencyError(
                checkEmergencyParam.getInfoId(),
                checkEmergencyParam.getEmergency(),
                checkEmergencyParam.getEmergencyPhone()
        ));
    }
}
