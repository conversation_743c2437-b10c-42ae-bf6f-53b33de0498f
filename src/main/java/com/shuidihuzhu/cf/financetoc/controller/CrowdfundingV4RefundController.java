package com.shuidihuzhu.cf.financetoc.controller;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceWriteFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.enums.CfRefundEnums;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CfInfoExtBiz;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.financetoc.biz.order.CrowdfundingOrderBiz;
import com.shuidihuzhu.cf.financetoc.delegate.ICFFinanceFeignDelegate;
import com.shuidihuzhu.cf.financetoc.util.ConfusionUtils;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfCaseVisitConfigVo;
import com.shuidihuzhu.common.web.annotation.SessionKeyValidateRequired;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * Created by lixuan on 2017/01/06.
 */
@Slf4j
@Controller
@RequestMapping(path = "api/cf/v4/refund")
public class CrowdfundingV4RefundController {
	@Autowired
	private CrowdfundingInfoBiz crowdfundingInfoBiz;
	@Autowired
	private CfInfoExtBiz cfInfoExtBiz;
	@Autowired
	private CrowdfundingOrderBiz crowdfundingOrderBiz;
	@Autowired
	private CfFinanceWriteFeignClient cfFinanceWriteFeignClient;
    @Resource
    private ICFFinanceFeignDelegate financeFeignDelegate;


	@RequestMapping(path = "launch", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@ResponseBody
	@SessionKeyValidateRequired
    @ApiOperation(value = "自主退款下发之后用户通消息触达退款")
	public Response launch(String infoUuid, @RequestParam(name = "orderId", required = false) Integer orderId,
						   @RequestParam(name = "newOrderId") Long newOrderId) {
		log.info("refund launch infoUuid:{};orderId:{};newOrderId:{}", infoUuid, orderId, newOrderId);
		if (newOrderId == null || newOrderId == 0) {
			log.info("参数不合法");
			return NewResponseUtil.makeFail("无法退款，请联系客服");
		}

		// 校验新旧orderId一致性
		long recoveredId = ConfusionUtils.recoverOrderId(newOrderId);
		log.info("refund launch recoveredId:{}", recoveredId);

		CrowdfundingOrder crowdfundingOrder = this.crowdfundingOrderBiz.getById(recoveredId);
		if (crowdfundingOrder == null) {
			return NewResponseUtil.makeError(CfErrorCode.CF_INFO_ORDER_INVALID);
		}

		CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(crowdfundingOrder.getCrowdfundingId());

		if (crowdfundingInfo == null || !infoUuid.equals(crowdfundingInfo.getInfoId())) {
			return NewResponseUtil.makeError(CfErrorCode.CF_NOT_FOUND);
		}
		CfInfoExt cfInfoExt = this.cfInfoExtBiz.getByInfoUuid(infoUuid);
		log.info("refund launch cfInfoExt:{}", cfInfoExt);
		if (cfInfoExt == null || cfInfoExt.getRefundEndTime() == null
				|| cfInfoExt.getRefundEndTime().before(new Date())) {
			return NewResponseUtil.makeError(CfErrorCode.CF_CAN_NOT_REFUND);
		}

		// 检查提现状态--这个交给finance退款处理来做处理
		Response response = financeFeignDelegate.cfRefundLaunchHelper(ContextUtil.getUserId(), infoUuid, recoveredId);
        log.info("refund launch infoUuid:{};recoveredId:{}\tfeignResponse:{}", infoUuid, recoveredId,JSON.toJSON(response));
        return response;
	}
	
	/**
	 * C端用户申请退款
	 * @param infoUuid
	 * @param reason
	 * @param refundMsgCode 退款原因code
	 * @return
	 */
	@RequestMapping(path = "/apply-refund", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
	@ResponseBody
	@SessionKeyValidateRequired
	public Response applyRefund(String infoUuid,
	                            @RequestParam(value = "reason", required = false, defaultValue = "") String reason,
	                            @RequestParam(value = "refundMsgCode", required = false, defaultValue = "0") int refundMsgCode) {
		log.info("CrowdfundingV4RefundController applyRefund infoUuid:{};reason:{} refundMsgCode:{}", infoUuid, reason, refundMsgCode);
		// 如果是其他原因且没有填内容
		if (refundMsgCode == CfRefundEnums.CfRefundReasonV2.OTHER.getCode() && StringUtils.isBlank(reason)) {
			return NewResponseUtil.makeResponse(-1,"请输入申请退款原因","");
		}
		long userId = ContextUtil.getUserId();
		if (userId <= 0) {
			log.warn("CrowdfundingV4RefundController finish contextUserId: {}", userId);
			return NewResponseUtil.makeError(CfErrorCode.CF_USER_ACCOUNT_NO_LOGIN);
		}
		CfRefundEnums.CfRefundReasonV2 refundReasonV2 = CfRefundEnums.CfRefundReasonV2.getByCode(refundMsgCode);
		if (Objects.isNull(refundReasonV2) || !refundReasonV2.getApplyUserSet().contains(CfRefundEnums.ApplyUser.C_USER)) {
			return NewResponseUtil.makeResponse(-1,"请重新选择申请退款原因","");
		}

		CrowdfundingInfo crowdfundingInfo = this.crowdfundingInfoBiz.getFundingInfo(infoUuid);
		if (crowdfundingInfo == null) {
			return NewResponseUtil.makeError(CfErrorCode.CF_NOT_FOUND);
		}
		//todo:临时补救后续干掉 1883894用户 c4c31e76-29d6-411f-a43f-ab01c55ebee3测试   线上测试案例
		if (crowdfundingInfo.getId() == 1883894 || "c4c31e76-29d6-411f-a43f-ab01c55ebee3".equals(crowdfundingInfo.getInfoId()) ||
				crowdfundingInfo.getId() == 2055988) {
			return NewResponseUtil.makeResponse(-1,"您的退款已完成，所筹款项已全部原路退还给所有赠与人。如有疑问，请拨打400-686-1179","");
		}

		if (crowdfundingInfo.getEndTime().after(new Date())) {
			return NewResponseUtil.makeResponse(-1,"案例未结束，不支持退款申请","");
		}
		if (crowdfundingInfo.getUserId() != userId) {
			log.warn("CrowdfundingV4RefundController finish contextUserId:{}, crowdfundingInfoUserId:{}", userId,
					crowdfundingInfo.getUserId());
			return NewResponseUtil.makeResponse(-1,"当前用户与案例发起人的身份不一致，请核对身份信息","");
		}
		//走finance的退款申请
		FeignResponse<Response> feignResponse = cfFinanceWriteFeignClient.fundraiserApplyRefund(infoUuid, reason,userId, refundMsgCode);
		if (null == feignResponse || feignResponse.notOk() || feignResponse.getData().notOk()) {
			return NewResponseUtil.makeResponse(-1,"发起退款失败，水滴筹正在对筹款信息做进一步核实，如果着急申请，请拨打400-686-1179","");
		}
		return feignResponse.getData();
	}

	@ApiOperation(value = "取消退款申请", response = CfCaseVisitConfigVo.class)
	@PostMapping(path = "/cancel-refund-apply")
	@ResponseBody
	@SessionKeyValidateRequired
	public Response<Void> cancelRefundApply(@RequestParam("infoUuid") String infoUuid) {
		if (StringUtils.isBlank(infoUuid)) {
			return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
		}

		CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
		if (Objects.isNull(fundingInfo)) {
			return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
		}
		long userId = ContextUtil.getUserId();
		if (userId <= 0) {
			return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
		}
		if (fundingInfo.getUserId() != userId) {
			return NewResponseUtil.makeError(CfErrorCode.CF_INFO_PARAM_ERROR_IDENTITY_INVALID);
		}
		return financeFeignDelegate.cancelRefundApply(fundingInfo.getId(), userId);
	}

}
