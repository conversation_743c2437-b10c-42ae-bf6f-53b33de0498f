package com.shuidihuzhu.cf.financetoc.controller;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.contribute.ContributeSourceEnum;
import com.shuidihuzhu.cf.financetoc.service.CaseDataStatService;
import com.shuidihuzhu.cf.financetoc.service.DonationOrderFacade;
import com.shuidihuzhu.cf.model.contribute.ContributeAddOrderDTO;
import com.shuidihuzhu.cf.model.crowdfunding.vo.OrderResultVo;
import com.shuidihuzhu.cf.param.order.LoveHelpCombineOrderParam;
import com.shuidihuzhu.charity.client.api.CharityClient;
import com.shuidihuzhu.common.web.annotation.SessionKeyValidateRequired;
import com.shuidihuzhu.common.web.constants.AlipayConfig;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;
import java.util.Objects;


/**
 * 捐款预下单
 */
@RefreshScope
@RestController
@RequestMapping(path = "api/cf/v41/order")
@Slf4j
public class CrowdfundingOrderController {

    public static final String HUZHU_PAGE_DOMAIN = "https://www.shuidihuzhu.com";

    @Autowired
    private DonationOrderFacade donationOrderFacade;
    @Autowired
    private CaseDataStatService caseDataStatService;
    @Autowired
    private CharityClient charityClient;


    @RequestMapping(path = "/add", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @SessionKeyValidateRequired
    public Response<OrderResultVo> addOrder(String infoUuid, double amt, String comment, String platform,
                                            @RequestParam(name = "source", required = false, defaultValue = "") String source,
                                            @RequestParam(name = "selfTag", required = false, defaultValue = "") String selfTag,
                                            @RequestParam(required = false, defaultValue = AlipayConfig.DEFAULT_SHOW_URL) String
                                                    showUrl,
                                            @RequestParam(required = false, defaultValue = AlipayConfig.DEFAULT_RETURN_URL) String
                                                    returnUrl,
                                            @RequestParam(required = false, defaultValue = "0") Integer cfPayStatus,
                                            @RequestParam(required = false) Long userAddressId, @RequestParam(required = false) Long
                                                    gearId,
                                            @RequestParam(required = false) Integer goodsCount,
                                            @RequestParam(name = "anonymous", required = false) Boolean anonymous,
                                            @RequestParam(name = "isFollow", defaultValue = "true", required = false) boolean isFollow,
                                            @RequestParam(name = "userThirdType", required = false) Integer userThirdType,
                                            @RequestParam(name = "splitFlowUuid", required = false) String splitFlowUuid,
                                            @RequestParam(defaultValue = "") String shareSourceId, @RequestParam(defaultValue = "0")
                                                    Integer payType,
                                            @ApiParam("是否使用捐助金") @RequestParam(required = false, defaultValue = "false") boolean useSubsidy,
                                            @RequestParam(required = false, defaultValue = "-97") int shareDv,
                                            HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
                                            @RequestParam(name = "activityId", defaultValue = "0", required = false) int activityId,
                                            @ApiParam("小善日分会场id") @RequestParam(value = "subActivityId", required = false, defaultValue = "") String subActivityId,
                                            @ApiParam("祝福卡片id") @RequestParam(name = "blessingCardId", defaultValue = "0", required = false) int blessingCardId,
                                            @ApiParam("捐赠金额") @RequestParam(name = "contributeAmount", defaultValue = "0", required = false) Integer contributeAmount,
                                            @ApiParam("众人帮活动id") @RequestParam(value = "bonfireUuid", required = false, defaultValue = "") String bonfireUuid,
                                            @RequestParam(value = "loveHelpCombineOrderParamsStr", required = false, defaultValue = "") String loveHelpCombineOrderParamsStr) throws Exception {
        long userId = ContextUtil.getUserId();
        if(StringUtils.isEmpty(returnUrl)) {
            String referer = StringUtils.trimToEmpty(httpServletRequest.getHeader("Referer"));
            if(referer.isEmpty()) {
                referer = HUZHU_PAGE_DOMAIN;
            }
            returnUrl = referer;
        }

        String clientIp = ContextUtil.getClientIp();
        Integer osType = ContextUtil.getOSType();
        String channel = ContextUtil.getChannel();
        //如果活动主会场的id为0，使用活动管理的活动id
        log.info("addOrder origin activityId:{},infoId:{}", activityId, infoUuid);
        if(activityId == 0) {
            Response<Long> activityData = charityClient.getvalidActivityIdByInfoId(infoUuid);
            if(activityData.ok()) {
                activityId = (int) (long) activityData.getData();
            }
            log.info("addOrder real activityId:{},infoId:{}", activityData, infoUuid);
        }
        List<LoveHelpCombineOrderParam> loveHelpCombineOrderParams = Collections.emptyList();
        if (StringUtils.isNotBlank(loveHelpCombineOrderParamsStr)) {
            try {
                loveHelpCombineOrderParams = JSON.parseArray(loveHelpCombineOrderParamsStr, LoveHelpCombineOrderParam.class);
            } catch (Exception e) {
                log.error("下单接口,合并支付参数异常, loveHelpCombineOrderParamsStr:{}", loveHelpCombineOrderParamsStr, e);
            }
        }
        Response<OrderResultVo> resultVoResponse = donationOrderFacade.createOrder(userId, infoUuid, amt, comment, platform, source, selfTag, showUrl,
                returnUrl, userAddressId, gearId, goodsCount, anonymous, userThirdType, splitFlowUuid, shareSourceId,
                payType, activityId, useSubsidy, shareDv, subActivityId, clientIp, osType, channel, blessingCardId,  contributeAmount,bonfireUuid,loveHelpCombineOrderParams);
        try {
            caseDataStatService.sendPaySuccess(String.valueOf(shareDv), resultVoResponse);
        } catch (Exception e) {
            log.error("sharedv:{} resultVoResponse:{}", shareDv, resultVoResponse, e);
        }
        return resultVoResponse;
    }

    @RequestMapping(path = "/contribute-add", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @SessionKeyValidateRequired
    public Response<OrderResultVo> addContributeOrder(@RequestBody ContributeAddOrderDTO contributeAddOrderDTO){

        if(Objects.isNull(contributeAddOrderDTO)){
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        long userId = ContextUtil.getUserId();
        String clientIp = ContextUtil.getClientIp();
        Integer osType = ContextUtil.getOSType();
        if(userId <= 0 ){
            return NewResponseUtil.makeError(CfErrorCode.CF_USER_ACCOUNT_NO_LOGIN);
        }
        if(contributeAddOrderDTO.getAmt() <= 0){
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        ContributeSourceEnum contributeSourceEnum = ContributeSourceEnum.getByChannel(contributeAddOrderDTO.getChannel());
        if(Objects.isNull(contributeSourceEnum)){
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        Response<OrderResultVo> resultVoResponse = donationOrderFacade.createContributeOrder(contributeAddOrderDTO,userId,clientIp,osType);
        try {
            caseDataStatService.sendPaySuccess("", resultVoResponse);
        } catch (Exception e) {
            log.error("resultVoResponse:{}", resultVoResponse, e);
        }
        return resultVoResponse;
    }
}
