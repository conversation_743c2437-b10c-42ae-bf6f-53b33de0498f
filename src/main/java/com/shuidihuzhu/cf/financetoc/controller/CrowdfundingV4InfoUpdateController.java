package com.shuidihuzhu.cf.financetoc.controller;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CfFinishStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingConstant;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.finance.client.feign.deposit.CfDepositAccountFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.financetoc.biz.payee.CfCharityPayeeBiz;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.financetoc.biz.payee.CrowdfundingInfoHospitalPayeeBiz;
import com.shuidihuzhu.cf.financetoc.biz.payee.CrowdfundingInfoPayeeBiz;
import com.shuidihuzhu.cf.financetoc.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.financetoc.service.*;
import com.shuidihuzhu.cf.financetoc.util.BackCardUtil;
import com.shuidihuzhu.cf.financetoc.util.BeenCopyUtil;
import com.shuidihuzhu.cf.financetoc.util.CfIdCardUtil;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoPayeeVo;
import com.shuidihuzhu.cf.response.crowdfunding.CrowdfundingInfoResponse;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.risk.model.enums.UserOperationEnum;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.annotation.SessionKeyValidateRequired;
import com.shuidihuzhu.common.web.enums.LoginResult;
import com.shuidihuzhu.common.web.enums.Platform;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.IdCardUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 收款材料
 */
@Slf4j
@RefreshScope
@Controller
@RequestMapping(path = "api/cf/v4", produces = "application/json;charset=UTF-8")
public class CrowdfundingV4InfoUpdateController {

    @Autowired
    private CrowdfundingInfoService crowdfundingInfoService;
    @Autowired
    private UserInfoDelegate userInfoDelegate;
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CfInfoMirrorService cfInfoMirrorService;
    @Autowired
    private CrowdfundingInfoPayeeBiz crowdfundingInfoPayeeBiz;
    @Autowired
    private CfInfoHospitalPayeeService cfInfoHospitalPayeeService;
    @Autowired
    private ICfRiskService cfRiskService;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;
    @Autowired
    private CfCharityPayeeBiz cfCharityPayeeBiz;
    @Autowired
    private CfCaseEndFacade cfCaseEndFacade;
    @Autowired
    private CfCommonStoreService cfCommonStoreService;
    @Autowired
    private CrowdfundingInfoHospitalPayeeBiz crowdfundingInfoHospitalPayeeBiz;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private CfMaterialStatusService cfMaterialStatusService;
    @Autowired
    private CfDepositAccountFeignClient cfDepositAccountFeignClient;


    @RequestMapping(path = "/open-payee-account", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    @ApiOperation(value = "四要素银行卡信息开户")
    /**
     * 平安三要素
     *  银行卡鉴权 只需要  持卡人姓名  卡号  身份证号 （外加 营业网点信息）
     */
    public Response<Void> openPayeeAccount(@RequestParam @ApiParam(value = "案例infoUuid") String infoUuid,
                                           @RequestParam @ApiParam(value = "持卡人姓名") String payeeName,
                                           @RequestParam @ApiParam(value = "持卡人卡号") String bankCard,
                                           @RequestParam @ApiParam(value = "发卡行名称") String bankName,
                                           @RequestParam @ApiParam(value = "持卡人身份证号") String idCard,
                                           @RequestParam(required = false, defaultValue = "") @ApiParam(value = "开户支行对应的联行号") String cnapsBranchId,
                                           @RequestParam(required = false, defaultValue = "") @ApiParam(value = "开户支行营业网点信息") String bankBranchName) {

        long userId = ContextUtil.getUserId();
        Platform platform = ContextUtil.getPlatform();
        String clientIp = ContextUtil.getClientIp();

        if (userId <= 0) {
            log.warn("CrowdfundingV4InfoUpdateController addOrUpdatePayeeInfo userId is {}", userId);
            return NewResponseUtil.makeResponse(LoginResult.LOGIN.code, LoginResult.LOGIN.msg, null);
        }

        CrowdfundingInfo crowdfundingInfo = this.crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null || userId != crowdfundingInfo.getUserId()) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_NOT_EXIST_RECORD_ERROR);
        }

        payeeName = StringUtils.trimToEmpty(payeeName);
        bankCard = StringUtils.trimToEmpty(bankCard);
        bankName = StringUtils.trimToEmpty(bankName);
        idCard = StringUtils.trimToEmpty(idCard);
        cnapsBranchId = StringUtils.trimToEmpty(cnapsBranchId);

        if (StringUtils.isBlank(bankName) || StringUtils.isBlank(payeeName)) {
            return NewResponseUtil.makeError(CfErrorCode.CF_INFO_PARAM_ERROR_PAYEE);
        }
        if (StringUtils.isBlank(bankCard) || !BackCardUtil.checkBankCard(bankCard)) {
            return NewResponseUtil.makeError(CfErrorCode.CF_BANK_CARD_VERIFY_FAILED);
        }
        if (IdCardUtil.illegal(idCard)) {
            return NewResponseUtil.makeError(CfErrorCode.CF_PARAM_ERROR_PAYEE_ID_CARD_ERROR);
        }
        //身份证不足18位不让提交（过滤掉15位的身份证）
        if (!CfIdCardUtil.isValidIdCard(idCard)) {
            return NewResponseUtil.makeError(CfErrorCode.USER_INFO_ID_CARD_ERROR);
        }

        // 年龄小于18
        if (CfIdCardUtil.getCurrentAge(idCard) < 18) {
            return NewResponseUtil.makeError(CfErrorCode.PAYEE_NOT_ALLOW_OF_AGE);
        }

        FeignResponse<Void> response = cfDepositAccountFeignClient.openPayeeAccount(infoUuid, payeeName, bankCard, bankName, idCard, cnapsBranchId, bankBranchName, userId, platform, clientIp);
        return NewResponseUtil.makeResponse(response.getCode(), response.getMsg(), null);
    }


    @RequestMapping(path = "/save-payee-info", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    @ApiOperation(value = "保存收款人信息")
    public Response<CrowdfundingInfoResponse.Data> savePayeeInfo(@RequestBody MaterialsParam materialsParam) {
        String appVersion = ContextUtil.getAppVersion();

        String key = materialsParam.getInfoUuid()+"_payee";
        try {
            String identifier = redissonHandler.tryLock(key, 0, 5000);
            if (StringUtils.isBlank(identifier)) {
                return NewResponseUtil.makeError(CfErrorCode.CLICK_TOO_FAST);
            }
        } catch (InterruptedException e) {
            log.info("savePayeeInfo lock error materialsParam={}",materialsParam);
        }

        long userId = ContextUtil.getUserId();
        List<String> materialsKeys = materialsParam.getMaterialsKeys();

        if (CollectionUtils.isEmpty(materialsKeys) || StringUtils.isEmpty(materialsParam.getInfoUuid()) || userId <= 0){
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<CfCommonStoreModel> list = cfCommonStoreService.getByKeys(userId,materialsKeys,materialsParam.getInfoUuid());
        log.info("查询出的案例材料值有. userId:{} result:{}", userId, JSON.toJSONString(list));
        if (CollectionUtils.isEmpty(list)){
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR_VALID_DATA_RANGE);
        }
        // bugfix key相同时，后保存的优先级应该更高
        list = list.stream()
                .sorted(Comparator.comparing(CfCommonStoreModel::getUpdateTime)).collect(Collectors.toList());
        CrowdfundingInfoPayee crowdfundingInfoPayee = crowdfundingInfoPayeeBiz.getByInfoUuid(materialsParam.getInfoUuid());
        if (crowdfundingInfoPayee != null){

            boolean flag = cfMaterialStatusService.checkReject4Submit(materialsParam.getInfoUuid(),userId,CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT,materialsParam.getRejectCodes());
            if (!flag){
                return NewResponseUtil.makeSuccess(null);
            }
            return crowdfundingInfoService.updatePayeeInfo(crowdfundingInfoPayee,userId,list,materialsParam,appVersion);
        }

        CrowdfundingInfoPayeeVo payee = new CrowdfundingInfoPayeeVo();
        list.forEach(r-> BeenCopyUtil.jsonToClass(payee,r.getStoreValue(),CrowdfundingInfoPayeeVo.class));
        return crowdfundingInfoService.savePayeeInfo(payee,userId,appVersion);

    }

    @RequestMapping(path = "/add-or-update-payee-info", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    @ApiOperation(value = "保存收款人信息")
    public Response<CrowdfundingInfoResponse.Data> addOrUpdatePayeeInfo(String param) {
        log.info("CrowdfundingV4InfoUpdateController addOrUpdatePayeeInfo param:{}", param);
        CrowdfundingInfoPayeeVo crowdfundingInfoPayeeVo = JSON.parseObject(param, CrowdfundingInfoPayeeVo.class);
        long userId = ContextUtil.getUserId();
        String appVersion = ContextUtil.getAppVersion();
        return crowdfundingInfoService.savePayeeInfo(crowdfundingInfoPayeeVo,userId,appVersion);
    }

    @RequestMapping(path = "/finish-v2", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    public Response<Void> finishV2(String infoUuid, String selfTag, @RequestParam(required = false) String desc
            ,@RequestParam(value = "verifyCode",required = false,defaultValue = "") String verifyCode,
                                   @RequestParam(value = "version",required = false,defaultValue = "v1")String version,
                                   @RequestParam(value = "reasonType", required = false, defaultValue = "0") int reasonType) {

        if (StringUtils.length(desc) > 4000) {
            return NewResponseUtil.makeFail("描述过长");
        }

        long userId = ContextUtil.getUserId();
        if (userId <= 0) {
            log.warn("CrowdfundingV4InfoUpdateController finish contextUserId: {}", userId);

            return NewResponseUtil.makeResponse(LoginResult.LOGIN.code, LoginResult.LOGIN.msg, null);
        }
        CrowdfundingInfo crowdfundingInfo = this.crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(CfErrorCode.CF_NOT_FOUND);
        }
        if (crowdfundingInfo.getUserId() != userId) {
            log.warn("CrowdfundingV4InfoUpdateController finish contextUserId:{}, crowdfundingInfoUserId:{}",
                    userId, crowdfundingInfo.getUserId());
            return NewResponseUtil.makeError(CfErrorCode.CF_INFO_PARAM_ERROR_IDENTITY_INVALID);
        }

        if (!cfRiskService.operatorValid(0, crowdfundingInfo.getId(), UserOperationEnum.RAISER_FINISH)) {
            return NewResponseUtil.makeError(CfErrorCode.RISK_RAISER_FINISH_NOT_ALLOW);
        }

        Date endTime = crowdfundingInfo.getEndTime();

        /**
         * 由于上一版本前端代码没有verifyCode,为了兼容老系统，增加version,
         * 老版本不传，verifyCode和version
         * 新版本version为 v2
         */
        if(!("v1".equals(version) && "".equals(verifyCode))){
            if(!"v2".equals(version)){
                return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
            }
            //检查验证码
            if(StringUtils.isBlank(verifyCode)){
                return NewResponseUtil.makeFail("请输入验证码");
            }
            Response response = cfCaseEndFacade.checkVerifyCode(userId,ContextUtil.getClientIp(),verifyCode);
            if(Objects.isNull(response) || response.notOk()){
                return response;
            }
        }else{
            log.info("userId:{},infoUuid:{}",userId,crowdfundingInfo.getInfoId());
        }
        if (endTime.after(new Date())) {
            CfOperatingRecord cfOperatingRecord = this.cfInfoMirrorService.before(crowdfundingInfo,
                    crowdfundingInfo.getUserId(),
                    crowdfundingInfo.getPayeeName(),
                    CfOperatingRecordEnum.Type.STOP_CF,
                    CfOperatingRecordEnum.Role.USER);
            int caseId
                    = crowdfundingInfo.getId();
            CfFinishStatus endType = CfFinishStatus.FINISH_BY_RAISER;
            cfCaseEndFacade.stopCase(caseId, endType, 0, desc, reasonType);
            this.cfInfoMirrorService.after(cfOperatingRecord);
        }
        return NewResponseUtil.makeSuccess(null);
    }


    @RequestMapping(path = "/save-hospital-payee", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    public Response<CrowdfundingInfoResponse.Data> saveHospitalPayee(@RequestBody MaterialsParam materialsParam) {

        long userId = ContextUtil.getUserId();
        List<String> hospitalPayees = materialsParam.getMaterialsKeys();

        if (CollectionUtils.isEmpty(hospitalPayees) || StringUtils.isEmpty(materialsParam.getInfoUuid()) || userId <= 0){
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<CfCommonStoreModel> list = cfCommonStoreService.getByKeys(userId,hospitalPayees,materialsParam.getInfoUuid());
        if (CollectionUtils.isEmpty(list) || list.size() != hospitalPayees.size()){
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR_VALID_DATA_RANGE);
        }

        CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee = crowdfundingInfoHospitalPayeeBiz.getByInfoUuid(materialsParam.getInfoUuid());
        //更新
        if (crowdfundingInfoHospitalPayee != null){

            boolean flag = cfMaterialStatusService.checkReject4Submit(materialsParam.getInfoUuid(),userId,CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT,materialsParam.getRejectCodes());
            if (!flag){
                return NewResponseUtil.makeSuccess(null);
            }

            crowdfundingInfoHospitalPayee.setHospitalAccountName(shuidiCipher.decrypt(crowdfundingInfoHospitalPayee.getHospitalAccountName()));
            list.stream().forEach(r->BeenCopyUtil.jsonToClass(crowdfundingInfoHospitalPayee,r.getStoreValue(),CrowdfundingInfoHospitalPayee.class));
            return cfInfoHospitalPayeeService.saveHospitalPayee(crowdfundingInfoHospitalPayee,userId);
        }

        CrowdfundingInfoHospitalPayee payee = new CrowdfundingInfoHospitalPayee();
        list.stream().forEach(r-> BeenCopyUtil.jsonToClass(payee,r.getStoreValue(),CrowdfundingInfoHospitalPayee.class));
        return cfInfoHospitalPayeeService.saveHospitalPayee(payee,userId);

    }


    @RequestMapping(path = "/add-or-update-hospital-payee-info", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    public Response<CrowdfundingInfoResponse.Data> addOrUpdateHospitalDrawcashInfo(String param) {
        log.info("CrowdfundingV4InfoUpdateController saveHospitalDrawcashInfo param:{}", param);
        CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee = JSON.parseObject(param,
                CrowdfundingInfoHospitalPayee.class);
        return cfInfoHospitalPayeeService.saveHospitalPayee(crowdfundingInfoHospitalPayee,ContextUtil.getUserId());
    }


    @RequestMapping(path = "/add-or-update-charity-payee-info", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    public Response<Void> addOrUpdateCharityPayeeInfo(@RequestParam("param") String param) {
        long userId = ContextUtil.getUserId();
        log.info("CrowdfundingV4InfoUpdateController addOrUpdateCharityPayeeInfo userId={} param:{}", userId, param);
        CfCharityPayee cfCharityPayee = JSON.parseObject(param, CfCharityPayee.class);
        if (cfCharityPayee == null
                || StringUtils.isEmpty(cfCharityPayee.getInfoUuid())
                || StringUtils.isEmpty(cfCharityPayee.getOrgName())
                || StringUtils.isEmpty(cfCharityPayee.getOrgMobile())
                || StringUtils.isEmpty(cfCharityPayee.getOrgBankName())
                || StringUtils.isEmpty(cfCharityPayee.getOrgBankCard())
                || StringUtils.isEmpty(cfCharityPayee.getOrgBankBranchName())
        ) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR_IS_NULL);
        }
        cfCharityPayee.setOrgBankCard(cfCharityPayee.getOrgBankCard().replaceAll("\\s+",""));
        CfErrorCode cfErrorCode = cfCharityPayeeBiz.insertOrUpdate(cfCharityPayee, userId);
        return NewResponseUtil.makeError(cfErrorCode);
    }
}

