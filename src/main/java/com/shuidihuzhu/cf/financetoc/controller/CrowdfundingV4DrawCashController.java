package com.shuidihuzhu.cf.financetoc.controller;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceWriteFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.enums.NewCfCapitalEnum;
import com.shuidihuzhu.cf.finance.model.vo.DrawApplyCheckResult;
import com.shuidihuzhu.cf.finance.model.vo.draw.CfAmountInfoVo;
import com.shuidihuzhu.cf.finance.model.vo.drawcash.CrowdfundingInfoApplyDrawVo;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.financetoc.delegate.ICFFinanceFeignDelegate;
import com.shuidihuzhu.cf.financetoc.delegate.IFinanceDrawCashFeignDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoPayeeVo;
import com.shuidihuzhu.common.web.annotation.SessionKeyValidateRequired;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.util.Date;
import java.util.Objects;

/**
 * 提现
 */
@Slf4j
@Controller
@RefreshScope
@RequestMapping(path = "api/cf/v4/drawcash")
@SuppressWarnings({"all"})
public class CrowdfundingV4DrawCashController {

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CfFinanceWriteFeignClient cfFinanceWriteFeignClient;
    @Autowired
    private IFinanceDrawCashFeignDelegate financeDrawCashFeignDelegate;
    @Autowired
    private ICFFinanceFeignDelegate icfFinanceFeignDelegate;

    /**
     * 申请提现
     *  2020年06月08日 feign 接口内部限定为筹款人
     * @param param
     * @return
     */
    @RequestMapping(path = "/apply-draw-cash", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    public Response applyDrawCash(String param) {
        long userId = ContextUtil.getUserId();
        FeignResponse<Response> feignResponse = cfFinanceWriteFeignClient.applyDrawCash(userId, param);
        if (feignResponse != null) {
            log.debug("applydrawcash, param:{} response:{}", param, feignResponse.getData());
            return feignResponse.getData();
        } else {
            log.error("申请提现审核失败, param:{}", param);
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_ERROR);
        }
    }

    private CrowdfundingInfoPayeeVo.CanCashResult convert(DrawApplyCheckResult checkResult,
                                                          CrowdfundingStatus crowdfundingStatus) {
        if (crowdfundingStatus == null) {
            crowdfundingStatus = CrowdfundingStatus.APPROVE_PENDING;
        }
        if (checkResult == null) {
            return new CrowdfundingInfoPayeeVo.CanCashResult(false, "系统维护—待会在尝试", crowdfundingStatus);
        }
        CrowdfundingInfoPayeeVo.CanCashResult canCashResult = new CrowdfundingInfoPayeeVo.CanCashResult(checkResult.isCheckResult(), checkResult.getRemark(),
                checkResult.getInfoUuid(), checkResult.isSupportDrawSplit(), checkResult.getCheckResultVo(), crowdfundingStatus);
        if (Objects.nonNull(canCashResult.getResultErrorType()) && Objects.nonNull(canCashResult.getResultErrorType().getResultErrorType())) {
            DrawApplyCheckResult.ResultErrorType resultErrorType = canCashResult.getResultErrorType().getResultErrorType();
            // 提现流程
            canCashResult.setDrawProcessError(resultErrorType.getCode() / 100 == 2);
        }

        return canCashResult;
    }


    @PostMapping(path = "draw-split/apply-draw-cash", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    @SessionKeyValidateRequired
    @ApiOperation(value = "C端用户自定义金额发起提现申请（边筹边取）")
    public Response<CrowdfundingInfoPayeeVo.CanCashResult> drawSplitApplyDrawCash(
            @ModelAttribute @ApiParam(value = "申请提现信息") CrowdfundingInfoApplyDrawVo crowdfundingInfoApplyDrawVo,
            @ApiParam(value = "订单截止时间") @RequestParam(required = false, defaultValue = "0") long deadlineOrderTimeLong) {
        String infoUuid = crowdfundingInfoApplyDrawVo.getInfoUuid();
        if (StringUtils.isEmpty(infoUuid)) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (deadlineOrderTimeLong > 0) {
            crowdfundingInfoApplyDrawVo.setDeadlineOrderTime(new Timestamp(deadlineOrderTimeLong));
        }
        CrowdfundingInfo fundingInfo = this.crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (fundingInfo == null) {
            return NewResponseUtil.makeError(CfErrorCode.CF_NOT_FOUND);
        }

        DrawApplyCheckResult drawApplyCheckResult = new DrawApplyCheckResult();
        drawApplyCheckResult.setCheckResult(false);
        drawApplyCheckResult.setSupportDrawSplit(true);
        DrawApplyCheckResult.ResultVo resultVo = new DrawApplyCheckResult.ResultVo();
        resultVo.setResultErrorType(DrawApplyCheckResult.ResultErrorType.COMMON_MSG);
        drawApplyCheckResult.setCheckResultVo(resultVo);
        // 限定为筹款人
        long userId = ContextUtil.getUserId();
        if (fundingInfo.getUserId() != userId) {
            log.error("caseId:{} loginUserId:{} funding.userId:{} 非本人申请提现", fundingInfo.getId(), userId, fundingInfo.getUserId());
            drawApplyCheckResult.setRemark("当前用户身份有误，非案例发起人");
            resultVo.setTitle("不能申请提现，非案例发起人");
            resultVo.setTipMsg("不能申请提现，非案例发起人。有疑问请联系客服");
            return NewResponseUtil.makeFail(this.convert(drawApplyCheckResult, fundingInfo.getStatus()));
        }
        crowdfundingInfoApplyDrawVo.setCaseId(fundingInfo.getId());
        crowdfundingInfoApplyDrawVo.setApplyUserId(ContextUtil.getUserId());
        if (crowdfundingInfoApplyDrawVo.getDrawCashType() == 0) {
            crowdfundingInfoApplyDrawVo.setDrawCashType(NewCfCapitalEnum.DrawCashType.OFFLINE_PAY.getCode());
        }

        // 提现金额信息校验
        Response response = this.checkApplyAmountInfo(fundingInfo.getId(),
                fundingInfo.getInfoId(), crowdfundingInfoApplyDrawVo.getDrawCashAmount(),
                NewCfCapitalEnum.DrawCashType.getByValue(crowdfundingInfoApplyDrawVo.getDrawCashType()),
                crowdfundingInfoApplyDrawVo.getDeadlineOrderTime());

        if (response.notOk()) {
            log.warn("提现申请金额校验异常,caseId:{},{}", fundingInfo.getId(), JSON.toJSONString(response));
            return response;
        }

        Response<DrawApplyCheckResult> resultResponse = financeDrawCashFeignDelegate.drawSplitApplyDrawCash(crowdfundingInfoApplyDrawVo);
        log.info("caseId:{} resultResponse:{}", fundingInfo.getId(), JSON.toJSON(resultResponse));
        if (resultResponse.notOk() || null == resultResponse.getData()) {
            drawApplyCheckResult.setRemark("当前系统繁忙，请稍后再试");
            resultVo.setTitle("当前系统繁忙，请稍后再试");
            resultVo.setTipMsg("当前系统繁忙，请稍后再试。有疑问请联系客服");
            return NewResponseUtil.makeFail(this.convert(drawApplyCheckResult, fundingInfo.getStatus()));
        }

        return NewResponseUtil.makeSuccess(convert(resultResponse.getData(), fundingInfo.getStatus()));
    }

    /**
     * 提现金额信息校验
     *
     * @param applyAmountInFen
     * @param drawCashType
     * @param applyTime
     * @return
     */
    private Response checkApplyAmountInfo(int caseId, String infoUuid, int applyAmountInFen,
                                          NewCfCapitalEnum.DrawCashType drawCashType, Timestamp applyTime) {
        if (caseId <= 0 || StringUtils.isBlank(infoUuid)) {
            return NewResponseUtil.makeError(ErrorCode.CF_NOT_FOUND);
        }
        if (applyAmountInFen <= 0) {
            return NewResponseUtil.makeFail("提现申请金额有误");
        }
        if (drawCashType == null) {
            return NewResponseUtil.makeFail("提现申请类型有误");
        }
        if (applyTime.after(new Timestamp(System.currentTimeMillis()))) {
            return NewResponseUtil.makeFail("提现申请时间戳有误");
        }

        StopWatch watch = StopWatch.createStarted();
        CfAmountInfoVo cfAmountInfoVo = icfFinanceFeignDelegate
                .getFundraiserAmountInfo(infoUuid);
        watch.stop();

        if (cfAmountInfoVo == null) {
            log.error("查询可提现金额超时caseId:{},{}", caseId, watch.getTime());
            return NewResponseUtil.makeFail("系统繁忙，请稍后再试");
        }
        log.info("caseId:{},drawCashType:{}, applyAmountInFen:{},applyTime:{},cfAmountInfoVo:{}",
                caseId, drawCashType, applyAmountInFen, applyTime, JSON.toJSONString(cfAmountInfoVo));


        if (cfAmountInfoVo.getCashLimit() != null
                &&  cfAmountInfoVo.getCashLimit().getHasLimit() == 1
                && applyAmountInFen > cfAmountInfoVo.getCashLimit().getCurLimitAmount()) {
            log.error("案例提现超过限额 caseId:{} limitAmount:{}", caseId, cfAmountInfoVo.getCashLimit().getCurLimitAmount());
            return NewResponseUtil.makeFail("当前最多可提现金额：" + MoneyUtil.buildBalance(cfAmountInfoVo.getCashLimit().getCurLimitAmount()) + "元");
        }

        // 边筹边取-急速提现
        if (drawCashType.equals(NewCfCapitalEnum.DrawCashType.OFFLINE_PAY)) {
            if (applyTime.after(new Date()) || applyTime.after(cfAmountInfoVo.getOrderDeadlineTime())) {
                return NewResponseUtil.makeFail("提现申请时间有误");
            }
            // 边筹边取，只取可提现金额
            if (applyAmountInFen > cfAmountInfoVo.getAllowDrawCashAmountInFen()) {
                return NewResponseUtil.makeFail("提现申请金额有误");
            }
            // 边筹边取-普通提现
        } else if (drawCashType.equals(NewCfCapitalEnum.DrawCashType.OFFLINE_PAY_BY_COMMON)){
            if (applyTime.after(new Date()) || applyTime.after(cfAmountInfoVo.getLastOrderPayTime())) {
                return NewResponseUtil.makeFail("提现申请时间有误");
            }
            // 普通全额提现，取当前能提现的余额
            if (applyAmountInFen > cfAmountInfoVo.getAllowDrawCashSurplusAmountInFen()) {
                return NewResponseUtil.makeFail("提现申请金额有误");
            }

            // 全额提现小于等于极速提现，且全额提现金额与极速提现不等
            if (applyAmountInFen <= cfAmountInfoVo.getAllowDrawCashAmountInFen()
                    && cfAmountInfoVo.getAllowDrawCashAmountInFen() != cfAmountInfoVo.getAllowDrawCashSurplusAmountInFen()) {
                log.warn("非常规提现caseId:{},applyAmountInFen:{},{}", caseId, applyAmountInFen, JSON.toJSONString(cfAmountInfoVo));
            }

        } else {
            return NewResponseUtil.makeFail("提现申请类型有误");
        }
        return NewResponseUtil.makeSuccess("");
    }


}
