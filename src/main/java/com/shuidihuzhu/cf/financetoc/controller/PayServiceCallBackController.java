package com.shuidihuzhu.cf.financetoc.controller;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.financetoc.service.DonationOrderCallbackFacade;
import com.shuidihuzhu.client.baseservice.pay.model.PayInnerCallBack;
import com.shuidihuzhu.client.baseservice.pay.model.v3.combine.callback.CombineInnerCallBack;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 捐款回调
 */
@Controller
@RequestMapping("api/cf/pay")
@RefreshScope
@Slf4j
public class PayServiceCallBackController {

	@Autowired
	private DonationOrderCallbackFacade donationOrderCallbackFacade;

	@RequestMapping(path = "/callback", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@ResponseBody
	public String callback(HttpServletRequest request, HttpServletResponse response) {
		PayInnerCallBack payInnerCallBack = donationOrderCallbackFacade.buildPayCallBackParam(request);
		log.debug("支付成功支付中心回调对象: {}", JSON.toJSONString(payInnerCallBack));
		return donationOrderCallbackFacade.handlePayCallback(payInnerCallBack,0);
	}


	@RequestMapping(path = "/combine/callback", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@ResponseBody
	public String combineCallback(HttpServletRequest request, HttpServletResponse response) {
		CombineInnerCallBack payInnerCallBack = donationOrderCallbackFacade.buildCombineCallBackParam(request);
		return donationOrderCallbackFacade.handleCombinePayCallback(payInnerCallBack,0);
	}

	/**
	 * 独立资助
	 * @param request
	 * @return
	 */
	@PostMapping(path = "/contribute/callback",produces = "application/json;charset=UTF-8")
	@ResponseBody
	public String contributeCallback(HttpServletRequest request) {
		PayInnerCallBack payInnerCallBack = donationOrderCallbackFacade.buildPayCallBackParam(request);
		return donationOrderCallbackFacade.handleContributePayCallback(payInnerCallBack,0);
	}
}
