package com.shuidihuzhu.cf.financetoc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfPayeeInfoChangeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceWriteFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.CfPayeeInfoChange;
import com.shuidihuzhu.cf.finance.model.deposit.CfPayeeDepositAccount;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.financetoc.biz.payee.CrowdfundingInfoPayeeBiz;
import com.shuidihuzhu.cf.financetoc.service.CfDepositCommonService;
import com.shuidihuzhu.cf.financetoc.service.CfPaDepositPayeeAccountService;
import com.shuidihuzhu.cf.financetoc.util.CfIdCardUtil;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.mq.payload.DishonestPeoplePayload;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.common.web.annotation.SessionKeyValidateRequired;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.IdCardUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author: lianghongchao
 * @Date: 2018/7/27 10:54
 */
@Slf4j
@RestController
@RequestMapping(path = "api/cf/v5/change-payee")
@AllArgsConstructor
public class CfChangePayeeInfoController {

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoSimpleBiz;
    @Autowired
    private CfFinanceWriteFeignClient cfFinanceWriteFeignClient;
    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;
    @Autowired
    private CfPaDepositPayeeAccountService cfPaDepositPayeeAccountService;
    @Autowired
    private CfDepositCommonService cfDepositCommonService;
    @Autowired
    private CrowdfundingInfoPayeeBiz crowdfundingInfoPayeeBiz;
    @Autowired
    private Producer producer;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @RequestMapping(path = "/add-or-update-payee-info", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @SessionKeyValidateRequired
    @ApiOperation(value = "保存与修改收款人信息数据,在变更时调用", httpMethod = "POST")
    public Response addOrUpdatePayeeInfo(@ApiParam("收款人变更的类型") @RequestParam(required = true) int changeType,
                                         @ApiParam("变更业务的操作类型") @RequestParam(required = true) int businessType,
                                         @ApiParam("案例的infoUuid") @RequestParam(required = true) String infoUuid,
                                         @ApiParam("收款人与病人关系") @RequestParam(required = false, defaultValue = "8") int relationType,
                                         @ApiParam("收款人姓名") @RequestParam(required = false, defaultValue = "") String collectionName,
                                         @ApiParam("原有收款人姓名") @RequestParam(required = false, defaultValue = "") String originalCollectionName,
                                         @ApiParam("患者姓名") @RequestParam(required = false, defaultValue = "") String patientName,
                                         @ApiParam("患者证件类型") @RequestParam(required = false, defaultValue = "1") int patientIdType,
                                         @ApiParam("患者证件号") @RequestParam(required = false, defaultValue = "") String patientIdCard,
                                         @ApiParam("患者手机号") @RequestParam(required = false, defaultValue = "") String patientMobile,
                                         @ApiParam("收款人手机号") @RequestParam(required = false, defaultValue = "") String collectionMobile,
                                         @ApiParam("证件类型") @RequestParam(required = false, defaultValue = "1") int collectionIdType,
                                         @ApiParam("证件号") @RequestParam(required = false, defaultValue = "") String collectionIdCard,
                                         @ApiParam("更换收款人原因") @RequestParam(required = false, defaultValue = "") String reasons,
                                         @ApiParam("银行名称或者医院对公账户名称") @RequestParam(required = false, defaultValue = "") String bankName,
                                         @ApiParam("开户行信息或者医院开户支行名称") @RequestParam(required = false, defaultValue = "") String bankBranchName,
                                         @ApiParam("银行卡号或者医院对公银行卡号") @RequestParam(required = false, defaultValue = "") String bankCardId,
                                         @ApiParam("科室") @RequestParam(required = false, defaultValue = "") String department,
                                         @ApiParam("床号") @RequestParam(required = false, defaultValue = "") String bedNum,
                                         @ApiParam("住院号") @RequestParam(required = false, defaultValue = "") String hospitalizationNum,
                                         @ApiParam("变更业务的操作状态") @RequestParam(required = false, defaultValue = "0") String status,
                                         @ApiParam("受助人(患者)手持身份证照-1") @RequestParam(required = false, defaultValue = "") String attachIdCard,
                                         @ApiParam("病案首页本人-12") @RequestParam(required = false, defaultValue = "") String attachMedicalRecordHome,
                                         @ApiParam("收款人手持身份证照片-4") @RequestParam(required = false, defaultValue = "") String attachPayeeIdCard,
                                         @ApiParam("收款人和受助人关系证明图片-5") @RequestParam(required = false, defaultValue = "") String attachPayeeRelation,
                                         @ApiParam("患者手持医疗证明照片-6") @RequestParam(required = false, defaultValue = "") String attachTreatmentVerify,
                                         @ApiParam("证明材料图片") @RequestParam(required = false, defaultValue = "[]") String attachmentUrls,
                                         @ApiParam(value = "验证码") @RequestParam(required = false, defaultValue = "") String messageCheckCode,
                                         @ApiParam(value = "三方账号") @RequestParam(required = false, defaultValue = "") String paSubAcctNo,
                                         @ApiParam(value = "银行预留手机号") @RequestParam(required = false, defaultValue = "") String reservedMobile) {
        long userId = ContextUtil.getUserId();
        if (userId <= 0) {
            return NewResponseUtil.makeError(CfErrorCode.CF_USER_ACCOUNT_NO_LOGIN);
        }
        CrowdfundingInfo cfInfoSimpleModel = crowdfundingInfoSimpleBiz.getFundingInfo(infoUuid);
        if (cfInfoSimpleModel == null || userId != cfInfoSimpleModel.getUserId()) {
            return NewResponseUtil.makeResponse(CfErrorCode.CF_SWITCHER_ERROR.getCode(), "案例为空", null);
        }

        List<CrowdfundingAttachment> attachmentList = Lists.newArrayList();
        List<CrowdfundingAttachment> attachment = this.getAttachment(attachIdCard, 1);
        if (CollectionUtils.isNotEmpty(attachment)) {
            attachmentList.addAll(attachment);
        }
        attachment = this.getAttachment(attachMedicalRecordHome, 12);
        if (CollectionUtils.isNotEmpty(attachment)) {
            attachmentList.addAll(attachment);
        }
        attachment = this.getAttachment(attachPayeeIdCard, 4);
        if (CollectionUtils.isNotEmpty(attachment)) {
            attachmentList.addAll(attachment);
        }
        attachment = this.getAttachment(attachPayeeRelation, 5);
        if (CollectionUtils.isNotEmpty(attachment)) {
            attachmentList.addAll(attachment);
        }
        attachment = this.getAttachment(attachTreatmentVerify, 6);
        if (CollectionUtils.isNotEmpty(attachment)) {
            attachmentList.addAll(attachment);
        }
        if (CollectionUtils.isNotEmpty(attachmentList)) {
            crowdfundingInfoPayeeBiz.insertUpdatePayeeImages(cfInfoSimpleModel.getId(), attachmentList);
            attachmentUrls = JSONArray.toJSONString(attachmentList);
        }
        bankCardId = StringUtils.trimToEmpty(bankCardId).replaceAll("\\s+","")
                .replaceAll("–","-");


        if (collectionIdType == UserIdentityType.identity.getCode()) {
            collectionIdCard = CfIdCardUtil.convertLastCharUpper(collectionIdCard);
        }

        log.info("addOrUpdatePayeeInfo   changeType:{}  businessType:{}  infoUuid:{}  relationType:{}" +
                        " collectionName:{}  patientName:{} collectionMobile:{} idType:{} idCard:{} reasons:{}" +
                        " bankName:{} bankBranchName:{} bankCardId:{} department:{} bedNum:{}" +
                        " hospitalizationNum:{} attachmentUrls:{} status:{}",
                changeType, businessType, infoUuid, relationType, collectionName, patientName, collectionMobile, collectionIdType, collectionIdCard, reasons
                , bankName, bankBranchName, bankCardId, department, bedNum, hospitalizationNum, attachmentUrls, status);
        int intStatus = StringUtils.isNumeric(status) ? Integer.parseInt(status) : 0;
        CfPayeeInfoChange cfPayeeInfoChange = new CfPayeeInfoChange(changeType, businessType, infoUuid, relationType,
                collectionName, originalCollectionName, patientName, patientIdType, patientIdCard, collectionMobile,
                patientMobile, collectionIdType, collectionIdCard, reasons, bankName, bankBranchName, bankCardId, department,
                bedNum, hospitalizationNum, attachmentUrls, intStatus);
        // 设置案例id
        cfPayeeInfoChange.setInfoId(cfInfoSimpleModel.getId());

        CfPayeeInfoChangeEnum.BusinessTypeEnum businessTypeEnum = CfPayeeInfoChangeEnum.BusinessTypeEnum.getEnumByCode(businessType);
        CfPayeeInfoChangeEnum.ChangeTypeEnum changeTypeEnum = CfPayeeInfoChangeEnum.ChangeTypeEnum.getEnumByCode(changeType);
        if (null == businessTypeEnum || null == changeTypeEnum) {
            return NewResponseUtil.makeError(CfErrorCode.CF_SWITCHER_ERROR, "变更收款人的类型不在任务范畴内");
        }
        // 仅更换银行卡号  更换收款人为患者本人  更换收款人非患者本人
        List<CfPayeeInfoChangeEnum.ChangeTypeEnum> changeEnumList = Lists.newArrayList(
                CfPayeeInfoChangeEnum.ChangeTypeEnum.CHANGE_BANK_CARD_TO_PATIENT,
                CfPayeeInfoChangeEnum.ChangeTypeEnum.CHANGE_BANK_CARD_TO_NO_PATIENT,
                CfPayeeInfoChangeEnum.ChangeTypeEnum.ONLY_CHANGE_BANK_CARD);

        if (changeEnumList.contains(changeTypeEnum)
                && UserIdentityType.getByCode(collectionIdType) == UserIdentityType.identity
                && !IdCardUtil.illegal(collectionIdCard) && CfIdCardUtil.getCurrentAge(collectionIdCard) < 18) {
            return NewResponseUtil.makeError(CfErrorCode.PAYEE_NOT_ALLOW_OF_AGE);
        }
        // 2019年12月22日14:38:46 添加四要素的校验---银行卡鉴权
        if (changeEnumList.contains(changeTypeEnum) && cfDepositCommonService.isNeedDepositWithAcc(cfInfoSimpleModel.getId())) {
            Response<CfPayeeDepositAccount> accountResponse = cfPaDepositPayeeAccountService.getOpenSuccessAccount(
                    collectionName, bankCardId, collectionIdCard);
            log.info("openWithResponse:{}", JSON.toJSONString(accountResponse));
            if (accountResponse.notOk()) {
                return accountResponse;
            }
        }
        // 发送失信被执行人信息
        try {
            String key = new StringBuilder().append(userId).append(cfInfoSimpleModel.getId()).toString();
            DishonestPeoplePayload dishonestPeoplePayload = new DishonestPeoplePayload(userId,cfInfoSimpleModel.getId(),patientName, shuidiCipher.encrypt(patientIdCard),
                    collectionName,shuidiCipher.encrypt(collectionIdCard),cfInfoSimpleModel.getCreateTime(), 1);
            Message message = new Message(MQTopicCons.CF, MQTagCons.DISHONEST_PEOPLE_HANDLE_MSG, key, JSON.toJSONString(dishonestPeoplePayload));
            producer.send(message);
        }catch (Exception e){
            log.error("收款人失信信息发送失败, ", e);
        }

        try {
            FeignResponse<Response> response = cfFinanceWriteFeignClient.addOrUpdatePayeeInfo(cfPayeeInfoChange, userId);
            if (response == null || response.notOk()) {
                return NewResponseUtil.makeError(CfErrorCode.SYSTEM_ERROR);
            }
            commonOperationRecordClient.create().buildBasicUser(cfInfoSimpleModel.getId(),
                    OperationActionTypeEnum.SUBMIT_MODIFY_PAYEE_CHANNEL).save();
            return response.getData();
        } catch (Exception e) {
            log.error("finance服务异常, ", e);
        }

        return NewResponseUtil.makeError(CfErrorCode.SYSTEM_ERROR);
    }

    private List<CrowdfundingAttachment> getAttachment(String url, int type) {
        if (StringUtils.isEmpty(url) || null == AttachmentTypeEnum.getAttachmentTypeEnum(type)) {
            return Lists.newArrayList();
        }
        String[] urlStrs = url.split(",");

        List<CrowdfundingAttachment> crowdfundingAttachmentList = Lists.newArrayList();
        for (int i = 0; i < urlStrs.length; i++) {
            CrowdfundingAttachment attachment = new CrowdfundingAttachment();
            attachment.setUrl(urlStrs[i]);
            attachment.setType(type);
            crowdfundingAttachmentList.add(attachment);
        }
        return crowdfundingAttachmentList;
    }
}
