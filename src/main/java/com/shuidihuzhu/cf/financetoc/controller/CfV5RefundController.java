package com.shuidihuzhu.cf.financetoc.controller;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.financetoc.biz.order.CfContributeOrderBiz;
import com.shuidihuzhu.cf.financetoc.service.DonationOrderRefundFacade;
import com.shuidihuzhu.cf.model.contribute.ContributeRefundSubmitVo;
import com.shuidihuzhu.common.web.annotation.SessionKeyValidateRequired;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;


/**
 * @author: lixuan
 * @date: 2018/8/1 20:27
 */
@Slf4j
@RestController
@RequestMapping(path = "api/cf/v5/refund", produces = "application/json;charset=UTF-8",
        method = {RequestMethod.POST, RequestMethod.GET})
@RefreshScope
public class CfV5RefundController {

    @Autowired
    private CfContributeOrderBiz contributeOrderBiz;
    @Autowired
    private DonationOrderRefundFacade donationOrderRefundFacade;



    @RequestMapping(path = "/submit-refund-apply", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    @SessionKeyValidateRequired
    @ApiOperation(value = "提交退款订单", httpMethod = "POST")
    public Response<String> submitRefundOrder(@ApiParam(name = "捐款订单id") @RequestParam(name = "orderId", required = false) int orderId,
                                      @ApiParam(name = "捐款订单金额") int amountInFen,
                                      @ApiParam(name = "退款申请原因") String refundReason,
                                      @ApiParam(name = "退款姓名") String refundName,
                                      @ApiParam(name = "退款人手机号") String mobile,
                                      @ApiParam(name = "退款人申请案例id") String infoUuid,
                                      @ApiParam(name = "新版捐款订单id") @RequestParam(name = "newOrderId") Long newOrderId) {

        long userId = ContextUtil.getUserId();
        return donationOrderRefundFacade.submitRefund(userId,
                orderId, amountInFen, refundReason, refundName, mobile, infoUuid, newOrderId);
    }

    @RequestMapping(path = "/submit-contribute-refund-apply", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    @SessionKeyValidateRequired
    @ApiOperation(value = "提交捐赠退款订单", httpMethod = "POST")
    public Response<String> submitRefundOrder(@RequestParam String refundParam) {

        ContributeRefundSubmitVo submitVo = JSON.parseObject(refundParam, ContributeRefundSubmitVo.class);
        submitVo.setUserId(ContextUtil.getUserId());
        String handleMsg = contributeOrderBiz.submitRefundApply(submitVo);

        return StringUtils.isBlank(handleMsg) ? NewResponseUtil.makeSuccess("") :
                NewResponseUtil.makeFail(handleMsg);
    }
}
