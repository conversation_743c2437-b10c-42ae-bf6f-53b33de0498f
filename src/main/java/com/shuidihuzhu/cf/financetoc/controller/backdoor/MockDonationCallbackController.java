package com.shuidihuzhu.cf.financetoc.controller.backdoor;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.financetoc.biz.order.CrowdfundingOrderBiz;
import com.shuidihuzhu.cf.financetoc.biz.order.CrowdfundingPayRecordBiz;
import com.shuidihuzhu.cf.financetoc.service.DonationOrderCallbackFacade;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord;
import com.shuidihuzhu.client.baseservice.pay.model.PayInnerCallBack;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2018-08-28  21:06
 */
@RestController
@RequestMapping("innerapi/cf/mock/donation")
@Slf4j
@Profile("!production")
public class MockDonationCallbackController {

    @Resource
    private DonationOrderCallbackFacade donationOrderCallbackFacade;
    @Autowired
    private CrowdfundingPayRecordBiz crowdfundingPayRecordBiz;
    @Autowired
    private CrowdfundingOrderBiz crowdfundingOrderBiz;

    @ApiOperation(value = "mock 捐款回调", notes = "{\"realPayType\":61,\"amountInFen\":10000000,\"openId\":\"dbd89da4db454600b63f59f982398e7a\",\"payStatus\":2,\"orderId\":\"[tradeNo](取下单接口返回的此字段值)\",\"businessTime\":\"2019-02-28 17:50:37\"}")
    @PostMapping("callback")
    public String callback(
            @RequestParam Integer realPayType,
            @RequestParam Long amountInFen,
            @RequestParam(required = false) String openId,
            @RequestParam Integer payStatus,
            @RequestParam String orderId,
            @RequestParam String businessTime,
            HttpServletRequest request) {
        PayInnerCallBack payInnerCallBack = this.filterTestErrorParam(donationOrderCallbackFacade.buildMockPayCallBackParam(request));
        BigDecimal rate = new BigDecimal("0.006");
        payInnerCallBack.setRate(rate);
        payInnerCallBack.setFee(BigDecimal.valueOf(amountInFen).multiply(rate).setScale(0, RoundingMode.HALF_UP).intValue());
        log.debug("mock 捐款回调 payInnerCallBack: {}", JSON.toJSONString(payInnerCallBack));
        return donationOrderCallbackFacade.handlePayCallback(payInnerCallBack, 0);
    }


    /**
     * 过滤测试环境的非法下单参数
     *
     * @param payInnerCallBack
     * @return
     */
    private PayInnerCallBack filterTestErrorParam(PayInnerCallBack payInnerCallBack) {
        if (payInnerCallBack == null) {
            return null;
        }
        String payUid = payInnerCallBack.getOrderId();

        CrowdfundingPayRecord payRecord = crowdfundingPayRecordBiz.getByPayUid(payUid);
        if (payRecord == null) {
            log.error("crowdfunding-payService callback pay record error, payUid:{}", payUid);
            return null;
        }
        CrowdfundingOrder order = crowdfundingOrderBiz.getById(payRecord.getCrowdfundingOrderId());
        if (order == null) {
            log.error("crowdfunding-payService callback order error, payUid:{}, orderId:{}", payUid,
                    payRecord.getCrowdfundingOrderId());
            return null;
        }

        if (order.getAmount() != payInnerCallBack.getAmountInFen()) {
            log.error("测试环境回调下单金额与回调金额不符.{},{},{}", order.getId(),
                    order.getAmount(), payInnerCallBack.getAmountInFen());
            return null;
        }

        if (payInnerCallBack.getBusinessTime() == null || payInnerCallBack.getBusinessTime().before(order.getCtime())) {
            log.error("测试环境回调下单时间异常.{},{},{}", order.getId(),
                    order.getCtime(), payInnerCallBack.getBusinessTime());
            return null;
        }
        payInnerCallBack.setPayUid("mock_" + System.nanoTime());

        return payInnerCallBack;
    }

}