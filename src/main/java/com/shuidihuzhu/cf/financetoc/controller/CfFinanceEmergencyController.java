package com.shuidihuzhu.cf.financetoc.controller;

import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.finance.client.model.CheckEmergencyParam;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.financetoc.service.CrowdfundingInfoService;
import com.shuidihuzhu.cf.model.contribute.ContributeAddOrderDTO;
import com.shuidihuzhu.cf.model.crowdfunding.vo.OrderResultVo;
import com.shuidihuzhu.common.web.annotation.SessionKeyValidateRequired;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2025/6/9 14:51
 */
@Slf4j
@RestController
@RequestMapping(path = "api/cf/emergency")
public class CfFinanceEmergencyController {

    @Resource
    private CrowdfundingInfoService crowdfundingInfoService;

    @PostMapping(path = "/check-emergency-error")
    @SessionKeyValidateRequired
    public Response<Void> checkEmergencyError(@RequestBody CheckEmergencyParam checkEmergencyParam) {
        CfErrorCode cfErrorCode = crowdfundingInfoService.checkEmergencyError(
                checkEmergencyParam.getInfoId(),
                checkEmergencyParam.getEmergency(),
                checkEmergencyParam.getEmergencyPhone());
        if (cfErrorCode != CfErrorCode.SUCCESS) {
            return NewResponseUtil.makeError(cfErrorCode);
        }

        return NewResponseUtil.makeSuccess();
    }

}
