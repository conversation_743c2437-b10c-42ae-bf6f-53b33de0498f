package com.shuidihuzhu.cf.financetoc.model.springevent;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = false)
@Getter
@Setter
@ToString
public class OrderAddFinanceEvent extends ApplicationEvent {

    private final String channel;
    private int caseId;
    private long userId;
    private Long orderId;
    private String infoUuid;

    public OrderAddFinanceEvent(Object source, int caseId, long userId, Long orderId, String channel, String infoUuid) {
        super(source);
        this.caseId = caseId;
        this.userId = userId;
        this.orderId = orderId;
        this.channel = channel;
        this.infoUuid = infoUuid;
    }
}
