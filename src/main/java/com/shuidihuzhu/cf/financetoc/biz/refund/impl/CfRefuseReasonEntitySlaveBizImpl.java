package com.shuidihuzhu.cf.financetoc.biz.refund.impl;

import com.shuidihuzhu.cf.financetoc.biz.refund.CfRefuseReasonEntitySlaveBiz;
import com.shuidihuzhu.cf.financetoc.dao.refund.CfRefuseReasonEntitySlaveDao;
import com.shuidihuzhu.cf.model.crowdfunding.casematerial.CfRefuseReasonEntitySlave;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

@Service
public class CfRefuseReasonEntitySlaveBizImpl implements CfRefuseReasonEntitySlaveBiz {
    @Autowired
    private CfRefuseReasonEntitySlaveDao cfRefuseReasonEntityDao;

    @Override
    public List<CfRefuseReasonEntitySlave> selectByReasonIds(Set<Integer> set) {
        if (CollectionUtils.isEmpty(set)) {
            return Collections.emptyList();
        }
        List<CfRefuseReasonEntitySlave> cfRefuseReasonItemMaps = cfRefuseReasonEntityDao.selectByReasonIds(set);
        return cfRefuseReasonItemMaps != null ? cfRefuseReasonItemMaps : Collections.emptyList();
    }

}
