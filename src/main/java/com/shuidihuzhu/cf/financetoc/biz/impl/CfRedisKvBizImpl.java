package com.shuidihuzhu.cf.financetoc.biz.impl;

import com.shuidihuzhu.cf.constants.crowdfunding.RedisKeyCons;
import com.shuidihuzhu.cf.financetoc.biz.CfRedisKvBiz;
import com.shuidihuzhu.cf.financetoc.dao.CfRedisKvDao;
import com.shuidihuzhu.cf.financetoc.service.AbstractCfCache;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.cache.ICache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Created by wangsf on 17/2/16.
 */
@Slf4j
@Service
public class CfRedisKvBizImpl implements CfRedisKvBiz {
    @Autowired
    private CfRedisKvDao redisKvDao;

    @Autowired
    private CfRedisLocalCache redisLocalCache;

    @Resource(name = "cfUGCRedissonHandler")
    private RedissonHandler redissonHandler;

    private String queryValueByKey(String key) {
        return redisKvDao.queryValueByKey(key);
    }

    @Override
    public String queryByKey(String key, boolean useCache) {
        if (!useCache) {
            return queryValueByKey(key);
        }

        return getValueFromCache(key);
    }

    private String getValueFromCache(String key) {
        try {
            if (redissonHandler == null) {
                log.warn("redissonClient is null");
                return queryValueByKey(key);
            }

            String value = redissonHandler.get(getRedisKey(key), String.class);
            if (null == value) {
                value = queryValueByKey(key);
                if (null != value) {
                    updateRedisKvCache(key, value);
                }
            }

            return value;
        } catch (Exception e) {
            log.info("get value from redisKv cache error", e);
        }
        return null;
    }

    private void updateRedisKvCache(String key, String value) {
        try {
            redissonHandler.setEX(getRedisKey(key), value, RedisKeyCons.CF_REDIS_KV_TIME);
        } catch (Exception e) {
            log.info("update redisKv cache error, key={}, value={}", key, value, e);
        }
    }

    private static String getRedisKey(String key) {
        return RedisKeyCons.CF_REDIS_KV.replace("#key#", key);
    }


    /**
     * 爱心筹k-v配置表本地缓存
     */
    @Service
    private static class CfRedisLocalCache extends AbstractCfCache<String, String> implements ICache<String, String> {

        @Resource(name = "cfUGCRedissonHandler")
        private RedissonHandler redissonHandler;
        @Autowired
        private CfRedisKvDao redisKvDao;

        @Override
        protected String queryData(String key) {
            log.debug("CfRedisKvBizImpl localCache queryFromRedis key={}", key);
            if (StringUtils.isEmpty(key)) {
                return "";
            }
            return getValueFromCache(key);
        }

        @Override
        public String get(String s) {
            if (StringUtils.isEmpty(s)) {
                return "";
            }

            try {
                return getValue(s);
            } catch (Exception e) {
                log.error("CfRedisKvBizImpl localCache queryFromRedis key={}", s, e);
                return "";
            }
        }

        @Override
        protected int getExpireTimeInSeconds() {
            return 60;
        }

        @Override
        protected int getRefreshTimeAfterWrite() {
            return 60;
        }


        private String getValueFromCache(String key) {
            try {
                if (redissonHandler == null) {
                    log.warn("redissonClient is null");
                    return queryValueByKey(key);
                }

                String value = redissonHandler.get(getRedisKey(key), String.class);
                if (null == value) {
                    value = queryValueByKey(key);
                    if (null != value) {
                        updateRedisKvCache(getRedisKey(key), value);
                    }
                }

                log.debug("CfRedisKvBizImpl getValueFromCache key={}\tvalue={}\t", key, value);

                return value;

            } catch (Exception e) {
                log.info("get value from redisKv cache error", e);
            }
            return null;
        }

        private String queryValueByKey(String key) {
            return redisKvDao.queryValueByKey(key);
        }

        private void updateRedisKvCache(String key, String value) {
            try {
                redissonHandler.setEX(getRedisKey(key), value, RedisKeyCons.CF_REDIS_KV_TIME);
            } catch (Exception e) {
                log.info("update redisKv cache error, key={}, value={}", key, value, e);
            }
        }
    }
}
