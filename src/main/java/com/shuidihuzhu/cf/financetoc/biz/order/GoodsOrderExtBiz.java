package com.shuidihuzhu.cf.financetoc.biz.order;

import com.shuidihuzhu.cf.model.goods.GoodsOrderExt;
import com.shuidihuzhu.common.web.model.Response;

public interface GoodsOrderExtBiz {

	Response save(String infoUuid, long orderId, long userAddressId, long gearId, int amountInFen, int goodsCount);

	/**
	 * 刷数
	 */
	Response updateNum(long orderId, long gearId, int goodsCount);

	GoodsOrderExt getByOrderId(long orderId);

	int updateSuccessPayStatus(long orderId);
}