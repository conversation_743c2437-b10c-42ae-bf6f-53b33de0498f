package com.shuidihuzhu.cf.financetoc.biz.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.BankCardVerifyStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;

import java.util.Date;
import java.util.List;

/**
 * Created by lgj on 16/6/21.
 */
public interface CrowdfundingInfoBiz {

    int updateRelationType(CrowdfundingInfo crowdfundingInfo);

    int updatePayeeInfo(CrowdfundingInfo crowdfundingInfo);

    CrowdfundingInfo getFundingInfo(String infoId);

    CrowdfundingInfo getFundingInfoById(Integer id);

    boolean isRechargeable(CrowdfundingInfo crowdfundingInfo);

    // 更新结束时间
    int updateEndTime(int id, Date endTime);

    List<CrowdfundingInfo> selectByUserId(long userId);

    int updateVerifyStatus(int id, BankCardVerifyStatus verifyStatus,
                           String bankCardVerifyMessage,
                           String bankCardVerifyMessage2);

    int addAmount(int id, int amount);

    void transferCf(long fromUserId, long toUserId, int caseId);

}
