package com.shuidihuzhu.cf.financetoc.biz.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;

import java.util.List;
import java.util.Map;

/**
 * Created by lgj on 16/6/21.
 */
public interface CrowdfundingAttachmentBiz {

	Map<AttachmentTypeEnum, List<CrowdfundingAttachmentVo>> getFundingAttachmentMap(int parentId);

    int add(List<CrowdfundingAttachment> crowdfundingAttachmentList);

	int deleteByParentIdAndType(int parentId, List<AttachmentTypeEnum> typeList);
}