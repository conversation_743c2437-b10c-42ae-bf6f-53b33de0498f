package com.shuidihuzhu.cf.financetoc.biz.refund.impl;

import com.shuidihuzhu.cf.financetoc.biz.refund.CfRefuseReasonMsgBiz;
import com.shuidihuzhu.cf.financetoc.dao.refund.CfRefuseReasonMsgDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by Ahrievil on 2017/9/20
 */
@Service
@Slf4j
public class CfRefuseReasonMsgBizImpl implements CfRefuseReasonMsgBiz {

    @Autowired
    private CfRefuseReasonMsgDao cfRefuseReasonMsgDao;
    @Override
    public CfRefuseReasonMsg selectByInfoUuidAndType(String infoUuid, int type) {
        return cfRefuseReasonMsgDao.selectByInfoUuidAndType(infoUuid, type);
    }
}
