package com.shuidihuzhu.cf.financetoc.biz.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingOperationEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation;

public interface CrowdfundingOperationBiz {

	int add(CrowdfundingOperation crowdfundingOperation);

	int update(CrowdfundingOperation crowdfundingOperation);

	int update(String infoId, CrowdfundingOperationEnum operationEnum, int operatorId, String reason);

	CrowdfundingOperation getByInfoId(String infoId);
}
