package com.shuidihuzhu.cf.financetoc.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CfInfoBlessingBiz;
import com.shuidihuzhu.cf.financetoc.dao.crowdfunding.CfInfoBlessingDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoBlessing;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Author: <PERSON>
 * Date: 2017/6/23 16:02
 */
@Service
public class CfInfoBlessingBizImpl implements CfInfoBlessingBiz {

	@Autowired
	CfInfoBlessingDao cfInfoBlessingDao;

	@Override
	public List<CfInfoBlessing> selectBlessByUserId(long userId) {
		return cfInfoBlessingDao.selectBlessByUserId(userId);
	}

	@Override
	public int updateUserIdByIds(List<Integer> ids, long toUserId) {
		if (CollectionUtils.isEmpty(ids)) {
			return 0;
		}
		return cfInfoBlessingDao.updateUserIdByIds(ids, toUserId);
	}

	public boolean checkParams(CfInfoBlessing record) {
		if (StringUtils.isEmpty(record.getSelfTag()) ||
		    StringUtils.isEmpty(record.getInfoUuid())) {
			return false;
		}
		if (record.getUserId() < 0) {
			record.setUserId(0);
		}
		return true;
	}

}
