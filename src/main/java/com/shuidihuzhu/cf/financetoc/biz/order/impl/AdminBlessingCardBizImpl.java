package com.shuidihuzhu.cf.financetoc.biz.order.impl;

import com.shuidihuzhu.cf.financetoc.biz.order.AdminBlessingCardBiz;
import com.shuidihuzhu.cf.financetoc.dao.order.CfBlessingCardOrderDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/11/25  4:16 下午
 */
@Service
public class AdminBlessingCardBizImpl implements AdminBlessingCardBiz {

    @Autowired
    private CfBlessingCardOrderDao cfBlessingCardOrderDao;

    @Override
    public int insert(long orderId, int blessingCardId,int anonymousValid) {
        return cfBlessingCardOrderDao.insert(orderId, blessingCardId,anonymousValid);
    }
}
