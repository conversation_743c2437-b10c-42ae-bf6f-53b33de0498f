package com.shuidihuzhu.cf.financetoc.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/10/18.
 */
public interface CrowdFundingVerificationBiz {

	List<CrowdFundingVerification> queryByVerifyUserId(long verifyUserId);

	int updateVerifyUserId(long id, long verifyUserId);
}
