package com.shuidihuzhu.cf.financetoc.biz.order.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.financetoc.biz.order.AdminBlessingCardBiz;
import com.shuidihuzhu.cf.financetoc.biz.order.CrowdfundingOrderBiz;
import com.shuidihuzhu.cf.financetoc.dao.order.CrowdfundingOrderExtNewShardingDao;
import com.shuidihuzhu.cf.financetoc.dao.order.CrowdfundingOrderShardingCrowdfundingIdNewDao;
import com.shuidihuzhu.cf.financetoc.dao.order.CrowdfundingOrderShardingIdDao;
import com.shuidihuzhu.cf.financetoc.dao.order.CrowdfundingOrderShardingUserIdDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrderExt;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by lgj on 16/6/21.
 */
@Slf4j
@Service
@RefreshScope
public class CrowdfundingOrderBizImpl extends CrowdfundingOrderShardingBizImpl implements CrowdfundingOrderBiz {

    @Autowired
    private CrowdfundingOrderShardingIdDao crowdfundingOrderShardingIdDao;
    @Autowired
    private CrowdfundingOrderShardingUserIdDao crowdfundingOrderShardingUserIdDao;
    @Autowired
    private CrowdfundingOrderShardingCrowdfundingIdNewDao crowdfundingOrderShardingCrowdfundingIdNewDao;
    @Autowired
    private AdminBlessingCardBiz adminBlessingCardBiz;
    @Autowired
    private CrowdfundingOrderExtNewShardingDao crowdfundingOrderExtNewShardingDao;

    private static final DateTimeFormatter DATE_TIME_FORMAT = DateTimeFormat.forPattern("yyyyMM");

    private static String getOrderExtSharding(Date ctime) {
        return DATE_TIME_FORMAT.print(ctime.getTime());
    }

    @Override
    public void addOrderNewInstance(CrowdfundingOrder order, String splitFlowUuid, String shareSourceId, int cfStatus, int DataStatus,int blessingCardId) {
        CrowdfundingOrderExt crowdfundingOrderExt = new CrowdfundingOrderExt();
        //  双写
        try {
            crowdfundingOrderShardingCrowdfundingIdNewDao.addOrder(order);
            crowdfundingOrderShardingIdDao.addCrowdfundingOrderShardingModel(order);
            crowdfundingOrderShardingUserIdDao.addCrowdfundingOrderShardingModelBatch(order.getShardingUserIdTableNameSuffix(), Lists.newArrayList(order));
        } catch (Exception e) {
            AlarmBotService.sentTextV2("fdde176e-0c08-4cf1-96b3-aa62bb270e06",
                    "addOrderNewInstance写入分表有问题", "order=" + JSONObject.toJSONString(order) + "\n" + e.getMessage(), new String[]{"zouyu11"});
            log.error("addOrderNewInstance写入分表有问题,order={};", order, e);
        }
        //  双写 end
        try {
            crowdfundingOrderExt.setOrderId(order.getId());
            crowdfundingOrderExt.setAuditStatus(cfStatus);
            crowdfundingOrderExt.setDataStatus(DataStatus);
            crowdfundingOrderExt.setCtime(order.getCtime());
            crowdfundingOrderExt.setSplitFlowUuid(StringUtils.trimToEmpty(splitFlowUuid));
            crowdfundingOrderExt.setShareSourceId(StringUtils.trimToEmpty(shareSourceId));
            crowdfundingOrderExtNewShardingDao.add(crowdfundingOrderExt, getOrderExtSharding(order.getCtime()));
        } catch (Exception e) {
            log.error("crowdfundingOrderExtDaoAddError,crowdfundingOrderExt={};", JSONObject.toJSONString(crowdfundingOrderExt), e);
        }

        try {
            if (blessingCardId != 0) {
                adminBlessingCardBiz.insert(order.getId(), blessingCardId,0);
            }
        } catch (Exception e) {
            log.error("adminBlessingCardBiz,order:{} blessingCardId:{};", order.getId(), blessingCardId, e);
        }
    }


    @Override
    public CrowdfundingOrder getById(Long id) {
        return getByIdFromSharding(id);
    }

    @Override
    public List<CrowdfundingOrder> getByUserId(Set<Long> userIds, Integer crowdfundingId) {
        if(CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }
        return this.getByUserIdFromSharding(userIds, crowdfundingId);
    }

    @Override
    public List<CrowdfundingOrder> getListByUserId(int crowdfundingId, long userId, int limit) {
        return this.getListByUserIdFromSharding(crowdfundingId, userId, limit);
    }


    @Override
    public CrowdfundingOrderExt getCrowdfundingOrderExtByOrderId(long orderId, Date ctime) {
        if (Objects.isNull(ctime)) {
            return null;
        }
        return crowdfundingOrderExtNewShardingDao.getByOrderId(orderId, getOrderExtSharding(ctime));
    }

    private List<CrowdfundingOrder> getListByUserIdFromSharding(int crowdfundingId, long userId, int limit) {
        if (crowdfundingId <= 0 || userId <= 0) {
            return Lists.newArrayList();
        }
        return crowdfundingOrderShardingCrowdfundingIdNewDao.getListByUserId(crowdfundingId, userId, limit);
    }

    /**
     * 订单资产转移
     * CfOrderTransferMQListner
     *
     * @param orders
     * @param fromUserId
     */
    @Override
    public void updateUserIdBatch(List<CrowdfundingOrder> orders, long fromUserId) {
        if(CollectionUtils.isEmpty(orders)) {
            return;
        }
        try {
            for (CrowdfundingOrder order : orders) {
                crowdfundingOrderShardingCrowdfundingIdNewDao.updateUserId(order);
            }
            crowdfundingOrderShardingUserIdDao.deleteByUserIdWithOrderIds(fromUserId,
                    orders.stream().map(CrowdfundingOrder::getId)
                            .collect(Collectors.toList())
            );
            List<Long> existIdByIds = crowdfundingOrderShardingUserIdDao.getExistIdByIds(
                    orders.get(0).getShardingUserIdTableNameSuffix(),
                    orders.stream().map(CrowdfundingOrder::getId)
                            .collect(Collectors.toList())
            );
            List<CrowdfundingOrder> needAddOrder = orders.stream()
                    .filter(crowdfundingOrder -> !existIdByIds.contains(crowdfundingOrder.getId()))
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(needAddOrder)) {
                crowdfundingOrderShardingUserIdDao.addCrowdfundingOrderShardingModelBatch(
                        orders.get(0).getShardingUserIdTableNameSuffix(),
                        needAddOrder);
            }
        } catch (Exception e) {
            log.error("newUpdateUserIdBatchError orders:{},fromUserId:{};", orders, fromUserId, e);
        }

    }
}
