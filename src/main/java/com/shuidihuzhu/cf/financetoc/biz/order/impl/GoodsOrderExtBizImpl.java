package com.shuidihuzhu.cf.financetoc.biz.order.impl;

import com.google.common.base.Strings;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.financetoc.biz.order.GoodsOrderExtBiz;
import com.shuidihuzhu.cf.financetoc.dao.GoodsGearDao;
import com.shuidihuzhu.cf.financetoc.dao.order.GoodsOrderExtDao;
import com.shuidihuzhu.cf.model.goods.GoodsGear;
import com.shuidihuzhu.cf.model.goods.GoodsOrderExt;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@SuppressWarnings({"all"})
@Service
public class GoodsOrderExtBizImpl implements GoodsOrderExtBiz {
    
    private final String redisMapKey = "crowdfunding-goods-gear-";

    @Autowired
    private GoodsOrderExtDao goodsOrderExtDao;
    @Autowired
    private GoodsGearDao goodsGearDao;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    private void putCache(long orderId, long gearId, int goodsCount) {
        try {
            log.info("putCache orderId:{};gearId:{}", orderId, gearId);
            redissonHandler.putToMapCache(redisMapKey + gearId, redisMapKey + gearId + "-" + orderId,
                    goodsCount, 60L);
        } catch (Exception e) {
            log.error("", e);
        }
    }

    private int getCacheNum(long gearId) {
        int num = 0;
        try {
            Map<String, Integer> orderCountMap = redissonHandler.getMapCacheByKey(redisMapKey + gearId, Integer.class);
            if (orderCountMap == null) {
                return 0;
            }

            for (Map.Entry<String, Integer> entry : orderCountMap.entrySet()) {
                num += (entry.getValue() == null ? 0 : entry.getValue().intValue());
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return num;
    }

    @Override
    public Response save(String infoUuid, long orderId, long userAddressId, long gearId, int amountInFen, int goodsCount) {
        log.info("GoodsOrderExtBizImpl save infoUuid:{};orderId:{};userAddressId:{};gearId:{};goodsCount:{};", infoUuid, orderId,
                userAddressId, gearId, goodsCount);
        GoodsGear goodsGear = this.goodsGearDao.getById(gearId);
        // 下单前判断是否已经筹满
        if (goodsGear != null) {
            if (amountInFen < goodsGear.getAmount()) {
                return NewResponseUtil.makeError(CfErrorCode.GOODS_GEAR_AMOUNT_LIMIT);
            }
            if (goodsGear.getHasLimit() == 1) {
                if (goodsGear.getNum() + getCacheNum(gearId) >= goodsGear.getTargetNum()) {
                    return NewResponseUtil.makeError(CfErrorCode.GOODS_GEAR_LIMIT);
                }
                putCache(orderId, gearId, goodsCount);
            }
        }

        GoodsOrderExt goodsOrderExt = new GoodsOrderExt();
        goodsOrderExt.setInfoUuid(infoUuid);
        goodsOrderExt.setOrderId(orderId);
        goodsOrderExt.setGearId(gearId);
        goodsOrderExt.setGoodsCount(goodsCount);

        if (!Strings.isNullOrEmpty(goodsOrderExt.getAddress())) {
            goodsOrderExt.setEncryptAddress(oldShuidiCipher.aesEncrypt(goodsOrderExt.getAddress()));
        } else {
            goodsOrderExt.setEncryptAddress("");
        }
        if (!Strings.isNullOrEmpty(goodsOrderExt.getName())) {
            goodsOrderExt.setEncryptName(oldShuidiCipher.aesEncrypt(goodsOrderExt.getName()));
        } else {
            goodsOrderExt.setEncryptName("");
        }
        if (!Strings.isNullOrEmpty(goodsOrderExt.getMobile())) {
            goodsOrderExt.setEncryptMobile(oldShuidiCipher.aesEncrypt(goodsOrderExt.getMobile()));
        } else {
            goodsOrderExt.setEncryptMobile("");
        }
        if (!Strings.isNullOrEmpty(goodsOrderExt.getEmail())) {
            goodsOrderExt.setEncryptEmail(oldShuidiCipher.aesEncrypt(goodsOrderExt.getEmail()));
        } else {
            goodsOrderExt.setEncryptEmail("");
        }
        this.goodsOrderExtDao.save(goodsOrderExt);
        return ResponseUtil.makeSuccess(null);
    }

    @Override
    public Response updateNum(long orderId, long gearId, int goodsCount) {
        if (gearId <= 0L) {
            return NewResponseUtil.makeError(CfErrorCode.GOODS_NOT_EXISTS);
        }

        int result = this.goodsGearDao.updateNum(gearId, goodsCount);
        if (result <= 0) {
            return NewResponseUtil.makeError(CfErrorCode.GOODS_GEAR_LIMIT);
        }
        deleteCacheNum(orderId, gearId);
        return ResponseUtil.makeSuccess(null);
    }

    private void deleteCacheNum(long orderId, long gearId) {
        try {
            redissonHandler.delFromMapCache(redisMapKey + gearId, redisMapKey + gearId + "-" + orderId);
        } catch (Exception e) {
            log.error("", e);
        }
    }

    @Override
    public GoodsOrderExt getByOrderId(long orderId) {
        if (orderId <= 0) {
            return null;
        }
        return goodsOrderExtDao.get(orderId);
    }

    @Override
    public int updateSuccessPayStatus(long orderId) {
        return this.goodsOrderExtDao.updateSuccessPayStatus(orderId);
    }

}
