package com.shuidihuzhu.cf.financetoc.biz.order.impl;

import com.shuidihuzhu.cf.enums.crowdfunding.CfPropertyTransferEnum;
import com.shuidihuzhu.cf.financetoc.biz.order.CfOrderTransferHistoryBiz;
import com.shuidihuzhu.cf.financetoc.dao.order.CfOrderTransferHistoryDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfPropertyTransferHistory;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by chao on 2017/10/13.
 */
@Service
public class CfOrderTransferHistoryBizImpl implements CfOrderTransferHistoryBiz {
    @Autowired
    private CfOrderTransferHistoryDao cfPropertyTransferHistoryDao;

    @Override
    public List<CfPropertyTransferHistory> get(long fromUserId, long toUserId, long bizId, CfPropertyTransferEnum type) {
        return cfPropertyTransferHistoryDao.get(fromUserId, toUserId, bizId, type.getCode());
    }

    @Override
    public void addBatch(List<CfPropertyTransferHistory> historyList) {
        if (CollectionUtils.isEmpty(historyList)) {
            return;
        }
        cfPropertyTransferHistoryDao.addBatch(historyList);
    }
}