package com.shuidihuzhu.cf.financetoc.biz.payee.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.enums.crowdfunding.HonestPersonChangeEnum;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingAttachmentBiz;
import com.shuidihuzhu.cf.financetoc.biz.payee.CrowdfundingInfoPayeeBiz;
import com.shuidihuzhu.cf.financetoc.dao.crowdfunding.CrowdfundingInfoDao;
import com.shuidihuzhu.cf.financetoc.dao.payee.CrowdfundingInfoPayeeDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoPayeeVo;
import com.shuidihuzhu.client.cf.admin.client.CfRecordClient;
import com.shuidihuzhu.client.cf.api.model.DishonestPayload;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class CrowdfundingInfoPayeeBizImpl implements CrowdfundingInfoPayeeBiz {

    @Autowired(required = false)
    private Producer producer;
    @Autowired
    private CrowdfundingInfoDao crowdfundingInfoDao;

	@Autowired
	private CrowdfundingInfoPayeeDao crowdfundingInfoPayeeDao;
    @Autowired
    private CrowdfundingAttachmentBiz crowdfundingAttachmentBiz;
    @Autowired
    private CfRecordClient adminRelationClient;

	private void sendDishonestValidateMQ(CrowdfundingInfoPayee crowdfundingInfoPayee){
	    if(Objects.isNull(crowdfundingInfoPayee)){
	        return;
        }
	    try {
            String infoUuid = crowdfundingInfoPayee.getInfoUuid();
            DishonestPayload dishonestPayload = new DishonestPayload();
            dishonestPayload.setInfoUuid(infoUuid);
            dishonestPayload.setChangeType(HonestPersonChangeEnum.PAYEE.getKey());

            producer.send(new Message<>(MQTopicCons.CF,
                    MQTagCons.CF_DISHONEST_VALIDATE_MQ,
                    MQTagCons.CF_DISHONEST_VALIDATE_MQ + "-" + infoUuid + "-" + System.currentTimeMillis(),
                    dishonestPayload,
                    DelayLevel.S1));
        } catch (Exception e){
            log.error("send dishonest validate mq Exception", e);
        }
    }

	@Override
	public int add(CrowdfundingInfoPayee crowdfundingInfoPayee) {
        sendDishonestValidateMQ(crowdfundingInfoPayee);
        if (crowdfundingInfoPayee.getCaseId() == 0) {
            CrowdfundingInfo fundingInfo = crowdfundingInfoDao.getFundingInfo(crowdfundingInfoPayee.getInfoUuid());
            crowdfundingInfoPayee.setCaseId(fundingInfo.getId());
        }
		return this.crowdfundingInfoPayeeDao.add(crowdfundingInfoPayee);
	}

	@Override
	public int update(CrowdfundingInfoPayee crowdfundingInfoPayee) {
        sendDishonestValidateMQ(crowdfundingInfoPayee);
		return this.crowdfundingInfoPayeeDao.update(crowdfundingInfoPayee);
	}

	@Override
	public CrowdfundingInfoPayee getByInfoUuid(String infoUuid) {
		if (StringUtils.isBlank(infoUuid)) {
			return null;
		}
		return this.crowdfundingInfoPayeeDao.getByInfoUuid(infoUuid);
	}

    @Override
    public boolean checkCanSubmitRelationVideo(CrowdfundingInfoPayeeVo payeeVo)  {

	    if (payeeVo == null) {
	        return true;
        }

	    if (payeeVo.getRelationType() != CrowdfundingRelationType.spouse
                && payeeVo.getRelationType() != CrowdfundingRelationType.other) {
	        return CollectionUtils.isEmpty(payeeVo.getRelationVideos());
        }

	    // 当选择的关系是配偶和近亲属时 判断用户是否可以提交视频
	    if (CollectionUtils.isNotEmpty(payeeVo.getRelationVideos())) {
            Response<Boolean> result = adminRelationClient.canSubmitPayeeRelationVideo(payeeVo.getInfoUuid());
            log.info("判断用户是否可以提交收款人关系视频.infoUuid:{} result:{}", payeeVo.getInfoUuid(), JSON.toJSONString(result));

            return result != null && result.getData() != null && result.getData();
        }

	    return true;
    }


    @Override
    public void insertUpdatePayeeImages(int caseId, List<CrowdfundingAttachment> attachmentList) {
	    if (CollectionUtils.isEmpty(attachmentList)) {
	        return;
        }

        List<CrowdfundingAttachment> addAttachments = Lists.newArrayList();
	    for (CrowdfundingAttachment source : attachmentList) {
            CrowdfundingAttachment target = new CrowdfundingAttachment();
            BeanUtils.copyProperties(source, target);
            target.setParentId(caseId);
            target.setType(AttachmentTypeEnum.ATTACH_MODIFY_STAGE_PAYEE_INFO);
            addAttachments.add(target);
        }

        crowdfundingAttachmentBiz.add(addAttachments);
    }


}
