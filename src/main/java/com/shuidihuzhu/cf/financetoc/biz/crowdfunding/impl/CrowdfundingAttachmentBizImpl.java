package com.shuidihuzhu.cf.financetoc.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingAttachmentBiz;
import com.shuidihuzhu.cf.financetoc.dao.crowdfunding.CrowdfundingAttachmentDao;
import com.shuidihuzhu.cf.financetoc.service.IOssToCosTransService;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by wuxinlong on 6/21/16.
 */
@Slf4j
@Service
public class CrowdfundingAttachmentBizImpl implements CrowdfundingAttachmentBiz {

	@Autowired
	private IOssToCosTransService ossToCosTransService;

	@Autowired(required = false)
	private Producer producer;

	@Autowired
	private CrowdfundingAttachmentDao crowdfundingAttachmentDao;

	@Override
	public Map<AttachmentTypeEnum, List<CrowdfundingAttachmentVo>> getFundingAttachmentMap(int parentId) {
		return mapAttachments(crowdfundingAttachmentDao.getFundingAttachment(parentId));
	}

	@Override
	public int add(List<CrowdfundingAttachment> crowdfundingAttachmentList) {
		if (CollectionUtils.isEmpty(crowdfundingAttachmentList)) {
			return 0;
		}
		int result = crowdfundingAttachmentDao.add(crowdfundingAttachmentList);
		if(result > 0){
			try {
				int attaId = CollectionUtils.isNotEmpty(crowdfundingAttachmentList) ? crowdfundingAttachmentList.get(0).getId() : 0;
				MessageResult msgResult = producer.send(new Message(MQTopicCons.CF, MQTagCons.CF_ATTACHMENT_RISK_ATTR_STAT, MQTagCons.CF_ATTACHMENT_RISK_ATTR_STAT + "_" + attaId, crowdfundingAttachmentList));
				log.info("attachment risk add result:{}", null != msgResult ? msgResult.getStatus() : -1);
			} catch (Exception e){
				log.error("attachment risk add Exception", e);
			}
		}
		return result;
	}

	private Map<AttachmentTypeEnum, List<CrowdfundingAttachmentVo>> mapAttachments(
			List<CrowdfundingAttachment> attachments) {
		Map<AttachmentTypeEnum, List<CrowdfundingAttachmentVo>> result = Maps.newHashMap();
		for (CrowdfundingAttachment attachment : attachments) {

			//shuidihuzhu.com ==> shuidichou.com
			attachment = compactShuidihuzhuDotCom(attachment);

			List<CrowdfundingAttachmentVo> list = result.get(attachment.getType());
			if(list == null){
				list = Lists.newArrayList();
				result.put(attachment.getType(), list);
			}
			list.add(new CrowdfundingAttachmentVo(attachment));
		}
		return result;
	}

	@Override
	public int deleteByParentIdAndType(int parentId, List<AttachmentTypeEnum> typeList) {
		if (parentId <= 0 || CollectionUtils.isEmpty(typeList)) {
			return -1;
		}
		return this.crowdfundingAttachmentDao.deleteByParentIdAndType(parentId, typeList);
	}

	private CrowdfundingAttachment compactShuidihuzhuDotCom(CrowdfundingAttachment attachment) {
		//防止视频被替换域名
		if(attachment == null
				|| attachment.getType() == AttachmentTypeEnum.ATTACH_VIDEO
				|| attachment.getType() == AttachmentTypeEnum.ATTACH_PAYEE_RELATION_VIDEO){
			return attachment;
		}
	    if(attachment != null && !StringUtils.isEmpty(attachment.getUrl())) {
	    	String url = ossToCosTransService.convertToCosUrl(attachment.getUrl());
		    attachment.setUrl(url);
	    }

	    return attachment;
    }

}
