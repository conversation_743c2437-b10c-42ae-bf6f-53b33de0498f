package com.shuidihuzhu.cf.financetoc.biz.order;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrderShardingModel;

import java.util.List;
import java.util.Set;

/**
 * @author: wanghui
 * @time: 2019/3/25 3:15 PM
 * @description: crowdfunding_order 分表读。   未校验 暂不能用
 */
public interface CrowdfundingOrderShardingBiz {
	List<CrowdfundingOrderShardingModel> listCrowdfundingOrderShardingModelByUserId(long userId);
	/**
	 * 包含退款
	 *
	 * @return
	 */
	List<CrowdfundingOrder> getAllPayByUserIdsFromSharding(Set<Long> userIds);
}
