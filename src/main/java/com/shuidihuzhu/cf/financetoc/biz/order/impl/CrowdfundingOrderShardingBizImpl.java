package com.shuidihuzhu.cf.financetoc.biz.order.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.financetoc.biz.order.CrowdfundingOrderShardingBiz;
import com.shuidihuzhu.cf.financetoc.dao.order.CrowdfundingOrderShardingCrowdfundingIdDao;
import com.shuidihuzhu.cf.financetoc.dao.order.CrowdfundingOrderShardingIdDao;
import com.shuidihuzhu.cf.financetoc.dao.order.CrowdfundingOrderShardingUserIdDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrderShardingModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @time: 2019/3/25 1:59 PM
 * @description:  crowdfunding_order 分表读。   未校验 暂不能用
 */
@Slf4j
@Service
public class CrowdfundingOrderShardingBizImpl implements CrowdfundingOrderShardingBiz {

    @Autowired
    private CrowdfundingOrderShardingIdDao crowdfundingOrderShardingIdDao;
    @Autowired
    private CrowdfundingOrderShardingUserIdDao crowdfundingOrderShardingUserIdDao;
    @Autowired
    private CrowdfundingOrderShardingCrowdfundingIdDao crowdfundingOrderShardingCrowdfundingIdDao;


    @Override
    public List<CrowdfundingOrderShardingModel> listCrowdfundingOrderShardingModelByUserId(long userId) {
        if (userId <= 0) {
            return Lists.newArrayList();
        }
        return crowdfundingOrderShardingUserIdDao.getCrowdfundingOrderShardingModelListByUserId(userId);
    }

    protected List<CrowdfundingOrder> getByUserIdFromSharding(Set<Long> userIds, Integer crowdfundingId) {
        if (CollectionUtils.isEmpty(userIds) || crowdfundingId==null) {
            return Lists.newArrayList();
        }
        return crowdfundingOrderShardingCrowdfundingIdDao.getByUserId(userIds, Long.valueOf(crowdfundingId));
    }

    protected CrowdfundingOrder getByIdFromSharding(Long id) {
        Long crowdfundingId = crowdfundingOrderShardingIdDao.getCrowdfundingIdById(id);
        if (crowdfundingId==null){
            return null;
        }
        return crowdfundingOrderShardingCrowdfundingIdDao.getById(crowdfundingId, id);
    }

    /**
     * 包含退款记录
     */
    @Override
    public List<CrowdfundingOrder> getAllPayByUserIdsFromSharding(Set<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }
        Map<String, List<Long>> shardingUserIdMapData = this.getShardingFieldValueMapData(userIds);
        List<CrowdfundingOrderShardingModel> shardingModelList = shardingUserIdMapData.entrySet().parallelStream().map(entry -> {
            return crowdfundingOrderShardingUserIdDao.getCrowdfundingOrderShardingModelListByUserIdsWithTableSuffixName(
                    entry.getKey(), entry.getValue());
        }).reduce((crowdfundingOrderShardingModels, result) -> {
            crowdfundingOrderShardingModels.addAll(result);
            return crowdfundingOrderShardingModels;
        }).get();
        if (CollectionUtils.isEmpty(shardingModelList)){
            return Lists.newArrayList();
        }
        Map<String, List<CrowdfundingOrderShardingModel>> shardingMapData = this.getShardingMapData(shardingModelList);
        List<CrowdfundingOrder> crowdfundingOrderList = shardingMapData.entrySet().parallelStream().map(entry -> {
            return crowdfundingOrderShardingCrowdfundingIdDao
                    .getAllPayByCrowdfundingIdsAndUserIdsWithSuffixTableName(
                            entry.getKey(),
                            entry.getValue().stream().map(CrowdfundingOrderShardingModel::getId)
                                    .collect(Collectors.toList()));
        }).reduce((crowdfundingOrders,result)->{
            crowdfundingOrders.addAll(result);
            return crowdfundingOrders;
        }).get();
        return getSortedById(crowdfundingOrderList);
    }

    private List<CrowdfundingOrder> getSortedById(List<CrowdfundingOrder> orders){
        if (CollectionUtils.isEmpty(orders)){
            return Lists.newArrayList();
        }
        return orders.stream()
                .sorted(Comparator.comparing(CrowdfundingOrder::getId))
                .collect(Collectors.toList());
    }

    /**
     * 根据传入的 CrowdfundingOrderShardingModels 获得以 crowdfundingId  为key中 value为model的集合
     * @param crowdfundingOrderShardingModels
     * @return
     */
    private Map<String,List<CrowdfundingOrderShardingModel>> getShardingMapData(List<CrowdfundingOrderShardingModel> crowdfundingOrderShardingModels){
        Map<String,List<CrowdfundingOrderShardingModel>> shardingMap = Maps.newHashMap();
        for (CrowdfundingOrderShardingModel model:crowdfundingOrderShardingModels){
            String suffixTableName = String.format("%03d", Math.abs(model.getCrowdfundingId()) % 100);
            List<CrowdfundingOrderShardingModel> currentValues = shardingMap.get(suffixTableName);
            if (CollectionUtils.isEmpty(currentValues)){
                shardingMap.put(suffixTableName,Lists.newArrayList(model));
            }else {
                currentValues.add(model);
            }
        }
        return shardingMap;
    }

    /**
     * 根据传入的 set  按照分表逻辑 划分
     * 返回值 key为分表 编号  list是在属于分表编号的集合
     * @param fieldValueSet
     * @return
     */
    private Map<String,List<Long>> getShardingFieldValueMapData(Set<Long> fieldValueSet){
        Map<String,List<Long>> shardingFieldValueMap = Maps.newHashMap();
        for (Long fieldValue:fieldValueSet){
            String suffixTableName = String.format("%03d", Math.abs(fieldValue) % 100);
            List<Long> currentValues = shardingFieldValueMap.get(suffixTableName);
            if (CollectionUtils.isEmpty(currentValues)){
                shardingFieldValueMap.put(suffixTableName,Lists.newArrayList(fieldValue));
            }else {
                currentValues.add(fieldValue);
            }
        }
        return shardingFieldValueMap;
    }
}
