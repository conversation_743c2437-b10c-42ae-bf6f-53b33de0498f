package com.shuidihuzhu.cf.financetoc.biz.payee;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoPayeeVo;

import java.util.List;

public interface CrowdfundingInfoPayeeBiz {

	int add(CrowdfundingInfoPayee crowdfundingInfoPayee);

	int update(CrowdfundingInfoPayee crowdfundingInfoPayee);

	CrowdfundingInfoPayee getByInfoUuid(String infoUuid);

    boolean checkCanSubmitRelationVideo(CrowdfundingInfoPayeeVo payeeVo);

	void insertUpdatePayeeImages(int caseId, List<CrowdfundingAttachment> attachmentList);

}
