package com.shuidihuzhu.cf.financetoc.biz.order;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.enums.combine.CombineCommonEnum;
import com.shuidihuzhu.cf.financetoc.service.CfCombineOrderManageService;
import com.shuidihuzhu.cf.model.contribute.CfCombineOrderManage;
import com.shuidihuzhu.cf.model.contribute.CfCombinePayUidInfo;
import com.shuidihuzhu.client.baseservice.pay.model.v3.combine.callback.CombineInnerCallBack;
import com.shuidihuzhu.client.baseservice.pay.model.v3.combine.callback.CombineInnerSub;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class CfCombineOrderManageBiz {


    public Map<String, CfCombineOrderManage> selectMappingByParentUid(String parentUid) {
        Map<String, CfCombineOrderManage> mapping = Maps.newHashMap();
        List<CfCombineOrderManage> result = orderManageService.selectByParentPayUid(parentUid);
        for (CfCombineOrderManage orderManage : result) {
            mapping.put(orderManage.getSubPayUid(), orderManage);
        }
        return mapping;
    }

    @Autowired
    private CfCombineOrderManageService orderManageService;

    public int insertCombineOrderList(CfCombinePayUidInfo combineUidInfo) {
        List<CfCombineOrderManage> combineList = Lists.newArrayList();
        if (combineUidInfo.getCombineBizType() == CombineCommonEnum.CombineOrderBizType.CONTRIBUTE) {
            CfCombinePayUidInfo.CaseOrderPayUidInfo caseOrderPayUidInfo = combineUidInfo.getCaseOrderPayUidInfoList().get(0);
            CfCombineOrderManage caseOrder = buildCaseOrder(combineUidInfo, caseOrderPayUidInfo);

            CfCombinePayUidInfo.ContributeOrderPayUidInfo contributeOrderPayUidInfo = combineUidInfo.getContributeOrderPayUidInfo();
            CfCombineOrderManage contributeOrder = new CfCombineOrderManage();
            contributeOrder.setParentPayUid(combineUidInfo.getParentPayUid());
            contributeOrder.setSubPayUid(contributeOrderPayUidInfo.getContributeSubPayUid());
            contributeOrder.setOrderType(CombineCommonEnum.CombineOrderType.CONTRIBUTE.getCode());

            combineList.add(caseOrder);
            combineList.add(contributeOrder);
            combineList.forEach(r -> r.setBizType(CombineCommonEnum.CombineOrderBizType.CONTRIBUTE.getCode()));
        } else if (combineUidInfo.getCombineBizType() == CombineCommonEnum.CombineOrderBizType.LOVE_MORE_DONATE) {
            List<CfCombinePayUidInfo.CaseOrderPayUidInfo> caseOrderPayUidInfoList = combineUidInfo.getCaseOrderPayUidInfoList();
            for (CfCombinePayUidInfo.CaseOrderPayUidInfo caseOrderPayUidInfo : caseOrderPayUidInfoList) {
                CfCombineOrderManage caseOrder = buildCaseOrder(combineUidInfo, caseOrderPayUidInfo);
                combineList.add(caseOrder);
            }
            combineList.forEach(r -> r.setBizType(CombineCommonEnum.CombineOrderBizType.LOVE_MORE_DONATE.getCode()));
        }
        return orderManageService.addCombineOrderList(combineList);
    }

    @NotNull
    private static CfCombineOrderManage buildCaseOrder(CfCombinePayUidInfo combineUidInfo, CfCombinePayUidInfo.CaseOrderPayUidInfo caseOrderPayUidInfo) {
        CfCombineOrderManage caseOrder = new CfCombineOrderManage();
        caseOrder.setParentPayUid(combineUidInfo.getParentPayUid());
        caseOrder.setSubPayUid(caseOrderPayUidInfo.getCaseSubPayUid());
        caseOrder.setOrderType(CombineCommonEnum.CombineOrderType.CASE_DONATE.getCode());
        caseOrder.setCaseId(caseOrderPayUidInfo.getCaseId());
        caseOrder.setOrderId(caseOrderPayUidInfo.getOrderId());
        caseOrder.setMain(caseOrderPayUidInfo.isMain() ? 1 : 0);
        return caseOrder;
    }


    public int updatePaySuccess(CombineInnerCallBack combineCallBack, Map<String, CfCombineOrderManage> mapping) {

        List<CfCombineOrderManage> needUpdateCombine = Lists.newArrayList();
        for (CombineInnerSub inner : combineCallBack.getSubOrders()) {
            CfCombineOrderManage orderManage = new CfCombineOrderManage();

            orderManage.setParentThirdPayUid(combineCallBack.getPayUid());
            orderManage.setSubPayUid(inner.getSubOrderId());
            orderManage.setSubThirdPayUid(inner.getSubPayUid());
            needUpdateCombine.add(orderManage);
        }

        return orderManageService.updateThirdPayUid(needUpdateCombine);

    }

    public CfCombineOrderManage getByParentThirdPayUidAndOrderType(String parentThirdPayUid, CombineCommonEnum.CombineOrderType orderType) {
        if (StringUtils.isBlank(parentThirdPayUid) || Objects.isNull(orderType)) {
            return null;
        }
        return orderManageService.getByParentThirdPayUidAndOrderType(parentThirdPayUid, orderType.getCode());
    }
}
