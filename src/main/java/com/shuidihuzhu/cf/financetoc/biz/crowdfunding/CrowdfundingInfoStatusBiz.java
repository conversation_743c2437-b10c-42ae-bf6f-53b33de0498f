package com.shuidihuzhu.cf.financetoc.biz.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus;

import java.util.List;
import java.util.Map;

public interface CrowdfundingInfoStatusBiz {
	
	int add(CrowdfundingInfoStatus crowdfundingInfoStatus);

	int updateByInfoId(String infoUuId, int type, CrowdfundingInfoStatusEnum status);

	List<CrowdfundingInfoStatus> getByInfoUuid(String infoUuid);

	Map<Integer, Integer> getMapByInfoUuid(String infoUuid);

	CrowdfundingInfoStatus getByInfoUuidAndType(String infoUuid, int type);
}
