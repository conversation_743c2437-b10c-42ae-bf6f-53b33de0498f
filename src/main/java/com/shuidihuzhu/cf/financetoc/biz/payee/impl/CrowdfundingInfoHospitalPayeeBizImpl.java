package com.shuidihuzhu.cf.financetoc.biz.payee.impl;

import com.shuidihuzhu.cf.financetoc.biz.payee.CrowdfundingInfoHospitalPayeeBiz;
import com.shuidihuzhu.cf.financetoc.dao.crowdfunding.CrowdfundingInfoDao;
import com.shuidihuzhu.cf.financetoc.dao.payee.CrowdfundingInfoHospitalPayeeDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoHospitalPayee;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by niejiangnan on 2017/9/26.
 */
@Service
@Slf4j
public class CrowdfundingInfoHospitalPayeeBizImpl implements CrowdfundingInfoHospitalPayeeBiz {

    @Autowired
    private CrowdfundingInfoHospitalPayeeDao crowdfundingInfoHospitalPayeeDao;
    @Autowired
    private CrowdfundingInfoDao crowdfundingInfoDao;

    @Override
    //在添加前对特殊字符做替换
    public int add(CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee) {
        crowdfundingInfoHospitalPayee.setHospitalAccountName(crowdfundingInfoHospitalPayee.replaceSpecialString(crowdfundingInfoHospitalPayee.getHospitalAccountName()));
        crowdfundingInfoHospitalPayee.setHospitalBankBranchName(crowdfundingInfoHospitalPayee.replaceSpecialString(crowdfundingInfoHospitalPayee.getHospitalBankBranchName()));
        if (crowdfundingInfoHospitalPayee.getCaseId() == 0) {
            CrowdfundingInfo fundingInfo = crowdfundingInfoDao.getFundingInfo(crowdfundingInfoHospitalPayee.getInfoUuid());
            crowdfundingInfoHospitalPayee.setCaseId(fundingInfo.getId());
        }
        return crowdfundingInfoHospitalPayeeDao.save(crowdfundingInfoHospitalPayee);
    }

    @Override
    //在更新前对特殊字符做替换
    public int update(CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee) {
        crowdfundingInfoHospitalPayee.setHospitalAccountName(crowdfundingInfoHospitalPayee.replaceSpecialString(crowdfundingInfoHospitalPayee.getHospitalAccountName()));
        crowdfundingInfoHospitalPayee.setHospitalBankBranchName(crowdfundingInfoHospitalPayee.replaceSpecialString(crowdfundingInfoHospitalPayee.getHospitalBankBranchName()));

        return crowdfundingInfoHospitalPayeeDao.update(crowdfundingInfoHospitalPayee);
    }

    @Override
    public CrowdfundingInfoHospitalPayee getByInfoUuid(String infoUuid) {
        CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee = null;
        if (StringUtils.isNoneBlank(infoUuid)) {
            crowdfundingInfoHospitalPayee = crowdfundingInfoHospitalPayeeDao.getByInfoUuid(infoUuid);
        }
        return crowdfundingInfoHospitalPayee;
    }
}
