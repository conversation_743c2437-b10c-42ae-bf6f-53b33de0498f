package com.shuidihuzhu.cf.financetoc.biz.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;

public interface CfOperatingRecordBiz {

	CfOperatingRecord save(String infoUuid,long userId, String userName, CfOperatingRecordEnum.Type type,
	                       CfOperatingRecordEnum.Role role);

	CfOperatingRecord save(String infoUuid, long userId, String userName, CfOperatingRecordEnum.Type type,
						   CfOperatingRecordEnum.Role role, String comment, int refuseCount);


	void updateCfOperatingResion(CfOperatingRecord cfOperatingRecord);
}
