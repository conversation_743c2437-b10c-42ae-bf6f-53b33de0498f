package com.shuidihuzhu.cf.financetoc.biz.crowdfunding.impl;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingInfoStatusBiz;
import com.shuidihuzhu.cf.financetoc.dao.crowdfunding.CrowdfundingInfoStatusDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CrowdfundingInfoStatusBizImpl implements CrowdfundingInfoStatusBiz {

	@Autowired
	private CrowdfundingInfoStatusDao crowdfundingInfoStatusDao;

	@Override
	public int add(CrowdfundingInfoStatus crowdfundingInfoStatus) {
		return this.crowdfundingInfoStatusDao.add(crowdfundingInfoStatus);
	}

	public int updateByInfoId(String infoUuId, int type, CrowdfundingInfoStatusEnum status) {
		return this.crowdfundingInfoStatusDao.updateByType(infoUuId, type, status.getCode());
	}

	public int updateByInfoId(String infoUuId, CrowdfundingInfoStatusEnum status) {
		return this.crowdfundingInfoStatusDao.update(infoUuId, status.getCode());
	}

	@Override
	public List<CrowdfundingInfoStatus> getByInfoUuid(String infoUuid) {
		if (StringUtils.isBlank(infoUuid)) {
			return Collections.emptyList();
		}
		return this.crowdfundingInfoStatusDao.getByInfoUuid(infoUuid);
	}

	@Override
	public Map<Integer, Integer> getMapByInfoUuid(String infoUuid) {
		List<CrowdfundingInfoStatus> crowdfundingInfoStatusList = this.getByInfoUuid(infoUuid);
		if(CollectionUtils.isEmpty(crowdfundingInfoStatusList)) {
			return Maps.newHashMap();
		}
		return crowdfundingInfoStatusList.stream().collect(Collectors.toMap(CrowdfundingInfoStatus::getType, CrowdfundingInfoStatus::getStatus));
	}


	@Override
	public CrowdfundingInfoStatus getByInfoUuidAndType(String infoUuid, int type) {
		return this.crowdfundingInfoStatusDao.getByInfoUuidAndType(infoUuid, type);
	}

}
