package com.shuidihuzhu.cf.financetoc.biz.impl;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.shuidihuzhu.cf.financetoc.biz.WxMpConfigCacheBiz;
import com.shuidihuzhu.wx.biz.ShuidiWxService;
import com.shuidihuzhu.wx.model.WxMpConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @DATE 2019/3/5
 */
@Service
@Slf4j
public class WxMpConfigCacheBizImpl implements WxMpConfigCacheBiz {


    private LoadingCache<Integer, WxMpConfig> cache = CacheBuilder.newBuilder().maximumSize(1000)
            //但是是读触发的；如果没有读，则不会操作
            .refreshAfterWrite(1, TimeUnit.DAYS).build(new CacheLoader<Integer, WxMpConfig>() {
                @Override
                public WxMpConfig load(Integer key) {
                    return loadByThirdType(key);
                }
            });


    @Autowired
    private ShuidiWxService shuidiWxService;


    @Override
    public WxMpConfig getWxMpConfigFromCache(int userThirdType) {

        try {
            return cache.get(userThirdType);
        }catch (Exception e){
            log.error("getWxMpConfigFromCache userThirdType={}",userThirdType,e);
        }
        return null;
    }


    private WxMpConfig loadByThirdType(int userThirdType) {
        WxMpConfig wxMpConfig = this.shuidiWxService.getByThirdType(userThirdType);
        return wxMpConfig;
    }
}
