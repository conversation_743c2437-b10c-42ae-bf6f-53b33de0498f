package com.shuidihuzhu.cf.financetoc.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingAuthorBiz;
import com.shuidihuzhu.cf.financetoc.dao.crowdfunding.CrowdfundingAuthorDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by wuxinlong on 6/21/16.
 */

@Service
@Slf4j
public class CrowdfundingAuthorBizImpl implements CrowdfundingAuthorBiz {
	
    @Autowired
    private CrowdfundingAuthorDao crowdfundingAuthorDao;

	@Override
	public CrowdfundingAuthor get(Integer crowdfundingId) {
		CrowdfundingAuthor crowdfundingAuthor = null;
		try {
			crowdfundingAuthor = crowdfundingAuthorDao.get(crowdfundingId);
		} catch (Exception e) {
			log.error("CrowdfundingAuthorBiz#get error! caseId={}", crowdfundingId, e);
		}
		return crowdfundingAuthor;
    }

}
