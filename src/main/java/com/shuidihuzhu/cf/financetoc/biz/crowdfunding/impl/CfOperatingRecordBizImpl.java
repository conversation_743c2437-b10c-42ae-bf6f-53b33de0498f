package com.shuidihuzhu.cf.financetoc.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceReadFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CfOperatingRecordBiz;
import com.shuidihuzhu.cf.financetoc.dao.crowdfunding.CfOperatingRecordDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CfOperatingRecordBizImpl implements CfOperatingRecordBiz {

	@Autowired
	private CfOperatingRecordDao cfOperatingRecordDao;

	@Autowired
	private CfFinanceReadFeignClient cfFinanceReadFeignClient;

	@Override
	public CfOperatingRecord save(String infoUuid,long userId, String userName, CfOperatingRecordEnum.Type type,
	                              CfOperatingRecordEnum.Role role) {
		CfOperatingRecord cfOperatingRecord = new CfOperatingRecord();
		cfOperatingRecord.setInfoUuid(infoUuid);
		cfOperatingRecord.setRole(role.getCode());
		cfOperatingRecord.setType(type.getCode());
		cfOperatingRecord.setUserId(userId);
		cfOperatingRecord.setRefuseCount(0);
		cfOperatingRecord.setUserName(userName);
		cfOperatingRecord.setComment("");
		cfOperatingRecord.setFinancialComment(this.bulidResion(cfOperatingRecord));
		this.cfOperatingRecordDao.save(cfOperatingRecord);
		return cfOperatingRecord;
	}

	@Override
	public CfOperatingRecord save(String infoUuid, long userId, String userName, CfOperatingRecordEnum.Type type,
			CfOperatingRecordEnum.Role role, String comment, int refuseCount) {
		CfOperatingRecord cfOperatingRecord = new CfOperatingRecord();
		cfOperatingRecord.setInfoUuid(infoUuid);
		cfOperatingRecord.setRole(role.getCode());
		cfOperatingRecord.setType(type.getCode());
		cfOperatingRecord.setUserId(userId);
		cfOperatingRecord.setUserName(userName);
		cfOperatingRecord.setRefuseCount(refuseCount);
		cfOperatingRecord.setComment(StringUtils.trimToEmpty(comment));
		this.cfOperatingRecordDao.save(cfOperatingRecord);
		return cfOperatingRecord;

	}

	@Override
	public void updateCfOperatingResion(CfOperatingRecord cfOperatingRecord) {
		try {
			if (cfOperatingRecord == null || cfOperatingRecord.getId() <= 0L) {
				return;
			}
			String resion = bulidResion(cfOperatingRecord);
			if (StringUtils.isNotBlank(resion)) {
				cfOperatingRecordDao.updateResion(cfOperatingRecord.getId(), resion);
				log.info("资金操作记录更新:{}", resion);
			}
		} catch (Exception e) {
			log.error("资金操作记录更新异常", e);
		}
	}

	/**
	 * 构建当前resion
	 *
	 * @param cfOperatingRecord
	 * @return
	 */
	private String bulidResion(CfOperatingRecord cfOperatingRecord) {
		String resion = "";
		try {
			FeignResponse<String> response = cfFinanceReadFeignClient.buildOperatingResion(cfOperatingRecord.getInfoUuid(), cfOperatingRecord.getType(),
					cfOperatingRecord.getRole(), cfOperatingRecord.getUserId(), cfOperatingRecord.getUserName(),
					cfOperatingRecord.getBizId(), cfOperatingRecord.getComment(), cfOperatingRecord.getRefuseCount());
			if (response == null) {
				log.error("无法获取资金操作信息");
			} else {
				if (response.getData() != null) {
					resion = response.getData();
				}
				log.debug("资金操作信息：{}", response.getData());
			}
		} catch (Exception e) {
			log.error("资金操作Resion记录异常", e);
			resion = "";
		}
		return resion;
	}
}
