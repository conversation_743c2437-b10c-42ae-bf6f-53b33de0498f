package com.shuidihuzhu.cf.financetoc.biz.refund.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.finance.model.CfDonorRefundApply;
import com.shuidihuzhu.cf.financetoc.biz.refund.CfDonorApplyRefundBiz;
import com.shuidihuzhu.cf.financetoc.biz.refund.CfDonorRefundBlacklistBiz;
import com.shuidihuzhu.cf.financetoc.biz.refund.CfDonorRefundRecordBiz;
import com.shuidihuzhu.cf.financetoc.biz.order.CrowdfundingOrderBiz;
import com.shuidihuzhu.cf.financetoc.delegate.ICFFinanceFeignDelegate;
import com.shuidihuzhu.cf.financetoc.delegate.UserThirdDelegate;
import com.shuidihuzhu.cf.financetoc.service.MsgClientV2Service;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrderShardingModel;
import com.shuidihuzhu.cf.model.crowdfunding.pay.CfDonorRefundBlacklist;
import com.shuidihuzhu.cf.model.crowdfunding.pay.CfDonorRefundRecord;
import com.shuidihuzhu.wx.biz.ShuidiWxService;
import com.shuidihuzhu.wx.grpc.model.WxTextMessageModel;
import com.shuidihuzhu.wx.model.WxMpConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/10/11
 */
@Slf4j
@Service
public class CfDonorApplyRefundBizImpl implements CfDonorApplyRefundBiz {

    private static final long TIME = 720 * 3600 * 1000L;

    @Autowired
    private UserThirdDelegate userThirdDelegate;
    @Autowired
    private CrowdfundingOrderBiz crowdfundingOrderBiz;
    @Autowired
    private CfDonorRefundBlacklistBiz cfDonorRefundBlacklistBiz;
    @Autowired
    private CfDonorRefundRecordBiz cfDonorRefundRecordBiz;
    @Autowired
    private ShuidiWxService shuidiWxService;
    @Autowired
    private ICFFinanceFeignDelegate cfFinanceFeignDelegate;
    @Resource
    private MsgClientV2Service msgClientV2Service;

    @Override
    public void doEvent(WxTextMessageModel wxTextMessageModel) {
        if (!"我要退款".equals(wxTextMessageModel.getContent())) {
            log.warn("不是严格匹配");
            return;
        }
        UserThirdModel userThirdModel = userThirdDelegate.getThirdModelWithOpenId(wxTextMessageModel.getOpenId());
        if (null == userThirdModel) {
            return;
        }
        WxMpConfig wxMpConfig = shuidiWxService.getByOriginId(wxTextMessageModel.getOriginId());
        if (null == wxMpConfig) {
            return;
        }
        CfDonorRefundBlacklist cfDonorRefundBlacklist = cfDonorRefundBlacklistBiz.getByUserId(userThirdModel.getUserId());
        if (null != cfDonorRefundBlacklist) {
            //黑名单
            log.info("userId:{} 在黑名单内,不做处理", userThirdModel.getUserId());
            return;
        }

        boolean hasFunding = this.hasFunding(userThirdModel.getUserId());

        log.info("申请单笔退款校验结果userId:{},hasFunding:{}", userThirdModel.getUserId(), hasFunding);

        //防止资产转移，userId发生变化
        long userId = userThirdModel.getUserId();
        userThirdModel = userThirdDelegate.getThirdModelWithOpenId(wxTextMessageModel.getOpenId());
        if (userThirdModel != null && userId != userThirdModel.getUserId()) {
            userId = userThirdModel.getUserId();
        }

        if (hasFunding) {
            //有退款可以操作
            this.hasCanApply(userId, wxMpConfig.getThirdType());
            //添加退款申请记录信息
            CfDonorRefundRecord cfDonorRefundRecord = new CfDonorRefundRecord();
            cfDonorRefundRecord.setUserId(userId);
            cfDonorRefundRecord.setValidTime(new Date(System.currentTimeMillis() + TIME));
            cfDonorRefundRecordBiz.add(cfDonorRefundRecord);
        } else {
            //没有退款可以操作
            this.hasNoCanApply(userId, wxMpConfig.getThirdType());
        }

    }

    /**
     * 1604
     * 用户申请退款时，回复退款记录
     *
     * @param userId
     * @param thirdType
     */
    private void hasCanApply(long userId, int thirdType) {
        String modelNum0 = "1604_trans_v1";
        msgClientV2Service.sendWxMsg(modelNum0, Lists.newArrayList(userId), thirdType);
    }

    /**
     * 1603
     * 没有可退款用户申请退款
     *
     * @param userId
     * @param thirdType
     */
    private void hasNoCanApply(long userId, int thirdType) {
        String modelNum0 = "model1603-1";
        msgClientV2Service.sendWxMsg(modelNum0, Lists.newArrayList(userId), thirdType);
    }

    @Override
    public boolean hasFunding(long userId) {
        AtomicBoolean hasFunding = new AtomicBoolean(false);

        List<CrowdfundingOrderShardingModel> orderShardingModels = crowdfundingOrderBiz.listCrowdfundingOrderShardingModelByUserId(userId);
        if (CollectionUtils.isEmpty(orderShardingModels)) {
            return hasFunding.get();
        }
        List<Integer> caseIds = orderShardingModels.stream().map(x -> x.getCrowdfundingId().intValue()).distinct().collect(Collectors.toList());

        caseIds.parallelStream().forEach(caseId -> {
            if (hasFunding.get()) {
                return;
            }
            List<CrowdfundingOrder> orders = crowdfundingOrderBiz.getListByUserId(caseId, userId, 500);
            if (CollectionUtils.isEmpty(orders)) {
                return;
            }
            Set<Long> orderSets = orders.stream().map(CrowdfundingOrder::getId).collect(Collectors.toSet());

            Map<Long, CfDonorRefundApply.StatusEnum> refundStatusMap = cfFinanceFeignDelegate
                    .getOrderRefundStatus(Lists.newArrayList(orderSets));
            if (MapUtils.isEmpty(refundStatusMap)) {
                return;
            }
            //只要存在 “不属于默认，也不属于退款成功“，就有可操作的退款订单
            if (refundStatusMap.values().stream()
                    .anyMatch(a -> !a.equals(CfDonorRefundApply.StatusEnum.DEFAULT)
                            && !a.equals(CfDonorRefundApply.StatusEnum.SUCCESS))) {
                hasFunding.set(true);
                return;
            }
        });

        return hasFunding.get();
    }
}
