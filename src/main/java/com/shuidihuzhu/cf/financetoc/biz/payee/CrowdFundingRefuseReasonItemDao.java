package com.shuidihuzhu.cf.financetoc.biz.payee;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingRefuseReasonItem;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface CrowdFundingRefuseReasonItemDao {

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingRefuseReasonItem> getListByIds(@Param("ids") List<Integer> ids);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingRefuseReasonItem> getAll(@Param("start") int start, @Param("size") int size);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingRefuseReasonItem> getListByTypes(@Param("typeIds") List<Integer> typeIds);

    @DataSource(DS.CF_SLAVE)
    CrowdfundingRefuseReasonItem getByContent(@Param("content") String content);
}
