package com.shuidihuzhu.cf.financetoc.biz.payee;

import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.model.crowdfunding.BankCardVerifyRecord;
import com.shuidihuzhu.cf.model.crowdfunding.BankCardVerifyResult;

import java.util.List;

/**
 * Author: <PERSON> Date: 16/9/21 15:20
 */
public interface BankCardVerifyRecordBiz {

	int add(BankCardVerifyRecord bankCardVerifyRecord);

	BankCardVerifyResult verify(String holderName, UserIdentityType identityType, String idCard, String bankCardNum,
								int crowdfundingId, long userId, boolean updateStatus);
	/**
	 * 获取信息
	 *
	 * @param holderName
	 * @param cryptoIdCard
	 * @param cryptoBankCard
	 * @param caseId
	 * @return
	 */
	List<BankCardVerifyRecord> getByCaseIdAndElements(String holderName, String cryptoIdCard, String cryptoBankCard,
													  int caseId);
}
