package com.shuidihuzhu.cf.financetoc.biz.payee.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.financetoc.biz.payee.CrowdfundingRefuseReasonItemBiz;
import com.shuidihuzhu.cf.financetoc.biz.payee.CrowdFundingRefuseReasonItemDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingRefuseReasonItem;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by wangsf on 17/1/20.
 */
@Service
public class CrowdfundingRefuseReasonItemBizImpl implements CrowdfundingRefuseReasonItemBiz {

    @Autowired
    private CrowdFundingRefuseReasonItemDao crowdFundingRefuseReasonItemDao;

    @Override
    public List<CrowdfundingRefuseReasonItem> getListByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return crowdFundingRefuseReasonItemDao.getListByIds(ids);
    }

}
