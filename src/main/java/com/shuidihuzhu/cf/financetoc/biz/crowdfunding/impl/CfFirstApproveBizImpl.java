package com.shuidihuzhu.cf.financetoc.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.material.model.materialField.CfFirstApproveField;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CfFirstApproveBiz;
import com.shuidihuzhu.cf.financetoc.dao.crowdfunding.CfFirstApproveMaterialDao;
import com.shuidihuzhu.cf.financetoc.service.IMaterialCenterService;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: duchao
 * @Date: 2018/8/4 下午3:38
 */
@Slf4j
@Service
@RefreshScope
public class CfFirstApproveBizImpl implements CfFirstApproveBiz {

    @Resource
    private CfFirstApproveMaterialDao cfFirstApproveMaterialDao;
    @Autowired
    private IMaterialCenterService materialService;

    @Override
    public CfFirsApproveMaterial getByInfoUuid(String infoUuid) {
        if(StringUtils.isEmpty(infoUuid)) {
            return null;
        }
        return cfFirstApproveMaterialDao.getByInfoUuid(infoUuid);
    }

    @Override
    public List<CfFirsApproveMaterial> transferUserId(long fromUserId, long toUserId, int caseId) {
        List<CfFirsApproveMaterial> result = cfFirstApproveMaterialDao.getByUserId(fromUserId);
        if (CollectionUtils.isEmpty(result)) {
            return result;
        }

        result = result.stream()
                .filter(f -> {
                    if (caseId > 0)
                        return f.getInfoId() == caseId;
                    return true;
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(result)) {
            return result;
        }
        List<Integer> ids = result.stream().map(CfFirsApproveMaterial::getId).collect(Collectors.toList());
        cfFirstApproveMaterialDao.updateUserIdByIds(ids, toUserId);
        // 同步到材料中心
        for (CfFirsApproveMaterial material : result) {
            log.info("转移案例前置材料, fromUserId:{}\ttoUserId:{}\tcfINfo:{}", fromUserId, toUserId, material);
            material.setUserId(toUserId);
            materialService.doubleWriteFirstApprove(material, Lists.newArrayList(CfFirstApproveField.case_raise_user_id));
        }
        return result;
    }
}
