package com.shuidihuzhu.cf.financetoc.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.constants.AsyncPoolConstants;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingInfoSimpleBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @package: com.shuidihuzhu.cf.biz.crowdfunding.impl
 * @Author: liujiawei
 * @Date: 2018-11-29  15:17
 */
@Service
@Slf4j
public class CrowdfundingInfoSimpleBizImpl implements CrowdfundingInfoSimpleBiz {
    @Resource(name = "cfApiRedisClusterHandler")
    private RedissonHandler cfApiRedisClusterHandler;

    private static final String CF_INFO_KEY = "cf_info_redis_cluster_key_";


    @Async(AsyncPoolConstants.WRITE_SIMPLE_INFO_POOL)
    @Override
    public void deleteCaseKey(CrowdfundingInfo caseInfo) {
        if (caseInfo == null) {
            return;
        }

        try {
            cfApiRedisClusterHandler.delKey(Lists.newArrayList(getKey(caseInfo.getInfoId()),
                    getKey(caseInfo.getId())));
            log.info("删除案例的缓存数据。caseId:{}", caseInfo.getId());
        } catch (Exception e) {
            log.warn("删除案例缓存失败。caseId:{}", caseInfo.getId(), e);
        }
    }


    private static String getKey(String infoUuid) {
        return CF_INFO_KEY + infoUuid;
    }

    private static String getKey(int infoId) {
        return CF_INFO_KEY + infoId;
    }
}
