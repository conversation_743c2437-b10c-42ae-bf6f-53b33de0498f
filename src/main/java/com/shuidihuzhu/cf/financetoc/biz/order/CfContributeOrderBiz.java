package com.shuidihuzhu.cf.financetoc.biz.order;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.finance.client.feign.CfContributeRefundRecordFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.req.CfContributeRefundParam;
import com.shuidihuzhu.cf.financetoc.service.CfContributeOrderService;
import com.shuidihuzhu.cf.model.contribute.CfContributeMsgBody;
import com.shuidihuzhu.cf.model.contribute.CfContributeOrder;
import com.shuidihuzhu.cf.model.contribute.CfPayInnerCallBack;
import com.shuidihuzhu.cf.model.contribute.ContributeRefundSubmitVo;
import com.shuidihuzhu.msg.util.MobileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RefreshScope
public class CfContributeOrderBiz {

    @Autowired
    private CfContributeOrderService contributeOrderService;
    @Autowired
    private CfContributeRefundRecordFeignClient refundRecordFeignClient;

    // 更新支付的状态 上报业财
    public boolean forContributeBusiness(CfPayInnerCallBack innerCallBack) {

        CfContributeOrder contributeOrder = contributeOrderService.selectByPayUid(innerCallBack.getOrderId());
        if (contributeOrder == null) {
            log.info("捐赠表数据不存在 payUid:{}", innerCallBack.getOrderId());
            return false;
        }
        if (contributeOrder.getOrderStatus() != CfContributeOrder.OrderStatus.NO_PAY.getCode()) {
            log.error("捐赠单的状态不是未支付 payUid:{} orderStatus:{}", contributeOrder.getPayUid(), contributeOrder.getOrderStatus());
            return true;
        }

        //更新案例的状态
        contributeOrderService.updatePaySuccess(innerCallBack);

        contributeOrderService.sendContributeActionMsg(innerCallBack.getOrderId(),
                CfContributeMsgBody.OrderAction.PAY_SUCCESS);

        return true;
    }

    public String submitRefundApply(ContributeRefundSubmitVo submitVo) {
        List<CfContributeOrder> allOrderList = contributeOrderService.selectByPayUids(Lists.newArrayList(submitVo.getPayUid()));

        if (CollectionUtils.isEmpty(allOrderList) || allOrderList.get(0).getUserId() != submitVo.getUserId()
                || allOrderList.get(0).getOrderStatus() != CfContributeOrder.OrderStatus.PAY_SUCCESS.getCode()) {
            return "无法提交申请，如有疑问请拨打************";
        }

        if (StringUtils.isEmpty(submitVo.getRefundReason()) || StringUtils.isEmpty(submitVo.getMobile())) {
            return "参数不合法";
        }

        if (MobileUtil.illegal(submitVo.getMobile())) {
            log.info("手机号参数不合法");
        }

        if (StringUtils.isEmpty(submitVo.getRefundName())) {
            submitVo.setRefundName("系统默认");
        }

        CfContributeRefundParam refundParam = new CfContributeRefundParam();
        refundParam.setOrderId(allOrderList.get(0).getId());
        refundParam.setMobile(submitVo.getMobile());
        refundParam.setUsername(submitVo.getRefundName());
        refundParam.setRefundAmountInFen(submitVo.getAmountInFen());
        refundParam.setRefundReason(submitVo.getRefundReason());

        FeignResponse<String> feignResponse = refundRecordFeignClient.contributeOrderRefund(refundParam);
        log.info("捐赠退款 param:{} result:{}", JSON.toJSONString(refundParam), JSON.toJSONString(feignResponse));
        if (feignResponse == null || feignResponse.notOk()) {
            log.info("捐赠退款失败 param:{}", JSON.toJSONString(refundParam));
            return "无法提交申请，如有疑问请拨打************";
        }
        return  "";
    }
}
