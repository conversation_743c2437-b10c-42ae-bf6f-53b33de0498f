package com.shuidihuzhu.cf.financetoc.biz.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.CfFinishStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;

import java.util.Collection;
import java.util.Map;

@SuppressWarnings({"all"})
public interface CfInfoExtBiz {

	CfInfoExt getByInfoUuid(String infoUuid);

	Map<Integer, CfInfoExt> getCaseIdMapping(Collection<Integer> caseIds);

	int updateFinishStatusProtect(String infoUuid, CfFinishStatus finishStatus);

}
