package com.shuidihuzhu.cf.financetoc.biz.crowdfunding.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.enums.crowdfunding.BankCardVerifyStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.CfPropertyTransferEnum;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CfFirstApproveBiz;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingInfoSimpleBiz;
import com.shuidihuzhu.cf.financetoc.biz.order.CfOrderTransferHistoryBiz;
import com.shuidihuzhu.cf.financetoc.dao.crowdfunding.CrowdfundingInfoDao;
import com.shuidihuzhu.cf.financetoc.service.CryptoRelationService;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CfPropertyTransferHistory;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * Created by wuxinlong on 6/21/16.
 */

@Service
@Slf4j
@RefreshScope
public class CrowdfundingInfoBizImpl implements CrowdfundingInfoBiz {

    @Autowired
    private CrowdfundingInfoDao crowdfundingInfoDao;
    @Autowired
    private CryptoRelationService cryptoRelationService;
    @Resource
    private CrowdfundingInfoSimpleBiz infoSimpleBiz;
    @Resource
    private CfFirstApproveBiz firstApproveBiz;
    @Resource
    private CfOrderTransferHistoryBiz cfOrderTransferHistoryBiz;

    @Override
    public int updateRelationType(CrowdfundingInfo crowdfundingInfo) {
        return crowdfundingInfoDao.updateRelationType(crowdfundingInfo);
    }

    @Override
    public int updatePayeeInfo(CrowdfundingInfo crowdfundingInfo) {
        return crowdfundingInfoDao.updatePayeeInfo(crowdfundingInfo);
    }

    @Override
    public CrowdfundingInfo getFundingInfo(String infoId) {
        if (StringUtils.isBlank(infoId)) {
            return null;
        }
        return crowdfundingInfoDao.getFundingInfo(infoId);
    }


    @Override
    public CrowdfundingInfo getFundingInfoById(Integer id) {
        return crowdfundingInfoDao.getFundingInfoById(id);
    }

    @Override
    public boolean isRechargeable(CrowdfundingInfo crowdfundingInfo) {
        if (crowdfundingInfo != null) {
            return crowdfundingInfo.getEndTime().getTime() > System.currentTimeMillis();
        }
        return false;
    }

    @Override
    public int updateEndTime(int id, Date endTime) {
        return this.crowdfundingInfoDao.updateEndTime(id, endTime);
    }


    @Override
    public List<CrowdfundingInfo> selectByUserId(long userId) {
        return crowdfundingInfoDao.selectByUserId(userId);
    }

    @Override
    public int updateVerifyStatus(int id, BankCardVerifyStatus verifyStatus,
                                  String bankCardVerifyMessage,
                                  String bankCardVerifyMessage2) {
        if (BankCardVerifyStatus.passed.equals(verifyStatus)) {
            CrowdfundingInfo crowdfundingInfo = crowdfundingInfoDao.getFundingInfoById(id);
            if (crowdfundingInfo != null) {
                cryptoRelationService.sendPayeeInfoRelation(crowdfundingInfo);
            }
        }
        return crowdfundingInfoDao.updateVerifyStatus(id, verifyStatus,
                bankCardVerifyMessage, bankCardVerifyMessage2);

    }

    @Override
    public int addAmount(int id, int amount) {
        return crowdfundingInfoDao.addAmount(id, amount);
    }

    public List<CrowdfundingInfo> getCrowdfundingInfoListByUserId(long userId) {
        return crowdfundingInfoDao.getByUserId(userId);
    }

    public int updateUserId(CrowdfundingInfo crowdfundingInfo) {
        return crowdfundingInfoDao.updateUserId(crowdfundingInfo);
    }

    @Override
    public void transferCf(long fromUserId, long toUserId, int caseId) {
        List<CrowdfundingInfo> crowdfundingInfoList = getCrowdfundingInfoListByUserId(fromUserId);
        if (CollectionUtils.isEmpty(crowdfundingInfoList)) {
            return;
        }

        List<CfPropertyTransferHistory> historyList = Lists.newArrayList();

        crowdfundingInfoList.stream()
                .filter(f -> {
                    if (caseId > 0)
                        return f.getId() == caseId;
                    return true;
                })
                .forEach(info -> {
                    info.setUserId(toUserId);
                    log.info("转移案例, fromUserId:{}\ttoUserId:{}\tcfINfo:{}", fromUserId, toUserId, info);
                    updateUserId(info);

                    CfPropertyTransferHistory history = new CfPropertyTransferHistory(
                            fromUserId,
                            toUserId,
                            info.getId(),
                            CfPropertyTransferEnum.USER_STAT_BLESSING
                    );
                    historyList.add(history);
                    // 删除案例缓冲
                    infoSimpleBiz.deleteCaseKey(info);
                });

        List<CfFirsApproveMaterial> firstCases = firstApproveBiz.transferUserId(fromUserId, toUserId, caseId);
        firstCases.forEach(caseInfo -> {
            historyList.add(new CfPropertyTransferHistory(
                    fromUserId,
                    toUserId,
                    caseInfo.getId(),
                    CfPropertyTransferEnum.USER_FIRST_APPROVE_CASE));
        });

        cfOrderTransferHistoryBiz.addBatch(historyList);
    }
}
