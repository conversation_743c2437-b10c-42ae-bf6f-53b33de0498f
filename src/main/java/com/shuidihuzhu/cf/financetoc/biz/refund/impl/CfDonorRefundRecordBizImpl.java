package com.shuidihuzhu.cf.financetoc.biz.refund.impl;


import com.shuidihuzhu.cf.financetoc.biz.refund.CfDonorRefundRecordBiz;
import com.shuidihuzhu.cf.financetoc.dao.refund.CfDonorRefundRecordDao;
import com.shuidihuzhu.cf.model.crowdfunding.pay.CfDonorRefundRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: lianghongchao
 * @Date: 2019/3/18 15:54
 */
@Service
public class CfDonorRefundRecordBizImpl implements CfDonorRefundRecordBiz {
    @Autowired
    private CfDonorRefundRecordDao cfDonorRefundRecordDao;

    @Override
    public int add(CfDonorRefundRecord cfDonorRefundRecord) {
        if (null == cfDonorRefundRecord) {
            return 0;
        }
        return cfDonorRefundRecordDao.add(cfDonorRefundRecord);
    }

    @Override
    public CfDonorRefundRecord getByUserId(long usrId) {
        return cfDonorRefundRecordDao.getByUserId(usrId);
    }
}
