package com.shuidihuzhu.cf.financetoc.biz.refund.impl;


import com.shuidihuzhu.cf.financetoc.biz.refund.CfDonorRefundBlacklistBiz;
import com.shuidihuzhu.cf.financetoc.dao.refund.CfDonorRefundBlacklistDao;
import com.shuidihuzhu.cf.model.crowdfunding.pay.CfDonorRefundBlacklist;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: lianghongchao
 * @Date: 2019/3/18 15:54
 */
@Service
public class CfDonorRefundBlacklistBizImpl implements CfDonorRefundBlacklistBiz {
    @Autowired
    private CfDonorRefundBlacklistDao cfDonorRefundBlacklistDao;

    @Override
    public CfDonorRefundBlacklist getByUserId(long userId) {
        return cfDonorRefundBlacklistDao.getByUserId(userId);
    }
}
