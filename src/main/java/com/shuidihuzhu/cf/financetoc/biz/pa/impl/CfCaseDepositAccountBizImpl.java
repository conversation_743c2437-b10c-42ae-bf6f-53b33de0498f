package com.shuidihuzhu.cf.financetoc.biz.pa.impl;

import com.shuidihuzhu.cf.finance.model.deposit.CfCaseDepositAccount;
import com.shuidihuzhu.cf.financetoc.biz.pa.CfCaseDepositAccountBiz;
import com.shuidihuzhu.cf.financetoc.dao.pa.CfCaseDepositAccountDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019年12月13日15:37:47
 */
@Service
@Slf4j
public class CfCaseDepositAccountBizImpl implements CfCaseDepositAccountBiz {
    @Autowired
    private CfCaseDepositAccountDao cfCaseDepositAccountDao;

    /**
     * 获取案例子账户
     *
     * @param caseId
     * @return
     */
    @Override
    public CfCaseDepositAccount getByCaseId(int caseId) {
        return cfCaseDepositAccountDao.getByCaseId(caseId);
    }
}
