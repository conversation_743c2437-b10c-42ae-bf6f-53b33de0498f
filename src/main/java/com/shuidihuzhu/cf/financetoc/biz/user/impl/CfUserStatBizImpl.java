package com.shuidihuzhu.cf.financetoc.biz.user.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.financetoc.biz.user.CfUserStatBiz;
import com.shuidihuzhu.cf.financetoc.dao.user.CfUserStatDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfUserStat;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;


@Slf4j
@Service
@RefreshScope
public class CfUserStatBizImpl implements CfUserStatBiz {

    @Autowired
    private CfUserStatDao cfUserStatDao;

    @Override
    public void updateBatch(List<CfUserStat> cfUserStats) {
        if (CollectionUtils.isEmpty(cfUserStats)) {
            return;
        }
        cfUserStatDao.updateBatch(cfUserStats);
    }

    @Override
    public List<CfUserStat> getByUserIds(Set<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }

        return cfUserStatDao.getByUserIds(userIds);
    }

    @Override
    public CfUserStat getByUserId(Long userId) {
        if (userId == null || userId == 0) {
            return null;
        }
        return cfUserStatDao.selectByUserId(userId);
    }

    @Override
    public int insertContainStatus(CfUserStat toUserStat) {
        return cfUserStatDao.insertContainStatus(toUserStat);
    }
}
