package com.shuidihuzhu.cf.financetoc.biz.order.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.financetoc.biz.order.CrowdfundingPayRecordBiz;
import com.shuidihuzhu.cf.financetoc.dao.order.CrowdfundingPayRecordShardingOrderIdDao;
import com.shuidihuzhu.cf.financetoc.dao.order.CrowdfundingPayRecordShardingPayUidDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecordShardingModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * Created by lgj on 16/6/21.
 */
@Slf4j
@Service
@RefreshScope
public class CrowdfundingPayRecordBizImpl implements CrowdfundingPayRecordBiz {

    @Autowired
    private CrowdfundingPayRecordShardingOrderIdDao crowdfundingPayRecordShardingOrderIdDao;
    @Autowired
    private CrowdfundingPayRecordShardingPayUidDao crowdfundingPayRecordShardingPayUidDao;

    @Override
    public void addPayRecordNewInstance(CrowdfundingPayRecord record) {
        try {
            crowdfundingPayRecordShardingOrderIdDao.addPayRecord(record);
            crowdfundingPayRecordShardingPayUidDao.addPayRecord(record);
        } catch (Exception e) {
            log.error("addPayRecordNewInstanceERR", e);
        }
    }

    @Override
    public CrowdfundingPayRecord getByPayUid(String payUid) {
        return this.getByPayUidFromSharding(payUid);
    }

    @Override
    public double getDecimalAmount(Integer amount) {
        return amount * 1.0 / 100;
    }


    @Override
    public CrowdfundingPayRecord getByOrderId(Long orderId) {
        return this.getByOrderIdFromSharding(orderId);
    }

    private CrowdfundingPayRecord getByPayUidFromSharding(String payUid) {
        CrowdfundingPayRecordShardingModel shardingModel = crowdfundingPayRecordShardingPayUidDao.getPayRecordShardingModelByPayUid(payUid);
        if (shardingModel==null){
            return null;
        }
        List<CrowdfundingPayRecord> crowdfundingPayRecordList = crowdfundingPayRecordShardingOrderIdDao.getByOrderIdsAndPayUids(
                Lists.newArrayList(shardingModel.getCrowdfundingOrderId()),
                Lists.newArrayList(shardingModel.getPayUid()));
        return CollectionUtils.isEmpty(crowdfundingPayRecordList)?null:crowdfundingPayRecordList.get(0);
    }

    private CrowdfundingPayRecord getByOrderIdFromSharding(Long orderId) {
        if (orderId == null || orderId <= 0) {
            return null;
        }
        return crowdfundingPayRecordShardingOrderIdDao.getByOrderId(orderId);
    }
}
