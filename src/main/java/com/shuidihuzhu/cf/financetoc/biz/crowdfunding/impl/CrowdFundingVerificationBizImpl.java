package com.shuidihuzhu.cf.financetoc.biz.crowdfunding.impl;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdFundingVerificationBiz;
import com.shuidihuzhu.cf.financetoc.dao.crowdfunding.CrowdFundingVerificationDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification;
import com.shuidihuzhu.cipher.ShuidiCipher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/10/18.
 */
@Slf4j
@Service
public class CrowdFundingVerificationBizImpl implements CrowdFundingVerificationBiz {

    @Autowired
    private CrowdFundingVerificationDao crowdFundingVerificationDao;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Override
    public List<CrowdFundingVerification> queryByVerifyUserId(long verifyUserId) {
        if (verifyUserId <= 0) {
            return Lists.newArrayList();
        }
        List<CrowdFundingVerification> list= crowdFundingVerificationDao.queryByVerifyUserId(verifyUserId);
        if( list!=null && list.size()>0) {
            for (CrowdFundingVerification verification:list) {
                if(!Strings.isNullOrEmpty(verification.getEncryptMobile())){
                    verification.setMobile(shuidiCipher.decrypt(verification.getEncryptMobile()));
                }else {
                    verification.setMobile("");
                }
            }
        }
        return list;
    }

    @Override
    public int updateVerifyUserId(long id, long verifyUserId) {

        return crowdFundingVerificationDao.updateVerifyUserId(id, verifyUserId);
    }
}
