package com.shuidihuzhu.cf.financetoc.biz.payee.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.financetoc.biz.payee.CfCharityPayeeBiz;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingAttachmentBiz;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingInfoStatusBiz;
import com.shuidihuzhu.cf.financetoc.dao.payee.CfCharityPayeeDao;
import com.shuidihuzhu.cf.financetoc.service.IPayeeMaterialCenterService;
import com.shuidihuzhu.cf.model.crowdfunding.CfCharityPayee;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2018/11/15
 */
@Service
@Slf4j
public class CfCharityPayeeBizImpl implements CfCharityPayeeBiz {
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private CfCharityPayeeDao charityPayeeDao;

    @Autowired
    private CrowdfundingAttachmentBiz crowdfundingAttachmentBiz;

    @Autowired
    private CrowdfundingInfoStatusBiz crowdfundingInfoStatusBiz;

    @Autowired
    private IPayeeMaterialCenterService payeeMaterialService;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;


    @Override
    public CfErrorCode insertOrUpdate(CfCharityPayee cfCharityPayee, long userId) {

        String uuid = cfCharityPayee.getInfoUuid();

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(uuid);

        if (crowdfundingInfo == null) {
            return CfErrorCode.SYSTEM_NOT_EXIST_RECORD_ERROR;
        }

        CfErrorCode cc = verifyInfoId(crowdfundingInfo, userId);

        if (cc != CfErrorCode.SUCCESS) {
            return cc;
        }
        //加密
        cfCharityPayee.setOrgMobile(oldShuidiCipher.aesEncrypt(cfCharityPayee.getOrgMobile()));
        cfCharityPayee.setOrgBankCard(oldShuidiCipher.aesEncrypt(cfCharityPayee.getOrgBankCard()));

        CfCharityPayee cp = charityPayeeDao.getByCaseUUid(uuid);

        //保存上传的图片
        savePic(crowdfundingInfo.getId(), cfCharityPayee.getOrgPic());

        //更新
        if (cp != null) {
            charityPayeeDao.update(cfCharityPayee);
            crowdfundingInfoStatusBiz.updateByInfoId(uuid,
                    CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode(),
                    CrowdfundingInfoStatusEnum.UN_SUBMITTED);
            //更新打款方式为慈善组织
            crowdfundingInfo.setRelationType(CrowdfundingRelationType.charitable_organization);
            crowdfundingInfoBiz.updateRelationType(crowdfundingInfo);
            return CfErrorCode.SUCCESS;
        }
        //增加caseid 方便后台查询
        cfCharityPayee.setCaseId(crowdfundingInfo.getId());
        charityPayeeDao.insert(cfCharityPayee);
        //材料状态
        addInfoStatus(uuid, crowdfundingInfo.getId());
        //更新打款方式为慈善组织
        crowdfundingInfo.setRelationType(CrowdfundingRelationType.charitable_organization);
        crowdfundingInfoBiz.updateRelationType(crowdfundingInfo);

        payeeMaterialService.addOrUpdateCharity(crowdfundingInfo.getId(), cfCharityPayee, null);

        return CfErrorCode.SUCCESS;
    }


    private void addInfoStatus(String infoUuid, int caseId) {

        if (crowdfundingInfoStatusBiz.getByInfoUuidAndType(infoUuid,
                CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode()) == null) {
            // 提交收款人信息状态
            CrowdfundingInfoStatus crowdfundingInfoStatus = new CrowdfundingInfoStatus();
            crowdfundingInfoStatus.setInfoUuid(infoUuid);
            crowdfundingInfoStatus.setType(CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode());
            crowdfundingInfoStatus.setStatus(CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode());
            // 新增案例id
            crowdfundingInfoStatus.setCaseId(caseId);
            this.crowdfundingInfoStatusBiz.add(crowdfundingInfoStatus);
        } else {
            this.crowdfundingInfoStatusBiz.updateByInfoId(infoUuid,
                    CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode(),
                    CrowdfundingInfoStatusEnum.UN_SUBMITTED);
        }

    }


    private void savePic(int caseId, String pic) {

        //先删除
        List<AttachmentTypeEnum> typeList = Lists.newArrayList(AttachmentTypeEnum.ATTACH_CHARITY);
        crowdfundingAttachmentBiz.deleteByParentIdAndType(caseId, typeList);

        //再插入
        List<CrowdfundingAttachment> attachmentList = Lists.newArrayList();
        attachmentList.add(new CrowdfundingAttachment(caseId, AttachmentTypeEnum.ATTACH_CHARITY, pic));

        crowdfundingAttachmentBiz.add(attachmentList);
    }


    //检验能否被修改
    private CfErrorCode verifyInfoId(CrowdfundingInfo crowdfundingInfo, long requestUserId) {
        if (crowdfundingInfo == null) {
            return CfErrorCode.CF_NOT_FOUND;
        }
        if (crowdfundingInfo.getStatus() != CrowdfundingStatus.APPROVE_PENDING
                && crowdfundingInfo.getStatus() != CrowdfundingStatus.APPROVE_DENIED) {
            return CfErrorCode.CF_CAN_NOT_EDIT;
        }
        if (crowdfundingInfo.getUserId() != requestUserId) {
            return CfErrorCode.CF_INFO_PARAM_ERROR_IDENTITY_INVALID;
        }
        return CfErrorCode.SUCCESS;
    }


}
