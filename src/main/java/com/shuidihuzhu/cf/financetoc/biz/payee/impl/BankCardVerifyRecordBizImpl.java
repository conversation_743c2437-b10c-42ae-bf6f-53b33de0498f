package com.shuidihuzhu.cf.financetoc.biz.payee.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.verify.client.menu.CertTypeEnum;
import com.shuidihuzhu.account.verify.client.menu.ElementTypeEnum;
import com.shuidihuzhu.account.verify.client.model.MultiElementParam;
import com.shuidihuzhu.account.verify.client.model.MultiElementResult;
import com.shuidihuzhu.account.verify.client.service.VerifyMultiElementClient;
import com.shuidihuzhu.cf.enums.crowdfunding.BankCardVerifyStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.financetoc.biz.payee.BankCardVerifyRecordBiz;
import com.shuidihuzhu.cf.financetoc.dao.payee.BankCardVerifyRecordDao;
import com.shuidihuzhu.cf.model.crowdfunding.BankCardVerifyRecord;
import com.shuidihuzhu.cf.model.crowdfunding.BankCardVerifyResult;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;

/**
 * Author: Wesley Wu Date: 16/9/21 15:29
 */
@Slf4j
@Service
@RefreshScope
public class BankCardVerifyRecordBizImpl implements BankCardVerifyRecordBiz {
    @Autowired
    private BankCardVerifyRecordDao bankCardVerifyRecordDao;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Autowired
    private VerifyMultiElementClient verifyMultiElementClient;
    @Value("${spring.application.name:}")
    private String applicationName;

    /**
     * 平安管存的 1、普通绑卡没有单号 2、需要联行号的，traceNo 为 联行号
     * 微众的 verify-起始
     * 新渠道 verify-v2-
     *
     * @param bankCardVerifyRecord
     * @return
     */
    @Override
    public int add(BankCardVerifyRecord bankCardVerifyRecord) {
        //2019年12月26日19:42:00 将字符串 null的都转为 "" 空
        Function<String, String> f = a -> StringUtils.trimToEmpty(a);
        bankCardVerifyRecord.setCryptoHolderName(f.apply(bankCardVerifyRecord.getCryptoHolderName()));
        bankCardVerifyRecord.setCryptoIdCard(f.apply(bankCardVerifyRecord.getCryptoIdCard()));
        bankCardVerifyRecord.setCryptoBankCard(f.apply(bankCardVerifyRecord.getCryptoBankCard()));
        bankCardVerifyRecord.setCryptoMobile(f.apply(bankCardVerifyRecord.getCryptoMobile()));

        bankCardVerifyRecord.setVerifyMessage(f.apply(bankCardVerifyRecord.getVerifyMessage()));
        bankCardVerifyRecord.setVerifyMessage2(f.apply(bankCardVerifyRecord.getVerifyMessage2()));
        bankCardVerifyRecord.setTradeNo(f.apply(bankCardVerifyRecord.getTradeNo()));
        bankCardVerifyRecord.setThirdTradeNo(f.apply(bankCardVerifyRecord.getThirdTradeNo()));


        return bankCardVerifyRecordDao.add(bankCardVerifyRecord);
    }

    private BankCardVerifyResult getResultByThreeElements(String cryptoHolderName, String cryptoIdCard,
                                                          String cryptoBankCard) {
        BankCardVerifyRecord bankCardVerifyRecord = getRecordByThreeElements(cryptoHolderName, cryptoIdCard,
                cryptoBankCard);
        if (bankCardVerifyRecord == null) {
            return null;
        }
        return getBankCardVerifyResult(bankCardVerifyRecord);
    }

    private BankCardVerifyRecord getRecordByThreeElements(String cryptoHolderName, String cryptoIdCard,
                                                          String cryptoBankCard) {
        BankCardVerifyRecord bankCardVerifyRecord = this.bankCardVerifyRecordDao.getByThreeElements(cryptoHolderName,
                cryptoIdCard, cryptoBankCard, BankCardVerifyStatus.passed);
        if (bankCardVerifyRecord != null) {
            return bankCardVerifyRecord;
        }
        return null;
    }

    private BankCardVerifyResult getBankCardVerifyResult(BankCardVerifyRecord bankCardVerifyRecord) {
        if (bankCardVerifyRecord == null) {
            return null;
        }
        BankCardVerifyResult bankCardVerifyResult = null;
        BankCardVerifyStatus verifyStatus = bankCardVerifyRecord.getVerifyStatus();
        if (verifyStatus == BankCardVerifyStatus.passed) {
            bankCardVerifyResult = new BankCardVerifyResult();
            bankCardVerifyResult.setNowpayTransId(bankCardVerifyRecord.getThirdTradeNo());
            bankCardVerifyResult.setResponseCode("0000");
            bankCardVerifyResult.setFinalMessage(bankCardVerifyRecord.getVerifyMessage());
            bankCardVerifyResult.setResponseMsg(bankCardVerifyRecord.getVerifyMessage2());
        } else if (verifyStatus == BankCardVerifyStatus.failed) {
            bankCardVerifyResult = new BankCardVerifyResult();
            bankCardVerifyResult.setNowpayTransId(bankCardVerifyRecord.getThirdTradeNo());
            bankCardVerifyResult.setResponseCode("9999");
            bankCardVerifyResult.setFinalMessage(bankCardVerifyRecord.getVerifyMessage());
            bankCardVerifyResult.setResponseMsg(bankCardVerifyRecord.getVerifyMessage2());
        }
        return bankCardVerifyResult;
    }

    @Override
    public BankCardVerifyResult verify(String holderName, UserIdentityType identityType, String idCard,
                                       String bankCardNum, int crowdfundingId, long userId, boolean updateStatus) {
        if (userId <= 0 || StringUtils.isBlank(holderName) || StringUtils.isBlank(idCard) || StringUtils.isBlank(bankCardNum)) {
            return new BankCardVerifyResult("", "0002", "参数为空或不合法", "fail");
        }
        BankCardVerifyResult result;
        String cryptoIdCard = oldShuidiCipher.aesEncrypt(idCard);
        String cryptoBankCard = oldShuidiCipher.aesEncrypt(bankCardNum);
        // 取的本地是否存在已经验证通过的数据
        result = this.getResultByThreeElements(holderName, cryptoIdCard, cryptoBankCard);
        String orderNum = "verify-" + generateCode(crowdfundingId);
        if (result != null) {
            log.info("crowdfundingId:{} holderName:{} 验证从缓存数据获取", crowdfundingId, holderName);
        } else {
            orderNum = "verify-v2-" + generateCode(crowdfundingId);
            result = this.verifyV2(holderName, idCard, bankCardNum, orderNum, userId);
        }

        BankCardVerifyStatus verifyStatus;
        if (result != null && result.isOk()) {
            verifyStatus = BankCardVerifyStatus.passed;
        } else {
            verifyStatus = BankCardVerifyStatus.failed;
        }

        BankCardVerifyRecord record = new BankCardVerifyRecord(crowdfundingId, holderName, identityType,
                cryptoIdCard, cryptoBankCard, verifyStatus, result.getFinalMessage(),
                result.getResponseMsg(), orderNum, result.getNowpayTransId());
        log.info("bankCardVerify result:{}", JSON.toJSONString(result));
        // 记录结果
        this.add(record);
        return result;
    }

    private BankCardVerifyResult verifyV2(String holderName, String idCard, String bankCardNum, String bizSn, long userId) {
        if (userId <= 0 || StringUtils.isBlank(holderName) || StringUtils.isBlank(idCard) || StringUtils.isBlank(bankCardNum)) {
            return new BankCardVerifyResult("", "0002", "参数为空或不合法", "fail");
        }
        MultiElementParam multiElementParam = MultiElementParam.builder()
                // 业务线类型 1.水滴互助 2.水滴筹 3.水滴保 4.诚光咨询
                .bizType(2)
                // 服务名称
                .serviceName(applicationName)
                //此次请求流水号，每次请求不可重复
                .bizSn(bizSn)
                // 要素鉴权类型
                .elementType(ElementTypeEnum.THIRD_ELEMENT)
                //证件类型
                .certType(CertTypeEnum.ID_CARD)
                //用户姓名
                .name(holderName)
                //加密身份证号
                .cryptoIdCard(oldShuidiCipher.aesEncrypt(idCard))
                //加密银行卡号
                .cryptoBankCard(oldShuidiCipher.aesEncrypt(bankCardNum))
                .build();
        Response<MultiElementResult> response = verifyMultiElementClient.verify(multiElementParam);
        if (response == null) {
            response = NewResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
        }
        log.info("bizSn:{} response:{}", bizSn, JSON.toJSONString(response));
        // code为0则代表此次请求成功
        MultiElementResult multiElementResult = response.getData();
        if (null == multiElementResult) {
            return BankCardVerifyResult.build(StringUtils.EMPTY, String.valueOf(response.getCode()),
                    StringUtils.trimToEmpty(response.getMsg()), "核实参数信息格式");
        }
        String responseCode;
        if (multiElementResult.getStatus().equals(30000)) {
            log.info("验证一致");
            responseCode = "0000";
        } else {
            responseCode = String.valueOf(multiElementResult.getStatus());
        }
        BankCardVerifyResult result = null;
        result = BankCardVerifyResult.build(multiElementResult.getVerifySn(), responseCode,
                multiElementResult.getThirdMsg(), multiElementResult.getMsg());
        log.info("result:{}", JSON.toJSONString(result));
        return result;
    }


    private String generateCode(int crowdfundingId) {
        int randomInt = ThreadLocalRandom.current().nextInt(99999);
        DecimalFormat df = new DecimalFormat("00000");
        String randomStr = df.format(randomInt);
        return System.currentTimeMillis() + "_" + crowdfundingId + "_" + randomStr;
    }

    @Override
    public List<BankCardVerifyRecord> getByCaseIdAndElements(String holderName, String cryptoIdCard,
                                                             String cryptoBankCard, int caseId) {
        if (StringUtils.isEmpty(holderName) || StringUtils.isEmpty(cryptoBankCard) || StringUtils.isEmpty(cryptoIdCard)
                || caseId <= 0) {
            return Lists.newArrayList();
        }
        return bankCardVerifyRecordDao.getByCaseIdAndElements(holderName, cryptoIdCard, cryptoBankCard, caseId);
    }

}