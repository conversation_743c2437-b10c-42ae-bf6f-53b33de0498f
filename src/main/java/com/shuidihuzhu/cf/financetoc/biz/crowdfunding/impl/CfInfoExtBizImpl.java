package com.shuidihuzhu.cf.financetoc.biz.crowdfunding.impl;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.enums.crowdfunding.CfFinishStatus;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CfInfoExtBiz;
import com.shuidihuzhu.cf.financetoc.dao.crowdfunding.CfInfoExtDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Service
public class CfInfoExtBizImpl implements CfInfoExtBiz {

    @Autowired
    private CfInfoExtDao cfInfoExtDao;


    @Override
    public CfInfoExt getByInfoUuid(String infoUuid) {
        if (StringUtils.isBlank(infoUuid)) {
            return null;
        }

        return this.cfInfoExtDao.getByInfoUuid(infoUuid);
    }

    @Override
    public int updateFinishStatusProtect(String infoUuid, CfFinishStatus finishStatus) {
        if (StringUtils.isBlank(infoUuid) || finishStatus == null) {
            return -1;
        }
        return this.cfInfoExtDao.updateFinishStatus(infoUuid, finishStatus.getValue());
    }

    @Override
    public Map<Integer, CfInfoExt> getCaseIdMapping(Collection<Integer> caseIds) {
        if (CollectionUtils.isEmpty(caseIds)) {
            return Maps.newHashMap();
        }

        Map<Integer, CfInfoExt> caseIdMapping = Maps.newHashMap();
        List<CfInfoExt> infoExtList = cfInfoExtDao.getByCaseIds(caseIds);
        for (CfInfoExt infoExt : infoExtList) {
            caseIdMapping.put(infoExt.getCaseId(), infoExt);
        }
        return caseIdMapping;
    }
}
