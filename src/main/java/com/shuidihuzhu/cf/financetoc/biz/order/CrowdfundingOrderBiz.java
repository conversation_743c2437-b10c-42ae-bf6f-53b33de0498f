package com.shuidihuzhu.cf.financetoc.biz.order;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrderExt;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Created by lgj on 16/6/21.
 */
public interface CrowdfundingOrderBiz extends CrowdfundingOrderShardingBiz {

	void addOrderNewInstance(CrowdfundingOrder order,String splitFlowUuid, String shareSourceId, int cfStatus, int DataStatus,int blessingCardId);

	CrowdfundingOrder getById(Long id);

	List<CrowdfundingOrder> getByUserId(Set<Long> userIds, Integer crowdfundingId);

	List<CrowdfundingOrder> getListByUserId(int crowdfundingId,long userId, int limit);

	/**
	 * 查询
	 * @param orderId
	 * @param ctime
	 * @return
	 */
	CrowdfundingOrderExt getCrowdfundingOrderExtByOrderId(long orderId, Date ctime);

	void updateUserIdBatch(List<CrowdfundingOrder> orders,long fromUserId);

}
