package com.shuidihuzhu.cf.financetoc.biz.crowdfunding.impl;

import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingOperationEnum;
import com.shuidihuzhu.cf.financetoc.biz.crowdfunding.CrowdfundingOperationBiz;
import com.shuidihuzhu.cf.financetoc.dao.crowdfunding.CrowdfundingInfoDao;
import com.shuidihuzhu.cf.financetoc.dao.crowdfunding.CrowdfundingOperationDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CrowdfundingOperationBizImpl implements CrowdfundingOperationBiz {

	@Autowired
	private CrowdfundingOperationDao crowdfundingOperationDao;
	@Autowired
	private CrowdfundingInfoDao crowdfundingInfoDao;

	@Override
	public int add(CrowdfundingOperation crowdfundingOperation) {
        if (crowdfundingOperation.getFollowType() == null){
            crowdfundingOperation.setFollowType(0);
        }
        //没有设置caseId,通过查crowdfunding_info设置
		if (crowdfundingOperation.getCaseId() == 0) {
			CrowdfundingInfo fundingInfo = crowdfundingInfoDao.getFundingInfo(crowdfundingOperation.getInfoId());
			crowdfundingOperation.setCaseId(fundingInfo.getId());
		}
		return crowdfundingOperationDao.add(crowdfundingOperation);
	}

	@Deprecated
	@Override
	public int update(CrowdfundingOperation crowdfundingOperation) {
		return crowdfundingOperationDao.update(crowdfundingOperation);
	}

	@Override
	public int update(String infoUuid, CrowdfundingOperationEnum operationEnum, int operatorId, String reason) {
		return crowdfundingOperationDao.updateOperation(infoUuid, operationEnum.value(), operatorId, reason);
	}

	@Override
	public CrowdfundingOperation getByInfoId(String infoId) {
		return crowdfundingOperationDao.getByInfoId(infoId);
	}

}
