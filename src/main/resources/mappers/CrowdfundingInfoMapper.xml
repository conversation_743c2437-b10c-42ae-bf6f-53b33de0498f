<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.financetoc.dao.crowdfunding.CrowdfundingInfoDao">
	<sql id="tableName">
		crowdfunding_info
	</sql>

    <sql id="fields">
		`id`, `info_id`, `user_id`,
		`relation`, `applicant_name`, `applicant_qq`, `applicant_mail`, `relation_type`, `channel_type`,`channel`,
		`payee_name`, `payee_id_card`, `payee_mobile`, `payee_bank_name`, `payee_bank_branch_name`, `payee_bank_card`,
		`bank_card_verify_status`,
		`bank_card_verify_message`, `bank_card_verify_message2`, `title`, `title_img`, `content`, `encrypt_content`,
		`target_amount`, `amount`, `donation_count`, `status`, `create_time`, `begin_time`, `end_time`, `from`, `use`,`data_status`,
		`type`, `content_type`, `material_plan_id`, `content_image`, `content_image_status`
	</sql>

	<sql id="simple_fields">
		`id`, `info_id`, `user_id`, `target_amount`, `amount`, `create_time`, `begin_time`, `end_time`
	</sql>


	<select id="getFundingInfo" parameterType="String"
	        resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT
		<include refid="fields"/>
		FROM
		<include refid="tableName"/>
		WHERE
		info_id = #{infoId}
	</select>

	<select id="getFundingInfoById" parameterType="int" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT
		<include refid="fields"/>
		FROM
		<include refid="tableName"/>
		WHERE id = #{id}
	</select>

	<select id="selectByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT <include refid="fields"/>
		FROM <include refid="tableName"/>
		WHERE `user_id`=#{userId}
	</select>


	<update id="addAmount">
		UPDATE
		<include refid="tableName"/>
		SET
		amount = amount + #{amount},
		donation_count = donation_count + 1
		WHERE id = #{id}
	</update>



	<update id="updatePayeeInfo" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo" useGeneratedKeys="true" keyProperty="id">
		UPDATE
		<include refid="tableName"/>
		SET
		`payee_name` = #{payeeName},
		`payee_id_card` = #{payeeIdCard},
		`payee_bank_name` = #{payeeBankName},
		`payee_bank_branch_name` = #{payeeBankBranchName},
		`payee_bank_card` = #{payeeBankCard},
		`payee_mobile` = #{payeeMobile},
		`relation` = #{relation},
		`relation_type` = #{relationType, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType}
		WHERE info_id = #{infoId}
	</update>



	<update id="updateRelationType" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		UPDATE
		<include refid="tableName"/>
		SET
		`relation_type` = #{relationType, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType}
		WHERE info_id = #{infoId}
	</update>


	<update id="updateEndTime">
		UPDATE
		<include refid="tableName"/>
		SET `end_time`=#{endTime}
		WHERE id = #{id}
	</update>

	<update id="updateVerifyStatus">
		UPDATE
		<include refid="tableName"/>
		SET `bank_card_verify_status`=#{verifyStatus},
		`bank_card_verify_message`=#{bankCardVerifyMessage},
		`bank_card_verify_message2`=#{bankCardVerifyMessage2}
		WHERE
		`id`=#{id}
	</update>

	<update id="updateUserId" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		UPDATE
		<include refid="tableName"/>
		SET
		`user_id` = #{userId}
		WHERE info_id = #{infoId}
	</update>

	<select id="getByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT
		<include refid="fields"/>
		FROM
		<include refid="tableName"/>
		WHERE user_id = #{userId}
		ORDER BY id desc
	</select>

</mapper>
