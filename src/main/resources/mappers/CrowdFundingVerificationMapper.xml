<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.financetoc.dao.crowdfunding.CrowdFundingVerificationDao">

    <sql id="tableName">
        crowd_funding_verification
    </sql>

    <insert id="saveCrowdFundingVerification" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tableName"/>
        (
          crowd_funding_info_id, patient_user_id, verify_user_id, open_id, relation_ship, user_name, description, valid, self_tag, create_time, operate_time,share_source_id,encrypt_mobile,
          province_code, hospital_name, medical_image_list
        )
        VALUES
        (
          #{crowdFundingInfoId}, #{patientUserId}, #{verifyUserId}, #{openId}, #{relationShip}, #{userName}, #{description}, #{valid}, #{selfTag}, #{createTime}, #{operateTime},#{shareSourceId},#{encryptMobile},
          #{provinceCode}, #{hospitalName} ,#{medicalImageList}
        );
    </insert>

    <update id="updateCrowdFundingVerification">
        UPDATE
        <include refid="tableName"/>
        SET
        valid = #{newValid},
        operate_time = now()
        WHERE
        id = #{verifyId} AND
        crowd_funding_info_id = #{crowdFundingInfoId} AND
        valid = #{oldValid}
    </update>

    <update id="updateCrowdFundingVerificationDescription">
        UPDATE
        <include refid="tableName"/>
        SET
        description = #{description}
        WHERE
        id = #{verifyId}
    </update>

    <select id="countCrowdFundingVerificationByInfoUuid" resultType="java.lang.Integer">
        SELECT
        COUNT(id)
        FROM
        <include refid="tableName"/>
        WHERE
        crowd_funding_info_id = #{crowdFundingInfoId} AND valid = 1
    </select>

    <select id="countVerifyCfByUserId" resultType="java.lang.Integer">
        SELECT
        COUNT(DISTINCT  crowd_funding_info_id)
        FROM
        <include refid="tableName"/>
        WHERE
        verify_user_id = #{verifyUserId} AND valid = 1
    </select>

    <select id="getByOffset" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT
        *
        FROM
        <include refid="tableName"/>
        WHERE
        `id`>#{offset} AND
        valid=1
        limit #{limit}
    </select>

    <select id="getById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT * FROM <include refid="tableName"/> WHERE id = #{id}
    </select>

    <select id="getByVerifyUserIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT
        *
        FROM
        <include refid="tableName"/>
        WHERE
        verify_user_id IN
        <foreach collection="verifyUserids" item="userId" separator="," open="(" close=")">
            #{userId}
        </foreach>
        AND
        crowd_funding_info_id = #{infoUuid} AND
        valid = 1
        ORDER BY id DESC
    </select>

    <select id="countCrowdFundingVerificationByVerifyUserId" resultType="java.lang.Integer">
        SELECT
        COUNT(id)
        FROM
        <include refid="tableName"/>
        WHERE
        verify_user_id = #{verifyUserId} AND crowd_funding_info_id = #{crowdFundingInfoId} AND valid = 1
    </select>

    <select id="countCrowdFundingVerificationByVerifyUserIdNotWithValid" resultType="java.lang.Integer">
        SELECT
        COUNT(id)
        FROM
        <include refid="tableName"/>
        WHERE
        verify_user_id = #{verifyUserId} AND crowd_funding_info_id = #{crowdFundingInfoId}
    </select>

    <select id="queryCrowdFundingVerificationByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT
        *
        FROM
        <include refid="tableName"/>
        WHERE crowd_funding_info_id = #{crowdFundingInfoId} AND valid = 1
        ORDER BY id DESC
        LIMIT #{limit}
    </select>

    <select id="queryAllCrowdFundingVerificationByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT
        *
        FROM
        <include refid="tableName"/>
        WHERE
        crowd_funding_info_id = #{crowdFundingInfoId} AND valid = 1
        ORDER BY id DESC
    </select>

    <select id="countCrowdFundingVerificationByInfoUuidAndVerifyUserId" resultType="java.lang.Integer">
        SELECT
        COUNT(id)
        FROM
        <include refid="tableName"/>
        WHERE
        verify_user_id = #{verifyUserId} AND crowd_funding_info_id = #{crowdFundingInfoId} AND valid = 1
    </select>

    <select id="countCrowdFundingVerificationByInfoUuidAndOpenId" resultType="java.lang.Integer">
        SELECT
        COUNT(id)
        FROM
        <include refid="tableName"/>
        WHERE
        crowd_funding_info_id = #{crowdFundingInfoId} AND open_id = #{openId} AND valid = 1
    </select>

    <select id="getListByAnchorId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE crowd_funding_info_id = #{crowdFundingInfoId}
        AND `id` &lt;= #{anchorId}
        AND `valid`=1
        ORDER BY `id` DESC
        LIMIT #{limit}
    </select>

    <select id="getListBetween" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE (`create_time` BETWEEN #{startTime} AND #{endTime})
        AND `valid`=1
        ORDER BY `create_time` DESC
        LIMIT #{limitStart}, #{limitSize}
    </select>

    <select id="getListByRelationships" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT *
        FROM <include refid="tableName"/> FORCE INDEX(cfid_valid_id)
        WHERE crowd_funding_info_id = #{crowdfundingInfoId}
        AND <![CDATA[ `id`<=#{anchorId} ]]>
        AND `relation_ship` in
        <foreach item="item" index="index" collection="relationships" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND `valid`=1
        ORDER BY `id` DESC
        LIMIT #{limit}
    </select>
    <select id="getByRelationships" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT *
        FROM <include refid="tableName"/> FORCE INDEX(cfid_valid_id)
        WHERE crowd_funding_info_id = #{crowdfundingInfoId}
        AND `relation_ship` in
        <foreach item="item" index="index" collection="relationships" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND `valid`=1
        ORDER BY `id` DESC
        LIMIT #{limit}
    </select>

    <select id="getFriendsVerification" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT *
        FROM <include refid="tableName"/> FORCE INDEX(cfid_valid_id)
        WHERE crowd_funding_info_id = #{crowdfundingInfoId}
        AND `verify_user_id` in
        <foreach item="item" collection="friends" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND `valid`=1
        ORDER BY `id` DESC
        LIMIT #{limit}
    </select>


    <select id="getFriendsVerificationAll" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT *
        FROM <include refid="tableName"/> FORCE INDEX(cfid_valid_id)
        WHERE crowd_funding_info_id = #{crowdfundingInfoId}
        AND `verify_user_id` in
        <foreach item="item" collection="friends" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND `valid`=1
    </select>

    <select id="getFriendsVerificationByTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT *
        FROM <include refid="tableName"/> FORCE INDEX(cfid_valid_id)
        WHERE crowd_funding_info_id = #{crowdfundingInfoId}
        AND `verify_user_id` in
        <foreach item="item" collection="friends" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND `valid`=1
        AND (`create_time` BETWEEN #{startTime} AND #{endTime})
    </select>


    <select id="countVerificationByInfoUuidAndRelationShip" resultType="java.lang.Integer">
        SELECT
        COUNT(id)
        FROM <include refid="tableName"/>
        WHERE crowd_funding_info_id = #{crowdfundingInfoId}
        AND `relation_ship` in
        <foreach item="item" index="index" collection="relationships"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        AND `valid`=1
    </select>

    <select id="getListByVerifyUserIdAndInfoIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT *
        FROM <include refid="tableName"/> FORCE INDEX(`verify_user_id`)
        WHERE `verify_user_id` = #{verifyUserId}
        AND
        `crowd_funding_info_id` in
        <foreach item="item" index="index" collection="infoUuids"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        AND `valid`=1
        LIMIT #{limit}
    </select>

    <update id="updateUserName">
        UPDATE <include refid="tableName"/>
        SET user_name = #{userName},operate_time = now()
        WHERE `verify_user_id` = #{verifyUserId}
    </update>

    <select id="getLastSuccessOne" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT *
        FROM <include refid="tableName"/> FORCE INDEX(`verify_user_id`)
        WHERE `verify_user_id` = #{verifyUserId}
        AND `valid`=1
        ORDER BY id DESC
        LIMIT 1
    </select>

    <update id="updateValid">
        UPDATE <include refid="tableName"/>
        SET `valid`=#{valid}, operate_time = now()
        WHERE `id`=#{id}
    </update>

    <select id="findByVerifyUserIdAndInfoId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT verify_user_id
        FROM <include refid="tableName"/>
        WHERE
          `verify_user_id`=#{verifyUserId}
          AND
          `crowd_funding_info_id`=#{crowdfundingInfoId}
          AND
          `valid`=1
        LIMIT 1
    </select>
    <select id="getVerifyUserIdsByInfoIds" resultType="java.lang.Long">
        SELECT `verify_user_id` as verifyUserId
        FROM <include refid="tableName"/>
        WHERE
        `crowd_funding_info_id` in
        <foreach item="item" index="index" collection="infoIds"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        AND `valid`=1
    </select>
    <select id="listByVerifyUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
         SELECT *
        FROM <include refid="tableName"/>
        WHERE
          id &lt; #{anchorId}
          and `verify_user_id`=#{verifyUserId}
          AND `valid`=1
          order by id desc
          limit #{limit}
    </select>

    <select id="queryByVerifyUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE `verify_user_id`=#{verifyUserId}
    </select>

    <select id="getByVerifyUserIdAndInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT
        *
        FROM
        <include refid="tableName"/>
        WHERE
        `verify_user_id` = #{verifyUserId}
        AND
        crowd_funding_info_id = #{infoUuid}
        ORDER BY id DESC
    </select>


    <select id="getVerifyUserIdById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT id,verify_user_id verifyUserId, relation_ship, valid, crowd_funding_info_id FROM <include refid="tableName"/> WHERE id = #{id}
    </select>


    <select id="queryByVerifyUserIdAndInfoId" resultType="java.lang.Integer">
        SELECT id
        FROM <include refid="tableName"/>
        WHERE
          `verify_user_id`=#{verifyUserId}
          AND
          `crowd_funding_info_id`=#{crowdfundingInfoId}
          AND
          `valid`=1
        LIMIT 1
    </select>

    <select id="getVerifyByIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        select *
        from <include refid="tableName"/>
        where id in
        <foreach item="item" index="index" collection="ids"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        AND `valid`=1
    </select>

    <select id="getByUserIdAndInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT *
        FROM
        <include refid="tableName"/>
        WHERE
        `verify_user_id`=#{verifyUserId}
        AND
        `crowd_funding_info_id`=#{infoUuid}
        AND
        `valid`=1
        ORDER BY id DESC
        LIMIT 1
    </select>


    <select id="getVerfiyNumToday" resultType="java.lang.Integer">
        SELECT count(1)
        FROM
        <include refid="tableName"/>
        WHERE
        `crowd_funding_info_id` in
        <foreach item="item" collection="infoUuids" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND
        `valid`=1
        and create_time > #{time}
    </select>


    <select id="getListByPatientUserIdAndAnchorId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT *
        FROM crowd_funding_verification
        WHERE `patient_user_id`=#{patientUserId}
          AND crowd_funding_info_id = #{crowdFundingInfoId}
          AND `id` &lt;= #{anchorId}
          AND `valid`=1
        ORDER BY `id` DESC
        LIMIT #{limit}
    </select>

    <update id="updateVerifyUserId">
        update <include refid="tableName"/> set `verify_user_id`=#{verifyUserId},operate_time = now()
        where `id`=#{id}
    </update>

    <select id="countCrowdFundingVerificationByVerifyUserIdAndTime" resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM
        <include refid="tableName"/>
        WHERE
        verify_user_id = #{verifyUserId} AND valid = 1
        AND (`create_time` BETWEEN #{startTime} AND #{endTime})
    </select>

    <select id="getVerificationList" resultType ="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT
        *
        FROM
        <include refid="tableName"/>
        WHERE
        id IN
        <foreach item="item" collection="verificationIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
        SELECT * FROM <include refid="tableName"/> WHERE id = #{id}
        AND valid = 1
    </select>

</mapper>

