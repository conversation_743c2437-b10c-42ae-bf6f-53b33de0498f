<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.financetoc.dao.order.CrowdfundingOrderShardingCrowdfundingIdNewDao">
	<sql id="TABLE">
        crowdfunding_order_crowdfundingid_sharding
    </sql>
	<sql id="FIELDS">
        `id`,`code`,`user_id`,`crowdfunding_id`,`amount`,`comment`,`pay_status`,
        `ctime`,`pay_time`,`valid`,`ip`,`os_type`,`channel`,`from`,`self_tag`,`user_third_id`,`user_third_type`,`anonymous`
    </sql>
	<sql id="INSERT_FIELDS">
        `id`,`code`,`user_id`,`crowdfunding_id`,`amount`,`comment`,`pay_status`,`ctime`,
        `valid`,`ip`,`os_type`,`channel`,`from`,`self_tag`,`user_third_id`,`user_third_type`,`anonymous`,`activity_id`
    </sql>

	<insert id="addOrder" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		INSERT INTO
		<include refid="TABLE"/>
		(<include refid="INSERT_FIELDS"/>)
		VALUES(#{id},#{code},#{userId},#{crowdfundingId},#{amount},#{comment},
		#{payStatus},#{ctime},#{valid},#{ip},#{osType},#{channel},#{from},#{selfTag},
		#{userThirdId},#{userThirdType},#{anonymous},#{activityId});
	</insert>

	<update id="editByIdAndComment">
		UPDATE <include refid="TABLE"/>
		SET `comment`=#{comment}
		WHERE crowdfunding_id = #{crowdfundingId} and `id`=#{id}
	</update>

	<update id="updatePayStatus">
		UPDATE
		<include refid="TABLE"/>
		SET `pay_status`=#{payStatus},`pay_time`=#{payTime}
		WHERE crowdfunding_id = #{crowdfundingId} and `id`=#{id};
	</update>

	<select id="getListByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE crowdfunding_id = #{crowdfundingId}
		and user_id = #{userId}
		and pay_status = 1
		and valid = 1
		order by id desc
		limit #{limit}
	</select>

	<update id="updateUserId" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		UPDATE
		<include refid="TABLE"/>
		SET `user_id`= #{order.userId}
		WHERE
		crowdfunding_id = #{order.crowdfundingId} and `id` = #{order.id}
	</update>
</mapper>
