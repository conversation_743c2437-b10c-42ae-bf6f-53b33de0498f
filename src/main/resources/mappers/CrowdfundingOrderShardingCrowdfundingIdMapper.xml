<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.financetoc.dao.order.CrowdfundingOrderShardingCrowdfundingIdDao">
	<sql id="TABLE">
        crowdfunding_order_crowdfundingid_sharding
    </sql>
	<sql id="SHARDING_TABLE">
        crowdfunding_order_crowdfundingid_sharding_${sharding}
    </sql>
	<sql id="FIELDS">
        `id`,`code`,`user_id`,`crowdfunding_id`,`amount`,`comment`,`pay_status`,
        `ctime`,`pay_time`,`valid`,`ip`,`os_type`,`channel`,`from`,`self_tag`,`user_third_id`,
		`user_third_type`,`anonymous`,`activity_id`,`single_refund_flag`
    </sql>

	<select id="getByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		FORCE index(idx_cfid_userid)
		WHERE `crowdfunding_id`=#{crowdfundingId}
		AND `user_id` IN
		<foreach collection="userIds" item="userId" open="(" close=")" separator=",">
			#{userId}
		</foreach>
		AND `pay_status`=1
		AND `valid`=1
		ORDER BY `id` DESC
	</select>

	<select id="getById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE `crowdfunding_id`=#{crowdfundingId} and `id`=#{id};
	</select>

	<select id="getAllPayByCrowdfundingIdsAndUserIdsWithSuffixTableName" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="SHARDING_TABLE"/>
		WHERE id IN
		<foreach collection="ids" open="(" close=")" item="item" separator=",">
			#{item}
		</foreach>
		AND `pay_status`=1
	</select>
</mapper>
