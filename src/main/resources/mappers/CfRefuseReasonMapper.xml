<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.financetoc.dao.refund.CfRefuseReasonMsgDao">

    <sql id="table_name">
        `cf_refuse_reason_msg`
    </sql>

    <sql id="insert_fields">
        `info_uuid`,
        `type`,
        `reason_ids`,
        `item_ids`,
        `item_reason`
    </sql>

    <sql id="select_fields">
        `id`,
        `info_uuid`,
        `type`,
        `reason_ids`,
        `item_ids`,
        `item_reason`,
        `suggest_modify`,
        `create_time`
    </sql>

    <select id="selectByInfoUuidAndType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonMsg">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `info_uuid`=#{infoUuid} AND `type`=#{type}
        AND `is_delete`=0 AND `disable`=0
        ORDER BY `create_time` DESC
        limit 1
    </select>
</mapper>