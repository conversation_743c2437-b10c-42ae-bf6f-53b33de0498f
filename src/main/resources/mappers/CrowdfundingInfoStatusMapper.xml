<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.financetoc.dao.crowdfunding.CrowdfundingInfoStatusDao">
	<sql id="tableName">
		crowdfunding_info_status
	</sql>
	
	<sql id="queryFields">
		`id`,
		`case_id` as caseId,
		`info_uuid` as infoUuid,
		`type`,
		`status`,
		`date_created` as dateCreated,
		`last_modified` as lastModified
	</sql>

	<insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus" keyProperty="id" useGeneratedKeys="true">
		INSERT INTO <include refid="tableName" />
	    	(`case_id`,`info_uuid`,`type`,`status`)
	    VALUES
	    	(#{caseId},#{infoUuid},#{type},#{status})
	</insert>

	<update id="updateByType">
		UPDATE <include refid="tableName"/>
		SET
			`status`=#{status}
		WHERE
			`info_uuid`=#{infoUuid}
		AND
			`type`=#{type}
		LIMIT 1
	</update>

	<update id="update">
		UPDATE <include refid="tableName"/>
		SET
		`status`=#{status}
		WHERE
		`info_uuid`=#{infoUuid}
		LIMIT 4
	</update>

	<select id="getByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus">
		SELECT <include refid="queryFields" />
		FROM <include refid="tableName" />
		WHERE info_uuid=#{infoUuid}
	</select>

	<select id="getByInfoUuidAndType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus">
		SELECT <include refid="queryFields"/>
		FROM <include refid="tableName"/>
		WHERE `info_uuid`=#{infoUuid}
			AND
			  `type`=#{type}
		LIMIT 1
	</select>
</mapper>