<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.financetoc.dao.crowdfunding.CfFirstApproveMaterialDao">
	<sql id="TABLE">
		cf_first_approve_material
	</sql>

	<sql id="QUERY_FIELDS">
		`id`, `user_id`, `date`, `ip`, `info_id`, `info_uuid`, `self_real_name`,
		`self_crypto_idcard`, `patient_real_name`, `patient_crypto_idcard`, `user_relation_type`,
		`image_url`, `status`, `create_time`, `update_time`, `patient_has_idcard`, `target_amount_desc`, `reject_reason_type`,
		`reject_message`,`poverty`,
		`poverty_image_url`,
		`patient_born_card`,`patient_id_type`
	</sql>

	<select id="getByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial">
		select
		<include refid="QUERY_FIELDS"/>
		from
		<include refid="TABLE"/>
		where `info_uuid`=#{infoUuid} and `is_delete`=0
	</select>

	<select id="getByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial">
		select
		<include refid="QUERY_FIELDS"/>
		from
		<include refid="TABLE"/>
		where `user_id`=#{userId} and info_id != 0 and `is_delete`=0 order by create_time DESC
	</select>

	<update id="updateUserIdByIds">
		update
		<include refid="TABLE"/>
		set user_id = #{userId}
		where id in
		<foreach collection="ids" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</update>
</mapper>