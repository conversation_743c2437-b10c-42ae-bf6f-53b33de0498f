<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.financetoc.dao.order.CrowdfundingPayRecordShardingPayUidDao">
    <sql id="TABLE">
        crowdfunding_pay_record_payuid_sharding
    </sql>
    <sql id="FIELDS">
        `id`,`pay_uid`,`crowdfunding_order_id`
    </sql>
    <sql id="INSERT_FIELDS">
        `pay_uid`,`crowdfunding_order_id`
    </sql>

    <insert id="addPayRecord" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord">
        INSERT INTO
        <include refid="TABLE"/>
        (<include refid="INSERT_FIELDS"/>)
        VALUES(#{payUid},#{crowdfundingOrderId});
    </insert>

    <select id="getPayRecordShardingModelByPayUid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecordShardingModel">
        SELECT
        <include refid="FIELDS"/>
        FROM
        <include refid="TABLE"/>
        WHERE `pay_uid`=#{payUid}
    </select>
</mapper>
