<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.financetoc.dao.order.GoodsOrderExtDao">
	<sql id="tableName">
		goods_order_ext
	</sql>

	<sql id="fields">
		`id` as id,
		`info_uuid` as infoUuid,
		`order_id` as orderId,
		`gear_id` as gearId,
		`province` as province,
		`city` as city,
		`county` as county,
		`goods_count` as goodsCount,
		`shipping_code` as shippingCode,
		`shipping_company` as shippingCompany,
		`order_status` as orderStatus,
		`date_created` as dateCreate,
		`last_modified` as lastModified,
		`encrypt_address`,
		`encrypt_name`,
		`encrypt_mobile`,
		`encrypt_email`
	</sql>

	<insert id="save" parameterType="com.shuidihuzhu.cf.model.goods.GoodsOrderExt" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO
		<include refid="tableName" />
		(`info_uuid`,`order_id`,`gear_id`,`province`,`city`,`county`, `goods_count`,
		`encrypt_address`,
		`encrypt_name`,
		`encrypt_mobile`,
		`encrypt_email`)
		VALUES
		(#{infoUuid},#{orderId},#{gearId},#{province},#{city},#{county}, #{goodsCount},
		#{encryptAddress},#{encryptName},#{encryptMobile},#{encryptEmail})
	</insert>

	<select id="get" resultType="com.shuidihuzhu.cf.model.goods.GoodsOrderExt">
		SELECT <include refid="fields"/>
		FROM <include refid="tableName"/>
		WHERE `order_id`=#{orderId}
		LIMIT 1
	</select>

	<update id="updateSuccessPayStatus">
		UPDATE <include refid="tableName"/>
		SET `pay_status`=1
		WHERE `order_id` = #{orderId}
	</update>
</mapper>