<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.financetoc.dao.order.CfContributeOrderDao">

    <sql id="table_name">cf_contribute_order</sql>

    <insert id="addContributeOrder" keyColumn="id" keyProperty="id"
            parameterType="com.shuidihuzhu.cf.model.contribute.CfContributeOrder"
            useGeneratedKeys="true">
        insert into
        <include refid="table_name"/>
        (`case_id`, `user_id`, `anonymous`, `pre_pay_amount`, `pay_uid`, `contribute_channel`, `contribute_source_id`,
         `pay_platform`, `ip`, `os_type`, `self_tag`, `user_third_type`, `product_name`, client_id)
        values (#{caseId}, #{userId}, #{anonymous}, #{prePayAmount}, #{payUid}, #{contributeChannel}, #{contributeSourceId},
         #{payPlatform}, #{ip}, #{osType}, #{selfTag}, #{userThirdType}, #{productName}, #{clientId})
    </insert>

    <update id="updatePaySuccess">
        update
        <include refid="table_name"/>
        set third_pay_uid = #{thirdPayUid},
        callback_time = #{callbackTime},
        real_pay_amount = #{realPayAmount},
        order_status = #{orderStatus},
        fee_amount = #{feeAmount}
        where pay_uid = #{payUid}
    </update>

    <select id = "selectByPayUids" resultType="com.shuidihuzhu.cf.model.contribute.CfContributeOrder">
        select *
        from <include refid="table_name"/>
        where pay_uid in
        <foreach collection="payUids" item="payUid" open="(" close=")" separator=",">
            #{payUid}
        </foreach>
        and is_delete = 0
    </select>

    <select id = "selectByUserIds" resultType="com.shuidihuzhu.cf.model.contribute.CfContributeOrder">
        select *
        from <include refid="table_name"/>
        where user_id in
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
        <if test="statusList != null and statusList.size() > 0">
            and order_status in
            <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        and is_delete = 0
    </select>

    <update id = "updateUserByIds">
        update
        <include refid="table_name"/>
        set user_id = #{toUserId}
        where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>

    </update>

</mapper>