<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.financetoc.dao.order.CrowdfundingOrderShardingIdDao">
	<sql id="TABLE">
        crowdfunding_order_id_sharding
    </sql>
	<sql id="FIELDS">
        `id`,`crowdfunding_id`
    </sql>
	<sql id="INSERT_FIELDS">
        `id`,`crowdfunding_id`
    </sql>

	<insert id="addCrowdfundingOrderShardingModel" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		INSERT INTO
		<include refid="TABLE"/>
		(<include refid="INSERT_FIELDS"/>)
		VALUES(#{id},#{crowdfundingId});
	</insert>


	<select id="getCrowdfundingIdById" resultType="java.lang.Long">
		SELECT
		crowdfunding_id
		FROM
		<include refid="TABLE"/>
		WHERE `id`=#{id};
	</select>
</mapper>
