<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.financetoc.dao.order.CrowdfundingOrderShardingUserIdDao">
	<sql id="TABLE">
        crowdfunding_order_userid_sharding
    </sql>
	<sql id="SHARDING_TABLE">
        crowdfunding_order_userid_sharding_${sharding}
    </sql>
	<sql id="FIELDS">
		`id`, `crowdfunding_id`, `user_id`
    </sql>
	<sql id="INSERT_FIELDS">
        `user_id`,`crowdfunding_id`, `id`
    </sql>

	<insert id="addCrowdfundingOrderShardingModelBatch">
		INSERT IGNORE INTO
		<include refid="SHARDING_TABLE"/>
		(<include refid="INSERT_FIELDS"/>)
		VALUES
		<foreach collection="orders" item="order" separator=",">
			(#{order.userId},#{order.crowdfundingId},#{order.id})
		</foreach>
	</insert>


	<select id="getCrowdfundingOrderShardingModelListByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrderShardingModel">
		SELECT
			<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE user_id = #{userId}
	</select>

	<select id="getCrowdfundingOrderShardingModelListByUserIdsWithTableSuffixName" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrderShardingModel">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="SHARDING_TABLE"/>
		WHERE
		`user_id` IN
		<foreach collection="userIds" item="userId" open="(" close=")" separator=",">
			#{userId}
		</foreach>
	</select>

	<delete id="deleteByUserIdWithOrderIds">
		delete from <include refid="TABLE"/> where user_id=#{userId} and  id in
		<foreach collection="orderIds" item="orderId" separator="," open="(" close=")">
			#{orderId}
		</foreach>
	</delete>

	<select id="getExistIdByIds" resultType="java.lang.Long">
		select id from <include refid="SHARDING_TABLE"/>
		where id in
		<foreach collection="orderIds" item="orderId" separator="," open="(" close=")">
			#{orderId}
		</foreach>
	</select>

</mapper>
