<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.financetoc.dao.crowdfunding.CrowdfundingAttachmentDao">
	<sql id="tableName">
		crowdfunding_attachment
	</sql>
	
	<sql id="QUERY_FIELDS">
		id,
		parent_id,
		url,
		type,
		sequence,
		create_time,
		last_modified,
		is_delete
	</sql>

	<select id="getFundingAttachment" parameterType="int" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment">
		SELECT id,url, type, sequence
		FROM
		<include refid="tableName"/>
		WHERE
		parent_id = #{parentId}
		AND `is_delete` = 0
	</select>

	<insert id="add" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO
		<include refid="tableName"/>
		(parent_id,url,type,sequence) VALUES
		<foreach collection="list" item="element" index="index" separator=",">
			(
			#{element.parentId},
			#{element.url},
			#{element.type, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum},
			#{element.sequence}
			)
		</foreach>
	</insert>
	

	<update id="deleteByParentIdAndType" parameterType="int">
		UPDATE <include refid="tableName"/>
		SET `is_delete`=1
		WHERE parent_id = #{parentId}
		AND type in 
		<foreach collection="typeList" item="type" index="index" open="(" separator="," close=")">
			#{type, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum}
		</foreach>
	</update>
</mapper>