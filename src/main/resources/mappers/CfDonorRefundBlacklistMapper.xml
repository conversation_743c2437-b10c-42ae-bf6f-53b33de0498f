<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.financetoc.dao.refund.CfDonorRefundBlacklistDao">

    <sql id="TABLE_NAME">
       `cf_donor_refund_blacklist`
   </sql>


    <select id="getByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.pay.CfDonorRefundBlacklist">
        SELECT *
        FROM
        <include refid="TABLE_NAME"/>
        WHERE `is_delete` = 0
        AND `user_id` = #{userId}
        order by id desc
        limit 1
    </select>
</mapper>