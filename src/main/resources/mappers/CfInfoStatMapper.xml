<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.financetoc.dao.crowdfunding.CfInfoStatDao">

	<sql id="TABLE">
        cf_info_stat
    </sql>

	<sql id="FIELDS">
		id as id,
		share_count as shareCount,
		donation_count as donationCount,
		verify_user_count as verifyUserCount,
		comment_count as commentCount,
		amount as amount,
		verify_friend_count as verifyFriendCount,
		verify_hospital_count as verifyHospitalCount,
		blessing_count as blessingCount,
		refund_count as refundCount,
		donator_count as donatorCount
	</sql>

	<update id="addAmount">
		UPDATE
		<include refid="TABLE"/>
		SET `amount`= `amount` + #{amount},`donation_count`= `donation_count` + 1
		WHERE `id`=#{id}
	</update>
</mapper>
