<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.financetoc.dao.pa.CfCaseDepositAccountDao">
	
	<sql id="table_name">
		`cf_case_deposit_account`
	</sql>
	
	<select id="getByCaseId" resultType="com.shuidihuzhu.cf.finance.model.deposit.CfCaseDepositAccount">
		SELECT * FROM
		<include refid="table_name"/>
		WHERE `case_id`= #{caseId}
	</select>

</mapper>