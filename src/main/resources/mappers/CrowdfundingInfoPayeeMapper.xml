<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.financetoc.dao.payee.CrowdfundingInfoPayeeDao">

	<sql id="TABLE_NAME">
		crowdfunding_info_payee
	</sql>

	<sql id="QUERY_FIELDS">
		`id`,
		`info_uuid` as infoUuid,
		`relation_type` as relationType,
		`name`,
		`id_card` as idCard,
		`id_type` as idType,
		`mobile`,
		`bank_name` as bankName,
		`bank_branch_name` as bankBranchName,
		`bank_card` as bankCard,
		`date_created` as dateCreated,
		`last_modified` as lastModified,
		`case_id` as caseId,
		emergency,
		emergency_phone,
		relatives_type,
		face_id_result
	</sql>
	
	<insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee" keyProperty="id" useGeneratedKeys="true">
		INSERT INTO <include refid="TABLE_NAME" />
	    	(`info_uuid`,`relation_type`,`name`,`id_card`,`id_type`,`mobile`,`bank_name`,`bank_branch_name`,`bank_card`,`case_id`
	    	<if test="emergency != '' and emergency !=null">
				,emergency
			</if>
			<if test="emergencyPhone != '' and emergencyPhone !=null">
				,emergency_phone
			</if>
			,relatives_type,face_id_result
	    	)
	    VALUES 
	    	(#{infoUuid},#{relationType},#{name},#{idCard},#{idType},#{mobile},#{bankName},#{bankBranchName},#{bankCard},#{caseId}
			<if test="emergency != '' and emergency !=null">
				,#{emergency}
			</if>
			<if test="emergencyPhone != '' and emergencyPhone !=null">
				,#{emergencyPhone}
			</if>
			,#{relativesType},#{faceIdResult}
	    	)
	</insert>
	
	<update id="update">
		UPDATE <include refid="TABLE_NAME" />
		SET `relation_type`=#{relationType},`name`=#{name},`id_card`=#{idCard},
		`id_type`=#{idType},`mobile`=#{mobile},`bank_name`=#{bankName},
		`bank_branch_name`=#{bankBranchName},`bank_card`=#{bankCard},relatives_type=#{relativesType}
		<if test="emergency != '' and emergency !=null">
			,emergency=#{emergency}
		</if>
		<if test="emergencyPhone != '' and emergencyPhone !=null">
			,emergency_phone=#{emergencyPhone}
		</if>
		,face_id_result = #{faceIdResult}
		WHERE id=#{id} AND `info_uuid`=#{infoUuid}
	</update>
	
	<select id="getByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee">
		SELECT <include refid="QUERY_FIELDS" />
		FROM <include refid="TABLE_NAME" />
		WHERE `info_uuid`=#{infoUuid}
	</select>
</mapper>