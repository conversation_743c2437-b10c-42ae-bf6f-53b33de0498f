<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.financetoc.dao.crowdfunding.CfOperatingRecordDao">

	<sql id="TABLE">
        cf_operating_record
    </sql>

    <sql id="select_fields">
        `id`,`info_uuid`,`type`,`role`,`user_id`,`user_name`,`comment`,`date_created`,`last_modified`
    </sql>

	<sql id="insert">
	  (`info_uuid`,`type`,`role`,`user_id`,`user_name`,`comment`,`refuse_count`)
	</sql>

    <insert id="save" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO
		<include refid="TABLE"/>
		(`info_uuid`,`type`,`role`,`user_id`,
		`user_name`,`comment`,`refuse_count`, `biz_id`, `financial_comment`)
		VALUES
		(#{infoUuid},#{type},#{role},#{userId},
		#{userName},#{comment},#{refuseCount}, #{bizId}, #{financialComment})
	</insert>
	<update id="updateResion">
		update
		<include refid="TABLE"/>
		set
		`financial_comment` = #{financialComment}
		where
		`id` = #{id}
	</update>


</mapper>