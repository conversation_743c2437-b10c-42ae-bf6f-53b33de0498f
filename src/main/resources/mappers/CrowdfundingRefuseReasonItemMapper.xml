<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.financetoc.biz.payee.CrowdFundingRefuseReasonItemDao">

    <sql id="table_name">
		cf_refuse_reason_item
	</sql>


    <select id="getListByIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingRefuseReasonItem">
        SELECT id, content, type, pro_type
        FROM <include refid="table_name" />
        WHERE `id` IN
        <foreach item="item" index="index" collection="ids"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getAll" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingRefuseReasonItem">
        SELECT id, content, type, pro_type
        FROM <include refid="table_name" />
        WHERE id > 0
        LIMIT #{start}, #{size}
    </select>

    <select id="getListByTypes" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingRefuseReasonItem">
        SELECT *
        FROM <include refid="table_name" />
        WHERE `type` IN
        <foreach item="item" index="index" collection="typeIds"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id = "getByContent" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingRefuseReasonItem">
        SELECT *
        FROM <include refid="table_name" />
        WHERE
        `content` = #{content}
        LIMIT 1
    </select>


</mapper>