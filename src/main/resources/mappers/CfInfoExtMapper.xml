<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.financetoc.dao.crowdfunding.CfInfoExtDao">

    <sql id="TABLE">
            cf_info_ext
    </sql>

    <sql id="FIELDS">
            id                     as id,
            info_uuid              as infoUuid,
            from_type              as fromType,
            from_detail            as fromDetail,
            self_tag               as selfTag,
            product_name           as productName,
            finish_status          as finishStatus,
            refund_status          as refundStatus,
            transfer_status        as transferStatus,
            refund_end_time        as refundEndTime,
            pay_type               as payType,
            user_third_type        as userThirdType,
            date_created           as dateCreated,
            last_modified          as lastModified,
            crypto_register_mobile as cryptoRegisterMobile,
            first_approve_status   as firstApproveStatus,
            first_approve_time     as firstApproveTime,
            volunteer_unique_code  as volunteerUniqueCode,
            client_ip              as clientIp,
            primary_channel        as primaryChannel,
            need_case_list         as needCaseList,
            cf_version             as cfVersion,
            pre_id                 as preId,
            bd_followed            as bdFollowed,
            finish_str             as finishStr,
            case_id                as caseId,
            no_handling_fee        as noHandlingFee

    </sql>


    <select id="getByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt">
        SELECT
        <include refid="FIELDS"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        `info_uuid` = #{infoUuid}
        LIMIT 1
    </select>

    <update id="updateFinishStatus">
        UPDATE
        <include refid="TABLE"/>
        SET
        `finish_status`=#{finishStatus}
        WHERE
        `info_uuid`=#{infoUuid}
        LIMIT 1
    </update>

    <select id="getByCaseIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt">
        SELECT
        <include refid="FIELDS"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        case_id IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>