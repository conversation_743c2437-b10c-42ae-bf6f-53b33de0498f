<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.financetoc.dao.GoodsGearDao">
	<sql id="tableName">
		goods_gear
	</sql>
	
	<select id="getById" resultType="com.shuidihuzhu.cf.model.goods.GoodsGear">
		SELECT *
		FROM <include refid="tableName" />
		WHERE `id`=#{id}
	</select>

	<update id="updateNum">
		UPDATE <include refid="tableName" />
		SET `num`=`num`+ #{goodsCount}
		WHERE <![CDATA[ `id`=#{id} AND `num`+ #{goodsCount} <=`target_num` ]]>
	</update>
</mapper>