<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.financetoc.dao.refund.CfDonorRefundRecordDao">

    <sql id="TABLE_NAME">
       `cf_donor_refund_record`
   </sql>

    <insert id="add">
        INSERT INTO
        <include refid="TABLE_NAME"/>
        (`user_id`,`valid_time`)
        VALUES (#{userId},#{validTime})
    </insert>

    <select id="getByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.pay.CfDonorRefundRecord">
        SELECT
        *
        FROM
        <include refid="TABLE_NAME"/>
        WHERE `is_delete` = 0
        AND `user_id` = #{userId}
        order by id desc
        limit 1
    </select>
</mapper>