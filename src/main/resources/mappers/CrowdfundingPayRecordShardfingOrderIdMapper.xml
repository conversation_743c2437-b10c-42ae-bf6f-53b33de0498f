<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.financetoc.dao.order.CrowdfundingPayRecordShardingOrderIdDao">
    <sql id="TABLE">
        crowdfunding_pay_record_orderid_sharding
    </sql>
    <sql id="SHARDING_TABLE">
        crowdfunding_pay_record_orderid_sharding_${sharding}
    </sql>
    <sql id="FIELDS">
        `id`,`pay_uid`,`crowdfunding_order_id`,`pre_pay_amount`,`real_pay_amount`,`pay_platform`,`pay_status`,`ctime`,`callback_time`,`valid`,
        `refund_status`,`refund_time`,`refund_amount`,`refund_reason`,`last_modified`
    </sql>
    <sql id="INSERT_FIELDS">
        `crowdfunding_order_id`,`pay_uid`,`pre_pay_amount`,`real_pay_amount`,`pay_platform`,`pay_status`,`ctime`,`valid`
    </sql>

    <insert id="addPayRecord" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord">
        INSERT INTO
        <include refid="TABLE"/>
        (<include refid="INSERT_FIELDS"/>)
        VALUES(#{crowdfundingOrderId},#{payUid},#{prePayAmount},#{realPayAmount},#{payPlatform},#{payStatus},#{ctime},#{valid});
    </insert>


    <update id="updatePayStatus">
        UPDATE
        <include refid="TABLE"/>
        SET `pay_status`=#{payStatus},`callback_time`=#{callbackTime},`real_pay_amount`=#{realPayAmount}
        WHERE crowdfunding_order_id=#{crowdfundingOrderId} and `pay_uid`=#{payUid};
    </update>

    <select id="getByOrderId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord">
        SELECT *
        FROM <include refid="TABLE"/>
        WHERE crowdfunding_order_id=#{crowdfundingOrderId}
        limit 1;
    </select>
    <select id="getByOrderIdsAndPayUids" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE crowdfunding_order_id in <foreach collection="orderIds" item="orderId" open="(" separator="," close=")" >#{orderId}</foreach>
        and `pay_uid` in <foreach collection="payUids" item="payUid" open="(" separator="," close=")" >#{payUid}</foreach>
    </select>
</mapper>
