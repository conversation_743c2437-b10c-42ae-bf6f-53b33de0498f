<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.financetoc.dao.crowdfunding.CrowdfundingAuthorDao">
	<sql id="tableName">
		`crowdfunding_author`
	</sql>

	<sql id="selectFields">
		`id`,`crowdfunding_id`,`name`,`crypto_id_card`,`crypto_phone`,`id_type`,`mail`,`qq`,`health_insurance`,`commercial_insurance`,
		`birth_year`, `birth_month`, `birth_day`, `province_code`, `city_code`, `district_code`, `gender`, `relation`,
		`create_time`,`last_modified`,face_id_result
	</sql>

	<select id="get" parameterType="int" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor">
		SELECT
		<include refid="selectFields"/>
		FROM
		<include refid="tableName"/>
		WHERE
		crowdfunding_id=#{crowdfundingId}
	</select>
</mapper>
